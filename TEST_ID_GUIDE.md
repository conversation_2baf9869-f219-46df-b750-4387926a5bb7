# Test ID Guide

This guide explains how data-testid attributes are managed across different environments in our application.

## Overview

We use a Babel plugin that automatically adds `data-testid` attributes to our React components. These are useful for:

- Automated testing (Cypress, Testing Library, etc.)
- Debugging
- Identifying elements in the DOM

However, we don't want these attributes to appear in production builds because:
- It can expose implementation details
- It increases the size of the HTML
- It's unnecessary in production

## How Test IDs Are Controlled

Test IDs are controlled using the `VITE_ENABLE_TEST_IDS` environment variable:

- When `VITE_ENABLE_TEST_IDS=true`, test IDs are added to components
- When `VITE_ENABLE_TEST_IDS=false`, test IDs are not added

## Environment Configuration

We've configured our environment files as follows:

1. **Local Development** (`.env`):
   - `VITE_ENABLE_TEST_IDS=true`
   - You should see test IDs when running locally with `npm run dev`

2. **Development/Staging** (`.env.dev`):
   - `VITE_ENABLE_TEST_IDS=true`
   - Test IDs will appear in the dev/staging environment

3. **Production** (`.env.prod`):
   - `VITE_ENABLE_TEST_IDS=false`
   - No test IDs will appear in production builds

## Troubleshooting

If test IDs aren't appearing when they should be, check:

1. That you're using the correct `.env` file for your environment
2. That the Babel plugin is properly configured in `vite.config.ts`
3. Run `npm run debug:env` to check environment variables

## Manually Controlling Test IDs

If you need to override the environment settings, you can:

1. Temporarily modify the `.env` file for your environment
2. Set the environment variable when running the build command:
   ```bash
   VITE_ENABLE_TEST_IDS=true npm run build
   ```

## Example Test ID Format

Our test IDs follow this format:
```
geocore-[componentName]-[elementType]-[optional-identifier]
```

For example:
- `geocore-Button-button`
- `geocore-LoginForm-input-username`

## Adding Test IDs Manually

If you need to add a test ID manually (for special cases), you can:

```jsx
<div data-testid="my-custom-id">...</div>
```

The Babel plugin will recognize existing test IDs and won't modify them. 