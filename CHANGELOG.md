# Changelog

All notable changes to the GeoCore Web project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.4.0] - 2025-07-16

### Added

- **Layer Management Enhancements**:

  - SLD (Styled Layer Descriptor) support for enhanced layer styling
  - Layer type toggle functionality
  - Border customization options for layers
  - Layer color and title display with selection improvements
  - Automatic zoom level determination

- **Workspace Improvements**:

  - New workspace creation modal
  - Enhanced workspace card display showing last edit instead of last visited
  - Dedicated mutation for updating workspace last visited date
  - Workspace records layer synchronization
  - Document title sync with selected workspace

- **UI/UX Enhancements**:

  - Homepage redesign
  - Dashboard workspace UI improvements
  - Restructured form buttons layout
  - Enhanced MultiSelectDropdown with search functionality and improved UI
  - JSON schema titles display
  - Map search functionality and various UI fixes

- **Drawing and Map Features**:

  - Prevention of drawing on basemap with "no data" indicator
  - Removed zoom selection coupling for better user experience
  - Enhanced map studio scroll functionality

- **Upload Workflow**:
  - Reordered upload steps for better user flow

### Fixed

- **Map and Layer Issues**:

  - FeatureInfoAlert panning logic adjustments
  - Layer settings reset on layer change
  - Layer settings heatmap functionality
  - Map studio scroll issues
  - Workspace records layer synchronization

- **UI and Display Fixes**:
  - StudioView EDA report preview display
  - MapChatSidebar scroll area for RTL languages
  - Various UI tweaks and improvements

### Changed

- Enhanced layer styling capabilities with SLD integration
- Improved workspace management workflow
- Better user experience with decoupled zoom and selection features
- Optimized upload process with reordered steps

## [0.3.0] - 2025-07-15

### Added

- **Map Enhancements**:

  - Enhanced feature info alert with pagination and highlighting
  - Improved map controls
  - Added scroll to top button
  - RTL layer selector support

- **Workspace Management**:

  - New RecordsTableView and RecordsTabsView components
  - Improved workspace creation flow with sticky header
  - Schema-based workspace type handling
  - Ability to modify schema based on workspace
  - Schema editor with JSON copy functionality
  - Persistent workspace ID in upload flow

- **Dataset Integration**:

  - Support for adding datasets from studio
  - Enhanced dataset creation and management
  - Improved dataset request handling

- **UI/UX Improvements**:
  - Enhanced Monaco editor options
  - Replaced showMore.svg icon with Fullscreen component
  - Multiple component UI/UX refinements
  - Improved records table search functionality
  - Non-looping loading splash screen
  - Better record details and schema editor

### Fixed

- Map controls functionality
- Dataset creation mutation block closure
- Workspace creation navigation
- Various UI/UX improvements across multiple components

### Changed

- Refactored MapPage component
- Enhanced layout in CreateWorkspace component
- Optimized workspace type handling
- Updated dependencies and reduced lockfile size

## [0.2.0] - 2025-06-01

### Added

- **Heatmap Layer Implementation**:

  - Property-based heatmap rendering with intensity and gradient controls
  - Toggle between point and heatmap views
  - Validation for heatmap weighting
  - WMS integration with retry logic

- **Map Interface Enhancements**:

  - Dual view modes (Details and Exploratory)
  - Redesigned drawing toolbar with shape and zoom controls
  - Base layer switcher with icons
  - Resizable/collapsible side panels using `react-resizable-panels`
  - RTL language support

- **Record Navigation**:

  - Previous/Next buttons with pagination support
  - Position indicators and current record tracking
  - Smart detail panel behavior

- **Thumbnail Management**:

  - Thumbnail capture system with manual update option

- **AI Upgrades**:

  - Multi-layer selection for conversations
  - Chat context reset support
  - Auto-resizing textarea for messages
  - Scrollable, improved message history

- **Dashboard & Analytics**:

  - Sorting workspaces by creation date and last visit
  - Dynamic page size controls
  - Debounced search inputs
  - Microsoft Clarity integration

- **EDA Reports**:

  - Support for YData and Sweetviz sources
  - Source switcher and improved UI layout

- **Developer Experience**:
  - Auto test ID generation using Babel plugin
  - Environment-based test ID configuration
  - Schema diagrams using React Flow

### Changed

- Improved toast notifications with RTL support
- Refined workspace and layer sidebar components
- Visual indicators for selected toolbar states
- Enhanced error handling and loading states

### Fixed

- **Map & Layers**:

  - Feature info component reset logic
  - WMS feature race condition prevention
  - Restored layer selection on click
  - Measurement overlay alignment fixes

- **Forms & Data**:

  - Preserved form data during loading
  - Upload workflow step navigation fixes
  - Prevented modal field errors
  - Automatic table refresh on record addition

- **Internationalization**:
  - RTL adjustments across components
  - Improved language switcher reliability

### Performance

- Optimized component rendering and state updates
- More efficient WMS request management
- Debounced search inputs to reduce API usage
- Improved organization data fetching

### Security & Configuration

- Updated redirect URIs to `app.geocore.ai`
- Streamlined environment-specific configurations
- Enhanced login and authentication flow

### Accessibility & Responsiveness

- Improved keyboard navigation support
- Enhanced screen reader compatibility
- Better responsive layout across breakpoints
- Optimized image sizing in org switcher

---

## [0.1.1] - 2025-04-20

### Added

- Merged main branch into develop with resolved conflicts (#95)

### Fixed

- Fixed layer properties by removing showOnly field from layer properties (#93)
- Updated workspace pagination to 21 items per page (#92)

### Documentation

- Added CHANGELOG.md file (#93)

This release includes important bug fixes for layer properties and pagination, along with documentation improvements through the addition of a changelog file. The main branch has been successfully merged into develop to ensure all production fixes are available in the development environment.

## [0.1.0] - 2025-04-17

### Added

- **Workspace Management**:
  - New workspace creation workflow with custom field definition
  - Two-step guided creation process
  - Field type support (Boolean, String, Number, Date)
  - Schema validation and preview capabilities
- **Organization System**:
  - Complete organization settings with Redux integration
  - Team management with phone number field
  - Role-based permission system
  - Workspace access controls
- **Forms**:
  - React JSON Schema Form integration
  - Custom form widgets and templates
  - Hijri calendar support
- **Mapping**:
  - Enhanced feature info alerts
  - Improved layer management
  - Dedicated overlay components
- **UI Components**:
  - New reusable component library
  - Standardized workspace cards
  - Consistent styling system

### Changed

- **Architecture**:
  - Standardized workspace method names
  - Improved component reusability
  - Reorganized workspace creation flow
- **Dependencies**:
  - Updated package.json
  - Modified vite.config.ts

### Fixed

- **UI Issues**:
  - Image preview constraints
  - Layer name box sizing
- **Form Handling**:
  - TextWidget type handling
  - UpDownWidget submission
- **API**:
  - GraphQL unauthorized errors
  - Mutation optimizations
- **Performance**:
  - Debounced search implementation
  - Efficient data fetching

## [0.0.0] - 2024-10-23

### Added

- **Core mapping functionality**:
  - Interactive map with tooltips
  - Multi-layer selection
  - WMS layer handling
- **Workspace system**:
  - Multi-step form creation
  - Summary fields
  - Record management
- **UI Components**:
  - Draggable legend
  - Base layer switcher
  - Data tables with map interaction
- **Internationalization**:
  - Language switcher
  - Multilingual font system

### Changed

- Map data flow architecture
- Font system (Almarai, IBM Plex)
- GraphQL query structure
- Base layer switcher design

### Fixed

- Docker configuration
- Map API key handling
- Performance bottlenecks
- Spatial data bugs

### Infrastructure

- CI/CD pipeline (cloudbuild.yaml)
- Environment configuration
- Nginx setup
- Build system files

[0.4.0]: https://github.com/SA-GeoTech/geocore-web/compare/v0.3.0...v0.4.0
[0.3.0]: https://github.com/SA-GeoTech/geocore-web/compare/v0.2.0...v0.3.0
[0.2.0]: https://github.com/SA-GeoTech/geocore-web/compare/v0.1.1...v0.2.0
[0.1.1]: https://github.com/SA-GeoTech/geocore-web/compare/v0.1.0...v0.1.1
[0.1.0]: https://github.com/SA-GeoTech/geocore-web/compare/v0.0.0...v0.1.0
[0.0.0]: https://github.com/SA-GeoTech/geocore-web/releases/tag/v0.0.0
