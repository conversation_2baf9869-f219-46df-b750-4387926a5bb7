steps:
  - name: ubuntu
    args:
      - cp
      - ".env.${_IMAGE_TAG}"
      - .env
  - name: ubuntu
    args:
      - rm
      - '-rf'
      - .env.local
      - .env.dev
      - .env.test
      - .env.staging
      - .env.prod
      - .env.example
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - "-t"
      - "${_DOCKER_REPO_BASE_URL}/${_IMAGE_NAME}:${_IMAGE_TAG}"
      - .
    id: build image
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - "${_DOCKER_REPO_BASE_URL}/${_IMAGE_NAME}:${_IMAGE_TAG}"
    id: push image
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - run
      - deploy
      - "${_RUN_INSTANCE_NAME}"
      - "--image"
      - "${_DOCKER_REPO_BASE_URL}/${_IMAGE_NAME}:${_IMAGE_TAG}"
      - "--region"
      - "${_REGION}"
    id: deploy to Cloud Run
    entrypoint: gcloud
timeout: 1800s
options:
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _REGION: me-central2
  _IMAGE_NAME: "geocore-web"
  _DOCKER_REPO_BASE_URL: "me-central2-docker.pkg.dev/geotech-sd-infra/docker"
  _IMAGE_TAG: "dev"
  _RUN_INSTANCE_NAME: geocore-web
