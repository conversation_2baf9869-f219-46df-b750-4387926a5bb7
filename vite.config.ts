import path from 'path'
import react from '@vitejs/plugin-react'
import { defineConfig, loadEnv } from 'vite'

// This function loads environment variables from .env files
export default defineConfig(({ mode }) => {
  // Load env file based on mode (development, production, etc)
  const env = loadEnv(mode, process.cwd(), '');

  return {
    // Set mode from the command
    mode,

    // Optimize dependencies
    optimizeDeps: {
      force: true,
      include: ['@rjsf/core', 'react-multi-date-picker'],
    },

    // Configure plugins
    plugins: [
      react({
        babel: {
          plugins: [
            [
              path.resolve(
                __dirname,
                'babel-plugins/babel-plugin-react-auto-test-id.js'
              ),
              {
                // Override default config if needed
                prefix: 'geocore',
                // Enable test IDs based on env var (true by default for development)
                enableInProduction: env.VITE_ENABLE_TEST_IDS === 'true',
              },
            ],
          ],
        },
      }),
    ],

    // Resolve aliases
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        'apollo-upload-client': path.resolve(
          __dirname,
          'node_modules/apollo-upload-client/public/index.js'
        ),
      },
    },

    // Define variables available at build time
    define: {
      // Make environment variables available to the app
      'process.env.NODE_ENV': JSON.stringify(env.NODE_ENV || mode),
      'process.env.VITE_ENABLE_TEST_IDS': JSON.stringify(env.VITE_ENABLE_TEST_IDS),
    },
  };
})
