FROM node:18-alpine as build

RUN apk add --no-cache git

ENV APP_DIR /app

WORKDIR ${APP_DIR}

COPY package.json .
COPY yarn.lock .
COPY .env .

RUN yarn install

COPY . .

RUN yarn build

# Stage 2
FROM nginx:1.21-alpine

COPY --from=build /app/dist /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
