VITE_BASE_URL=https://api.geocore.test.geotech.build

VITE_ACCOUNTS_BASE_URL=https://accounts.test.geotech.build
VITE_AUTHORITY=https://auth.test.geotech.build
VITE_CLIENT_ID=geocore_web
VITE_REDIRECT_URI=https://app.geocore.test.geotech.build/auth/callback
VITE_SCOPE="openid offline"

VITE_GRAPHQL_URL=https://api.geocore.test.geotech.build/graphql
VITE_WMS_BASE_URL=https://api.geocore.test.geotech.build/proxy/geocore/wms?
VITE_GOOGLE_MAPS_API_KEY=AIzaSyD6mrxWN3lH7Y-rJM1pc_-emnmTRb9bdpA

VITE_APP_DEPLOYMENT=DEVELOPMENT
VITE_APP_SENTRY_DSN=https://<EMAIL>/
VITE_CLARITY_SCRIPT_ID=rlx5yigutg

# Disable test IDs in production environment
VITE_ENABLE_TEST_IDS=true
