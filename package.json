{"name": "geocore-app", "private": true, "version": "0.4.0", "type": "module", "scripts": {"dev": "vite --port 7493", "build": "tsc -b && vite build", "lint": "eslint './src/**/*.{ts,tsx}'", "preview": "vite preview", "format": "prettier --write './src/**/*.{ts,tsx}'", "gcp-login": "GOOGLE_APPLICATION_CREDENTIALS=$(pwd)/gcloud-service-account-key.json && npx google-artifactregistry-auth"}, "dependencies": {"@apollo/client": "^3.11.8", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@reduxjs/toolkit": "^2.3.0", "@rjsf/core": "^5.22.2", "@rjsf/mui": "^5.22.2", "@rjsf/utils": "^5.22.2", "@rjsf/validator-ajv8": "^5.22.2", "@sentry/react": "^8.34.0", "@xyflow/react": "^12.6.0", "apollo-upload-client": "^17.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "dagre": "^0.8.5", "graphql": "^16.9.0", "i18next": "^23.16.0", "i18next-browser-languagedetector": "^8.0.0", "immer": "^10.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.453.0", "moment-hijri": "^3.0.0", "oidc-client-ts": "^3.1.0", "ol": "^10.2.1", "react": "^18.3.1", "react-date-object": "^2.1.9", "react-dom": "^18.3.1", "react-i18next": "^15.0.3", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-multi-date-picker": "^4.5.2", "react-oidc-context": "^3.2.0", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.8", "react-router-dom": "^6.27.0", "react-toastify": "^10.0.6", "shadcn-ui": "^0.9.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-react": "^7.26.3", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.11.1", "@types/apollo-upload-client": "^17", "@types/babel__core": "^7", "@types/geojson": "^7946.0.14", "@types/google.maps": "^3.58.1", "@types/i18next": "^13.0.0", "@types/js-cookie": "^3", "@types/moment-hijri": "^2.1.4", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/uuid": "^10", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.9.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "babel-plugin-transform-react-jsx": "^6.24.1", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}