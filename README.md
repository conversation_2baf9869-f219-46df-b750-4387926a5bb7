# GeoCore App

A powerful geospatial data management and visualization platform built with modern web technologies.

## Overview

GeoCore App is an enterprise-grade solution for handling complex geospatial data, offering advanced mapping capabilities, data analysis tools, and collaborative features.

## Key Features

- 🗺️ Interactive Mapping Interface
- 📊 Geospatial Data Analysis
- 🔄 Real-time Collaboration
- 📱 Responsive Design
- 🌐 Multi-language Support
- 🎨 Customizable Themes
- 📈 Data Visualization Tools
- 🔐 Role-based Access Control

## Technology Stack

- **Frontend**

  - React 18
  - TypeScript
  - Tailwind CSS
  - MapLibre GL JS
  - React Query
  - Redux Toolkit

- **Development Tools**
  - Vite
  - ESLint
  - Prettier
  - Husky
  - Jest

## Getting Started

1. Clone the repository:

```bash
git clone https://github.com/your-org/geocore-app.git
```

2. Install dependencies:

```bash
yarn install
```

3. Start development server:

```bash
yarn dev
```

4. Build for production:

```bash
yarn build
```

## Project Structure

```
├── src/
│   ├── api/            # API integration layer
│   ├── assets/         # Static assets and images
│   ├── components/     # Reusable UI components
│   ├── features/       # Feature-based modules
│   ├── hooks/          # Custom React hooks
│   ├── layouts/        # Layout components
│   ├── pages/          # Page components
│   ├── store/          # State management
│   ├── styles/         # Global styles
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Utility functions
├── public/             # Public assets
└── tests/              # Test files
```

## Configuration

The app can be configured through environment variables:

- `VITE_API_URL`: Backend API endpoint
- `VITE_MAPLIBRE_KEY`: MapLibre API key
- `VITE_ENVIRONMENT`: Development/Production environment

## Available Scripts

- `yarn dev`: Start development server
- `yarn build`: Build for production
- `yarn test`: Run tests
- `yarn lint`: Lint code
- `yarn format`: Format code
- `yarn storybook`: Run Storybook

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - see LICENSE file for details

## Support

For support and questions, please open an issue in the repository or contact our support team.

---

Made with ❤️ by the GeoCore Team
