import defaultConfig from './test-id-config.js'

export default function ({ types: t }) {
  return {
    visitor: {
      JSXOpeningElement(path, state) {
        // Get config options
        const userConfig = state.opts || {}
        const config = { ...defaultConfig, ...userConfig }

        // Check for test ID inclusion based on environment variables
        // First check for Vite-specific variable, then fall back to NODE_ENV-based logic
        const viteEnableTestIds = process.env.VITE_ENABLE_TEST_IDS
        
        // Skip if Vite env var is explicitly set to 'false'
        if (viteEnableTestIds === 'false') {
          return
        }
        
        // Fallback - if Vite env var isn't set, use NODE_ENV
        if (viteEnableTestIds === undefined) {
          const nodeEnv = process.env.NODE_ENV
          if (nodeEnv === 'production' && !config.enableInProduction) {
            return
          }
        }

        // Get filename and check if it should be excluded
        const filename = state.file.opts.filename || 'unknown'
        const shouldExclude = config.excludePaths.some((excludePath) =>
          filename.includes(excludePath)
        )

        if (shouldExclude) return

        // Skip if already has data-testid
        const hasTestId = path.node.attributes.some(
          (attr) => t.isJSXAttribute(attr) && attr.name.name === 'data-testid'
        )

        if (hasTestId) return

        // Skip certain elements that don't need test IDs
        const elementName = path.node.name.name || 'element'
        if (config.skipElements.includes(elementName)) {
          return
        }

        // Get component name from filename
        let componentName = filename.split('/').pop().split('.')[0]

        // Apply component name mapping if exists
        if (config.componentNameMap[componentName]) {
          componentName = config.componentNameMap[componentName]
        }

        // Get a more specific identifier based on props
        let identifier = ''

        // Check for key props that could help identify the element
        const idAttr = path.node.attributes.find(
          (attr) =>
            t.isJSXAttribute(attr) &&
            (attr.name.name === 'id' || attr.name.name === 'name')
        )

        if (idAttr && idAttr.value && t.isStringLiteral(idAttr.value)) {
          identifier = `-${idAttr.value.value}`
        }

        // Create a path-based test ID with prefix
        const testId = `${config.prefix}-${componentName}-${elementName}${identifier}`

        // Add the data-testid attribute
        path.node.attributes.push(
          t.jsxAttribute(
            t.jsxIdentifier('data-testid'),
            t.stringLiteral(testId)
          )
        )
      },
    },
  }
}
