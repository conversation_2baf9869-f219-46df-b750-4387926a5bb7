import { StepNavigation } from "@/components/MultiStepForm/StepNavigation";
import { FormSteps } from "@/components/MultiStepForm/steps/FormSteps";
import { XIcon } from "lucide-react";
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

export const CreateWorkspace = () => {
    const { t } = useTranslation();
    const { navigateWithOrg } = useOrganizationManager();
    const [searchParams] = useSearchParams();

    // Get workspaceId from URL parameters
    const workspaceId = searchParams.get('workspaceId');
    const isAddingToExistingWorkspace = !!workspaceId;

    const handleClose = () => {
        // If we're adding to an existing workspace, go back to that workspace
        if (isAddingToExistingWorkspace) {
            navigateWithOrg(`/${Routes.map}/${workspaceId}?type=${WorkspaceMethods.UPLOAD_FILE}`);
        } else {
            // Otherwise, go to the marketplace
            navigateWithOrg(`/${Routes.marketSpaces}`);
        }
    };

    // Determine the title based on whether we're adding to an existing workspace
    const pageTitle = isAddingToExistingWorkspace
        ? t('workspace.addDataset.title')
        : t('workspace.creation.title');

    return (
        <div className="w-full h-full flex flex-col">
            {/* Fixed header */}
            <div className="flex items-center justify-between border-b border-gray-200 p-6 bg-white sticky top-0 z-10">
                <h1 className="text-lg font-semibold text-gray-800">{pageTitle}</h1>
                <button onClick={handleClose} className="text-gray-500 hover:text-gray-700 transition">
                    <XIcon className="h-5 w-5" />
                </button>
            </div>
            {/* Scrollable content */}
            <div className="flex flex-1 overflow-auto">
                <StepNavigation />
                <div className="flex-1 p-8">
                    <FormSteps />
                </div>
            </div>
        </div>
    );
}