import { useRouteError } from 'react-router-dom';
import { Home, RefreshCw } from 'lucide-react';
import { Routes } from '@/shared/utils/routes';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';

export function ErrorPage() {
    const error = useRouteError() as { statusText?: string; message?: string };
    const { navigateWithOrg } = useOrganizationManager()

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
                <div className="w-24 h-24 bg-red-50 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span className="text-6xl">🤔</span>
                </div>

                <h1 className="text-3xl font-bold text-gray-900 mb-2">Oops!</h1>
                <p className="text-gray-600 mb-6">
                    {error.statusText || error.message || "Something unexpected happened"}
                </p>

                <div className="space-y-3">
                    <button
                        onClick={() => window.location.reload()}
                        className="w-full flex items-center justify-center gap-2 bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                        <RefreshCw className="w-5 h-5" />
                        Try Again
                    </button>

                    <button
                        onClick={() => navigateWithOrg(`/${Routes.marketSpaces}`)
                        }
                        className="w-full flex items-center justify-center gap-2 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                        <Home className="w-5 h-5" />
                        Go Home
                    </button>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-100">
                    <p className="text-sm text-gray-500">
                        If this problem persists, please contact support
                    </p>
                </div>
            </div>
        </div>
    );
}