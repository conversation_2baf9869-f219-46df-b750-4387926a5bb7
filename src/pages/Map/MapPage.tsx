import { usePara<PERSON>, useSearchParams } from 'react-router-dom'
import { useQuery, useMutation } from '@apollo/client'
import { GET_WORKSPACE_SORTED_LAYERS } from '@/shared/graphQl/queries/layers'
import { UPDATE_WORKSPACE_LAST_VISITED } from '@/shared/graphQl/mutations/layers'
import { Header } from "@/components/Header/Header";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useRef, useCallback } from "react";
import { setIsMapCollapsed, setSidebarView, setMapMode, MapMode } from "@/shared/store/slices/mapSlice";
import { handleUnauthorizedError } from "@/shared/utils/authHelpers";
import { MapComponent } from "@/components/Map/Map";
import { MapChatSidebar } from "@/components/Map/components/MapChatSidebar/MapChatSidebar";
import { RecordsTable } from "@/components/RecordsTable/RecordsTable";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";
import { WorkspaceMethods } from "@/shared/utils/routes";
import { getQueryByWorkspaceType } from "@/shared/utils/layerQueries";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { processLayers } from '@/shared/utils/layerManagement';
import { CollapsiblePanel } from '@/components/CollapsiblePanel/CollapsiblePanel';
import { usePanelControl } from '@/shared/hooks/usePanelControl';
import { MapModesHeader } from '@/components/Map/components/MapModesHeader/MapModesHeader';
import { fetchWorkspaces } from '@/shared/store/slices/workspaceSlice';
import { RootState } from '@/shared/store';
import { LayersView } from '@/components/Map/components/LayersView/LayersView';

export const MapPage: React.FC = () => {
    const { t } = useTranslation();
    const { workspaceId } = useParams();
    const [searchParams] = useSearchParams();
    const workspaceType = searchParams.get('type') as WorkspaceMethods;
    const dispatch = useDispatch();
    const { selectedOrg } = useOrganizationManager();
    const [updateWorkspaceLastVisited] = useMutation(UPDATE_WORKSPACE_LAST_VISITED);
    const isFirstVisit = useRef(true);
    const mapMode = useSelector((state: RootState) => state.map.mapMode);
    const selectedWorkspace = useSelector((state: RootState) => state.workspace.selectedWorkspace)

    // Add the handleMapModeChange callback
    const handleMapModeChange = useCallback((mode: MapMode) => {
        dispatch(setMapMode(mode));
    }, []);

    const mapPanel = usePanelControl(50);
    const tablePanel = usePanelControl(30);
    const assistantPanel = usePanelControl(20);

    const isFromCreation = searchParams.get('fromCreation') === 'true';

    // Ensure at least one panel is always open
    useEffect(() => {
        const allCollapsed = mapPanel.isCollapsed && tablePanel.isCollapsed && assistantPanel.isCollapsed;
        if (allCollapsed) {
            mapPanel.togglePanel();
        }
    }, [mapPanel.isCollapsed, tablePanel.isCollapsed, assistantPanel.isCollapsed]);

    useEffect(() => {
        dispatch(setIsMapCollapsed(mapPanel.isCollapsed));
    }, [mapPanel.isCollapsed]);

    const handleToggleMap = () => {
        mapPanel.togglePanel(() => {
            if (tablePanel.isCollapsed && assistantPanel.isCollapsed) {
                tablePanel.togglePanel();
            }
        });
    };

    const handleToggleTable = () => {
        tablePanel.togglePanel(() => {
            if (mapPanel.isCollapsed && assistantPanel.isCollapsed) {
                assistantPanel.togglePanel();
            }
        });
    };

    const handleToggleAssistant = () => {
        assistantPanel.togglePanel(() => {
            if (mapPanel.isCollapsed && tablePanel.isCollapsed) {
                mapPanel.togglePanel();
            }
        });
    };

    const shouldShowResizeHandle = (panel1Collapsed: boolean, panel2Collapsed: boolean) => {
        return !panel1Collapsed && !panel2Collapsed;
    };

    const selectedQuery = getQueryByWorkspaceType(workspaceType);
    const { data: layersData, error } = useQuery(selectedQuery, {
        variables: {
            workspaceId: parseInt(workspaceId!),
            orgId: selectedOrg?.id
        },
        skip: !workspaceId || !selectedOrg?.id,
    });

    if (error) {
        handleUnauthorizedError(error)
    }

    const { data: workspaceData } = useQuery(GET_WORKSPACE_SORTED_LAYERS, {
        variables: {
            pk: parseInt(workspaceId!), orgId: selectedOrg?.id
        },
        skip: !workspaceId || !selectedOrg?.id,
    });

    useEffect(() => {
        if (workspaceId && isFirstVisit.current && selectedOrg?.id) {
            isFirstVisit.current = false
            handleWorkspaceVisit()
        }
        dispatch(setSidebarView('layers'))
    }, [workspaceId, selectedOrg])

    const handleWorkspaceVisit = async () => {
        if (!selectedOrg?.id) return;

        try {
            const response = await updateWorkspaceLastVisited({
                variables: {
                    dataInput: {
                        workspaceId: parseInt(workspaceId!),
                        orgId: selectedOrg?.id
                    }
                }
            })
            handleUnauthorizedError(response)

            // Dispatch fetchWorkspaces after successful update
            dispatch(fetchWorkspaces({
                searchTerm: '',
                offset: 0,
                limit: 3,
                orderBy: '-last_visited',
                orgId: selectedOrg.id,
            }) as any)

        } catch (error) {
            handleUnauthorizedError(error)
        }
    }

    useEffect(() => {
        if (layersData?.layers?.data) {
            processLayers(layersData, workspaceData, isFromCreation, dispatch);
        }
    }, [layersData, workspaceData]);


    const mapContent = (
        <>
            <div style={{ display: mapMode === 'details' ? 'block' : 'none' }}>
                <MapComponent />
            </div>
            <div style={{ display: mapMode === 'details' ? 'none' : 'block' }}>
                <LayersView />
            </div>
        </>
    );

    return (
        <div className="h-screen w-full flex flex-col">
            <Header />
            <MapModesHeader onMapModeChange={handleMapModeChange} />

            <div className={`flex-grow flex ${selectedWorkspace?.description ? 'max-h-[87.5vh]' : 'max-h-[88.5vh]'}`}>
                <PanelGroup direction="horizontal" className="h-full w-full" id="map-page-layout">
                    <CollapsiblePanel
                        title={mapMode === 'details' ? t('map') : t('layers')}
                        isCollapsed={mapPanel.isCollapsed}
                        onToggle={handleToggleMap}
                        panelRef={mapPanel.panelRef}
                        defaultSize={50}
                        icon="true"
                    >
                        {mapContent}
                    </CollapsiblePanel>

                    {mapMode === 'details' && shouldShowResizeHandle(mapPanel.isCollapsed, tablePanel.isCollapsed) && (
                        <PanelResizeHandle className="w-1 bg-gray-200 hover:bg-gray-400 transition-colors" />
                    )}

                    <CollapsiblePanel
                        title={t('records')}
                        isCollapsed={tablePanel.isCollapsed}
                        onToggle={handleToggleTable}
                        panelRef={tablePanel.panelRef}
                        defaultSize={30}
                        minSize={25}
                    >
                        <RecordsTable />
                    </CollapsiblePanel>

                    {mapMode === 'details' && shouldShowResizeHandle(tablePanel.isCollapsed, assistantPanel.isCollapsed) && (
                        <PanelResizeHandle className="w-1 bg-gray-200 hover:bg-gray-400 transition-colors" />
                    )}

                    <CollapsiblePanel
                        title={t('geocoreAssistant')}
                        isCollapsed={assistantPanel.isCollapsed}
                        onToggle={handleToggleAssistant}
                        panelRef={assistantPanel.panelRef}
                        defaultSize={20}
                        minSize={15}
                    >
                        <MapChatSidebar />
                    </CollapsiblePanel>
                </PanelGroup>
            </div>
        </div>
    );
};