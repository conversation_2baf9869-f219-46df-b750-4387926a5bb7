import { lazy, Suspense, useState, useCallback, useMemo, useEffect } from "react";
import searchNotFoundIcon from "@/assets/searchNotFound.svg";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Search, LayoutGrid, List, ChevronLeft, ChevronRight } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import { SortFilterDropdown } from "@/components/SortFilterDropdown/SortFilterDropdown";
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { useWorkspaces } from "@/shared/hooks/useWorkspaces";
import { WorkspaceActions } from "@/components/WorkspaceActions/WorkspaceActions";
import { WorkspaceCardSkeleton } from "@/components/WorkspaceCard/WorkspaceCardSkeleton";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { getPageNumbers } from "@/shared/utils/dashboardHelpers";
import { useDebounce } from "@/shared/hooks/useDebounce";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@apollo/client";
import { CHECK_WORKSPACE_ID_EXISTENCE } from "@/shared/graphQl/queries/dataset";
import { checkForExistingWorkspaceId } from "@/shared/utils/workspaceUtils";

const WorkspaceCard = lazy(() =>
    import('@/components/WorkspaceCard/WorkspaceCard').then(module => ({
        default: module.WorkspaceCard
    }))
);

export const DashboardPage = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [searchInput, setSearchInput] = useState("");
    const debouncedSearchTerm = useDebounce(searchInput, 300);
    const [view, setView] = useState<"grid" | "list">("grid");
    const { selectedOrg, navigateWithOrg, count } = useOrganizationManager();
    const { t, i18n } = useTranslation();
    const [itemsPerPage, setItemsPerPage] = useState(9);
    const [currentPage, setCurrentPage] = useState(1);

    const { workspaces, totalCount, error, loading } = useWorkspaces(
        debouncedSearchTerm,
        itemsPerPage,
        (currentPage - 1) * itemsPerPage
    );

    // Query to check workspace existence
    const { data: workspaceExistenceData } = useQuery(CHECK_WORKSPACE_ID_EXISTENCE, {
        variables: { orgId: selectedOrg?.id },
        skip: !selectedOrg?.id,
    });

    const totalPages = Math.ceil(totalCount / itemsPerPage);

    // Generate items per page options based on total count
    const itemsPerPageOptions = useMemo(() => {
        // Don't generate options if totalCount is less than 18
        if (totalCount < 18) {
            return [];
        }

        // Start with basic options
        const options = [];

        // Add options in increments of 9, up to the minimum of totalCount or 36
        for (let i = 9; i <= Math.min(totalCount, 36); i += 9) {
            options.push(i);
        }

        return options;
    }, [totalCount]);

    // Only show items per page control if we have enough workspaces
    const showItemsPerPageControl = itemsPerPageOptions.length > 1;


    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo(0, 0);
    };

    const handleItemsPerPageChange = (value: string) => {
        const newItemsPerPage = parseInt(value);
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page when changing items per page
    };

    const handleUploadClick = useCallback(() => {
        const params = new URLSearchParams(searchParams)
        params.set('orgId', selectedOrg.id.toString())
        const existingWorkspaceId = checkForExistingWorkspaceId(workspaceExistenceData);
        if (existingWorkspaceId) {
            params.set('workspaceId', existingWorkspaceId.toString());
        }

        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/0?${params.toString()}`);
    }, [selectedOrg, workspaceExistenceData]);

    const handleDesignLayer = useCallback(() => {
        const params = new URLSearchParams(searchParams)
        params.set('orgId', selectedOrg.id.toString())
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/0?${params.toString()}`);
    }, [selectedOrg]);

    const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchInput(e.target.value);
    }, []);

    useEffect(() => {
        const currentParams = new URLSearchParams(searchParams);
        currentParams.set('search', debouncedSearchTerm);
        setSearchParams(currentParams);
        setCurrentPage(1);
    }, [debouncedSearchTerm]);

    // Reset items per page when organization changes
    useEffect(() => {
        if (selectedOrg?.id) {
            setItemsPerPage(9);
            setCurrentPage(1);
        }
    }, [selectedOrg?.id]);

    // Adjust itemsPerPage if the current value is greater than totalCount
    useEffect(() => {
        if (totalCount > 0 && itemsPerPage > totalCount) {
            // Find the largest option that's less than or equal to totalCount
            const newOption = itemsPerPageOptions.filter(option => option <= totalCount).pop() || 9;
            setItemsPerPage(newOption);
        }
    }, [totalCount, itemsPerPage, itemsPerPageOptions]);

    const containerClassName = useMemo(() =>
        view === "grid"
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-[3rem]"
            : "space-y-4",
        [view]
    );

    const skeletonLoader = useMemo(() => (
        <div className={containerClassName}>
            {[...Array(3)].map((_, index) => (
                <WorkspaceCardSkeleton key={`skeleton-${index}`} view={view} />
            ))}
        </div>
    ), [containerClassName, view]);

    const emptyState = useMemo(() => (
        <div className="flex flex-col items-center justify-center h-[400px]">
            <img src={searchNotFoundIcon} alt="No results" className="mb-4 w-10 h-10" />
            <h3>{t('noResultsFound')}</h3>
            <p className="text-gray-500">{t('noMatchingFiles')}</p>
        </div>
    ), [t]);

    const renderContent = () => {
        if (count === 0) {
            return (
                <div className="flex flex-col items-center justify-center h-[400px]">
                    <h3 className="text-xl font-semibold mb-2">{t('noOrganizations')}</h3>
                    <p className="text-gray-500 text-center">
                        {t('pleaseContactAdmin')}
                    </p>
                </div>
            );
        }
        if (!selectedOrg?.id || loading) return skeletonLoader;
        if (error) return <div>{error}</div>;
        if (workspaces.length === 0 && debouncedSearchTerm) return emptyState;

        return (
            <>
                <div className={containerClassName}>
                    {workspaces.map((workspace: any, index: number) => (
                        <Suspense
                            key={`${workspace.id}-${index}`}
                            fallback={<WorkspaceCardSkeleton view={view} />}
                        >
                            <WorkspaceCard
                                item={workspace}
                                view={view}
                            />
                        </Suspense>
                    ))}
                </div>
                {totalPages > 1 && (
                    <div className="flex justify-between items-center gap-4 mt-8 pt-4">
                        <div>
                            {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, totalCount)} {t('of')} {totalCount}
                        </div>

                        <div className="text-sm text-gray-600 flex items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="ghost"
                                    onClick={() => handlePageChange(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    title={t('buttons.previous')}
                                    className="h-8 px-2 sm:px-4"
                                >
                                    {i18n.language === 'ar' ? <ChevronRight className="h-4 w-4" />
                                        : <ChevronLeft className="h-4 w-4" />}
                                </Button>

                                <div className="hidden sm:flex items-center gap-2">
                                    {getPageNumbers(currentPage, totalPages).map((page, idx) => (
                                        page === '...' ? (
                                            <span key={`ellipsis-${idx}`} className="px-2">...</span>
                                        ) : (
                                            <Button
                                                key={`page-${page}`}
                                                variant={currentPage === page ? "outline" : "ghost"}
                                                className="h-8 w-8 p-0"
                                                onClick={() => handlePageChange(page as number)}
                                            >
                                                {page}
                                            </Button>
                                        )
                                    ))}
                                </div>

                                <div className="sm:hidden flex items-center gap-2">
                                    <span className="text-sm">
                                        {t('page')} {currentPage} {t('of')} {totalPages}
                                    </span>
                                </div>

                                <Button
                                    variant="ghost"
                                    onClick={() => handlePageChange(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                    title={t('buttons.next')}
                                    className="h-8 px-2 sm:px-4"
                                >
                                    {i18n.language === 'ar' ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                                </Button>
                            </div>
                            {showItemsPerPageControl && (
                                <div className="flex items-center gap-2">
                                    <span className="text-sm">{t('workspacesPerPage')}:</span>
                                    <Select
                                        value={itemsPerPage.toString()}
                                        onValueChange={handleItemsPerPageChange}
                                    >
                                        <SelectTrigger className="h-8 w-[70px] no-border-focus">
                                            <SelectValue placeholder={itemsPerPage.toString()} />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {itemsPerPageOptions.map(option => (
                                                <SelectItem key={option} value={option.toString()}>
                                                    {option}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </>
        );
    };

    return (
        <div className="max-w-7xl mx-auto p-4 bg-white text-gray-800">
            <h2 className="text-2xl font-bold mb-4">{t('start_title')}</h2>
            <WorkspaceActions onUploadClick={handleUploadClick} onDesignLayer={handleDesignLayer} orgId={selectedOrg?.id} />

            {/* New Title and Description Section with Search and Filter */}
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-6">
                <div className="flex-1">
                    <h2 className="text-2xl font-bold mb-2">{t('workspaces')}</h2>
                    <p className="text-gray-600">{t("find_your_workspaces")}</p>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto">
                    <div className="relative w-full sm:w-auto">
                        <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 size-5" />
                        <Input
                            className="pr-10 w-full sm:w-64"
                            value={searchInput}
                            onChange={handleSearch}
                            placeholder={t('searchWorkspacePlaceholder')}
                        />
                    </div>
                    <div className="flex items-center gap-4">
                        <SortFilterDropdown />
                        <Tabs value={view} onValueChange={(value) => setView(value as "grid" | "list")}>
                            <TabsList className="rounded-full bg-[#F2F2F5]">
                                <TabsTrigger value="grid" className="rounded-full">
                                    <LayoutGrid className="h-4 w-4" />
                                </TabsTrigger>
                                <TabsTrigger value="list" className="rounded-full">
                                    <List className="h-4 w-4" />
                                </TabsTrigger>
                            </TabsList>
                        </Tabs>
                    </div>
                </div>
            </div>

            {renderContent()}
        </div>
    );
};