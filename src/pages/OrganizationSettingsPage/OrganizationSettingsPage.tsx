import { useEffect } from "react"
import {
    <PERSON>, ArrowLeft, Shield, Workflow, Building2,
    ArrowRight
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useTranslation } from "react-i18next"
import { RoleManagement } from "@/components/OrgSettings/RoleManagement/RoleManagement"
import { TeamMembers } from "@/components/OrgSettings/TeamMembers/TeamMembers"
import { OrganizationInfo } from "@/components/OrgSettings/OrganizationInfo/OrganizationInfo"
import { WorkspacePermissions } from "@/components/OrgSettings/WorkspacePermissions/WorkspacePermissions"
import { Link, useSearchParams } from "react-router-dom"
import { Routes } from "@/shared/utils/routes"
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager"
import { useDispatch, useSelector } from "react-redux"
import { RootState } from "@/shared/store/store"
import { setActiveTab, setOrgId } from "@/shared/store/slices/organizationSettingsSlice"
import { Header } from "@/components/Header/Header"

export const OrganizationSettingsPage = () => {
    const { t, i18n } = useTranslation()
    const dispatch = useDispatch()
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get('orgId')
    const { selectedOrg, loading } = useOrganizationManager()

    const activeTab = useSelector((state: RootState) => state.orgSettings.activeTab)

    useEffect(() => {
        if (orgId) {
            dispatch(setOrgId(orgId))
        }
    }, [orgId])

    const settingsTabs = [
        {
            title: t("organizationInfo"),
            icon: Building2,
            id: "info",
            component: OrganizationInfo,
        },
        {
            title: t("roles"),
            icon: Shield,
            id: "roles",
            component: RoleManagement,
        },
        {
            title: t("teamMembers"),
            icon: Users,
            id: "team",
            component: TeamMembers,
        },
        {
            title: t("workspaceAccess"),
            icon: Workflow,
            id: "workspaces",
            component: WorkspacePermissions
        },
    ]

    const handleTabChange = (tabId: string) => {
        dispatch(setActiveTab(tabId))
    }

    const ActiveComponent = settingsTabs.find((tab) => tab.id === activeTab)?.component || (() => null)

    return (
        <>
            <Header />
            <div className="container mx-auto flex flex-col min-h-screen">
                <header className="px-8 bg-white">
                    <div className="container flex items-center justify-between py-8">
                        <div className="flex items-center gap-4">
                            <Link
                                to={`/${Routes.marketSpaces}?orgId=${selectedOrg?.id}`}
                                className="flex items-center space-x-2 rtl:space-x-reverse"
                            >
                                {i18n.dir() === 'ltr' ? <ArrowLeft className="h-5 w-5" /> : <ArrowRight className="h-5 w-5" />}
                            </Link>
                            <div>
                                <h1 className="text-xl font-semibold">{loading ? t("loading") : selectedOrg?.settings?.name}</h1>
                                <div className="text-muted-foreground"> {t("organizationSettings")}</div>
                            </div>
                        </div>
                    </div>
                </header>
                <div className="px-8 py-6 flex gap-6">
                    <nav className="w-60 space-y-1">
                        {settingsTabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => handleTabChange(tab.id)}
                                className={cn(
                                    "w-full ltr:w-max flex items-center gap-3 rounded-full px-4 py-3 text-sm font-medium",
                                    activeTab === tab.id ? "bg-[#EEEFFD] text-[#5E58EE]" : "text-gray-700 hover:bg-gray-100",
                                )}
                            >
                                <tab.icon className="h-5 w-5" />
                                {tab.title}
                            </button>
                        ))}
                    </nav>
                    <main className="container mx-auto px-8 bg-white text-gray-800">
                        <ActiveComponent />
                    </main>
                </div>
            </div>
        </>
    )
}