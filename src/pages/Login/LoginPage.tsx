import { useTranslation } from "react-i18next";
import logoImage from "./../../assets/logotext.svg";
import { useAuth } from "react-oidc-context";
import { useEffect } from "react";

export const LoginPage: React.FC = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const auth = useAuth();
    const handleLogin = async () => {
        await auth.signinRedirect();
    };

    // Auto-redirect when component mounts
    useEffect(() => {
        handleLogin()
    }, []);

    return (
        <div className='flex min-h-screen w-full' >
            <div className={`w-1/2 bg-white flex flex-col items-center justify-center p-12 ${direction}`}>
                <h1 className="text-3xl font-bold mb-4">{t('unified Accounting Syste')}</h1>
                <p className="text-[#AAA9B2] mb-8">{t('one account for all')}</p>
                <button className="bg-custom-bg text-white px-8 py-2 rounded" onClick={handleLogin}>
                    {t('login')}
                </button>
            </div>
            <div className="w-1/2 bg-custom-bg flex items-center justify-center">
                <div className="w-32 h-32 relative">
                    <img
                        src={logoImage}
                        alt="GeoCore Logo"
                        className="w-32 h-32 object-contain"
                    />
                </div>
            </div>
        </div >
    );
};
