{"login": "<PERSON><PERSON>", "welcome": "Welcome", "description": "This is an English description.", "profile": "Profile", "settings": "Settings", "signOut": "Sign Out", "unified Accounting Syste": "Unified Accounting System", "one account for all": "One account for all office operations", "noSampleDataAvailable": "No sample data available", "noOptions": "No options", "Please choose a Feature": "Please choose a Feature", "workspaces": "Workspaces", "find_your_workspaces": "You can find your workspaces here", "start_title": "Start from here", "home_page": "Home Page", "workspacesPerPage": "Workspaces per page", "searchWorkspacePlaceholder": "Search by workspace name", "noResultsFound": "No results found", "noMatchingFiles": "No matching files found", "uploadFile": "Upload File", "createWorkspace": "Create workspace", "uploadFileDescription": "Upload a data layer to start working on the map", "store": "Store", "storeDescription": "Choose a layer from the store to start working", "createworkspaceDesc": "Create an empty workspace", "designLayer": "Design Layer", "designLayerDescription": "Design a layer from scratch", "connectDatabase": "Connect Database", "connectDatabaseDescription": "Connect with external database", "noOrganizations": "You are not added to any organization", "pleaseContactAdmin": "Please contact your administrator to add you to an organization", "map": "Map", "records": "Records", "geocoreAssistant": "GeoCore Assistant", "dataExplorer": "Data Explorer", "jsonSchema": "JSON Schema", "selectLayer": "Select layer", "page": "Page", "of": "of", "loadMore": "Load More", "remaining": "remaining", "askAnything": "Ask me anything...", "market": "Market", "switchOrganization": "Switch Organization", "edaReport": "EDA Report", "imageUpload": "Upload Image", "multipleImageUpload": "Upload Multiple Images", "link": "Link", "selectValue": "Select Value", "customValue": "Enter Custom Value", "editSummaryFields": "Edit Summary <PERSON>", "loading": "Loading...", "update": "Update", "updating": "Updating...", "cancel": "Cancel", "searchPlace": "Search by place or (lon, lat)", "mapSearch": "Search on map", "results": "Results", "layers": "Layers", "filters": "filters", "baseLayers": "Base Layers", "loadingLayers": "Loading layers", "dataset": "Dataset", "addData": "Add", "satellite": "Satellite", "osm": "OSM", "googleMaps": "Google Maps", "darkMap": "Dark Map", "drawPoint": "Draw Point", "drawRectangle": "Draw Rectangle", "drawCircle": "Draw Circle", "drawPolygon": "Draw Polygon", "clearDrawings": "Clear Drawings", "measureDistance": "Measure Distance", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "selectFeature": "Select Feature", "recentWorkspaces": "Recent Workspaces", "allWorkspaces": "All Workspaces", "lastVisited": "Last visited", "lastEdit": "Last edit", "creationDate": "Creation Date", "sortBy": "Sort by", "sortFiler": "Filter/Sort", "sampleData": "Sample Data:", "backToTable": "Back to Table", "view": "View", "viewInDetails": "View in Details", "order": "Order", "oldestFirst": "Oldest First", "newestFirst": "Newest First", "featureInfo": {"pinTooltip": "Pin this info window", "viewDetails": "View Details", "editSummaryFields": "Edit Summary <PERSON>", "close": "Close"}, "layer": {"title": "Layer", "recordDetails": "Record Details", "deleteTitle": "Delete Layer", "deleteConfirmation": "Are you sure you want to delete layer \"${name}\"?\nThis action cannot be undone."}, "layerActions": {"layerSettings": "Layer Settings", "zoom_to_layer": "Zoom to layer", "duplicate": "Duplicate", "rename": "<PERSON><PERSON>", "delete": "Delete", "addLayer": "Add Layer", "showThisLayerOnly": "Show this layer only", "showAllLayers": "Show all layers", "layerShape": "Layer Shape", "deleteConfirm": "Are you sure you want to delete this layer?"}, "layerSettings": {"points": "Points", "heatMap": "Heat Map", "layerType": "Layer Type", "selectLayerType": "Select layer type", "circle": "Circle", "triangle": "Triangle", "square": "Square", "layerColor": "Layer Color", "range": "Range", "selectShape": "Select shape", "borders": "Borders", "outside": "Outside", "inside": "Inside", "color": "Color", "transparency": "Transparency", "size": "Size", "colorGradient": "Color Gradient", "heatmapIntensity": "Heatmap Intensity", "heatmapRadius": "Heatmap Radius", "selectProperty": "Select Property", "densityRange": "Density Range", "selectRangeType": "Select Range Type", "automatic": "Automatic", "manual": "Manual", "minValue": "Minimum Value", "maxValue": "Maximum Value", "apply": "Apply", "weightBy": "Weight by", "heatmapCreated": "Heatmap created successfully", "selectWeightPropertyError": "Please select a weight property", "settingsApplied": "Heatmap settings applied successfully", "noLayerSelected": "No layer selected", "borderSettingsApplied": "Border settings applied successfully", "errorApplyingSettings": "Error applying border settings", "units": {"meter": "<PERSON>er", "kilometer": "Kilometer", "centimeter": "Centimeter"}}, "field": {"array": "array", "selectValue": "Select value", "customValue": "Enter custom value", "required": "Required field", "pleaseSelectFromExistingValues": "Please select from the available values"}, "alerts": {"success": {"recordCreated": "Record created successfully", "recordUpdated": "Record updated successfully", "imageUploaded": "Image uploaded successfully", "summaryFieldsUpdateProgress": "Summary fields update in progress", "workspaceDeleted": "Workspace deleted successfully", "nameUpdated": "Workspace name updated successfully", "workspaceCreated": "Workspace created successfully"}, "error": {"recordCreate": "Failed to create record", "recordUpdate": "Failed to update record", "imageUpload": "Error uploading image", "summaryFields": "Failed to update summary fields", "workspaceDelete": "Error deleting workspace", "unauthorized": "Unauthorized access", "emptyName": "Workspace name cannot be empty", "updateError": "Error updating workspace name", "workspaceCreateError": "Failed to create workspace"}, "confirm": {"deleteRecord": "Are you sure you want to delete this record?", "discardChanges": "Are you sure you want to discard changes?"}}, "workspace": {"addDataset": {"title": "Add Dataset to Workspace"}, "card": {"layers": "Layers", "layers_one": "Layer", "rows": "Rows", "minutesAgo": "minutes ago", "hoursAgo": "hours ago", "daysAgo": "days ago", "monthsAgo": "months ago", "yearsAgo": "years ago", "ago": "ago"}, "creation": {"title": "Create New Workspace", "steps": {"fileUpload": "Upload File", "aiReport": "AI Report", "setColumns": "Set Columns", "showPlan": "Show Plan", "summaryFields": "Summary Fields", "layerProperties": "Layer Properties", "layerForm": "Layer Form"}}, "fileUpload": {"title": "Let's Start with Basics", "description": "Upload file and set layer settings", "alerts": {"fileInProgress": "This file is already being processed. Please try again later", "createError": "Error creating dataset", "uploadSuccess": "File uploaded successfully", "uploadError": "Error uploading file"}, "processing": "Processing file...", "supportedFormats": "Supported formats: KML, GeoJSON, CSV, Gpkg", "success": "File uploaded successfully", "errors": {"unsupportedType": "File type not supported. Please upload KML, GeoJSON, CSV, or Gpkg files.", "uploadFailed": "Error uploading file. Please try again."}}, "aiReport": {"title": "Data Quality Report", "description": "Data analysis and results", "fullscreen": "Full Screen View", "download": "Download File", "generating": "Generating report...", "inProgress": "Report in progress...", "loadingReport": "Loading report...", "reportPreview": "Report Preview", "errorFetchingReport": "Error fetching the report", "errorLoadingReport": "Error loading report"}, "setColumns": {"title": "Set File Longitude and Latitude Format", "description": "Choose coordinates type and columns", "columnSettingsSaved": "Column settings saved successfully"}, "showPlan": {"title": "View Data Schema", "loading": "Loading...", "errorUpdatingFiles": "Error updating the files"}, "advancedFields": {"title": "Summary Fields", "description": "These fields will appear on the map", "noData": "No data found"}, "incomplete": {"title": "Incomplete workspace exists", "description": "Would you like to continue creating the workspace?"}, "layerProperties": {"title": "Let's Start with the Basics", "description": " Define the layer properties on the map", "alerts": {"success": "Layer properties saved successfully", "error": "Error saving layer properties", "updateSuccess": "Layer properties updated successfully", "updateError": "Error updating layer properties"}}, "layerForm": {"title": "Layer Form", "description": "Define the layer properties on the map", "alerts": {"success": "Layer form saved successfully", "error": "Error saving layer form"}, "validation": {"requiredFieldMissing": "No required fields have been specified. The data must have at least one column as a required field to identify each row.", "visibleFieldMissing": "No visible fields have been specified. At least one field must be visible in the summary.", "identifier": {"required": "Identifier name is required", "format": "Identifier name must contain only English letters or numbers"}, "fieldTitle": {"required": "Field title is required"}, "fieldType": {"required": "Field type is required"}}, "userInterface": "User Interface", "advancedEditor": "Advanced Editor", "preview": "Form Preview", "view": "View", "addField": "Add Field", "fieldTitle": "Field Title", "fieldType": "Field Type", "identifier": "Identifier", "requiredField": "Required Field", "showInSummary": "Show in Summary", "requiredTooltip": "These fields must be filled in", "summaryTooltip": "These fields will appear on the map", "fieldTypes": {"text": "Text", "number": "Number", "boolean": "Boolean", "date": "Date"}}, "deleteConfirmTitle": "Are you sure you want to delete this workspace?", "deleteConfirmDescription": "Workspace \"${name}\" will be deleted\nThis action cannot be undone."}, "buttons": {"next": "Next", "previous": "Previous", "create": "Create", "processing": "Processing...", "Cancel Changes": "Cancel Changes", "yes": "Yes", "no": "No", "uploading": "Uploading...", "uploadFiles": "Upload Files", "update": "Update", "skipToWorkspace": "Skip to workspace"}, "common": {"cancel": "Cancel", "delete": "Delete", "save": "Save", "rename": "<PERSON><PERSON>", "loading": "Loading..."}, "modal": {"addRecord": "Add New Record", "editLayer": "Edit Layer", "viewLayer": "Record Details"}, "upload": {"success": "Image uploaded successfully", "error": "Failed to upload image", "dragAndDrop": "Drag and drop image here or click to select", "dragAndDropFiles": "Drag and drop file here or click to select", "supportedFormats": "JPG, PNG, GIF, JPEG up to 5MB", "validation": {"invalidType": "File must be an image of type JPG, JPEG, PNG, or GIF", "tooLarge": "Image size must be less than 5MB"}}, "schema": {"stepOne": {"layerName": {"title": "Enter a distinctive name for the layer"}, "layerDescription": {"title": "Add a brief description of the layer (optional)"}, "layerColor": {"title": "Select the default color for displaying the layer"}, "showOnly": {"title": "Use layer for display only (non-editable)"}, "fileUpload": {"title": "Upload File"}}, "stepThree": {"coordinateType": {"title": "Choose Coordinate Type", "point": "Point", "other": "Other", "pointHint": "Longitude and latitude distributed in separate columns", "otherHint": "Longitude and latitude in the same column"}, "columnLayout": {"title": "Choose Longitude and Latitude Format", "singleColumn": "Single Column", "twoColumns": "Two Columns", "singleColumnHint": "Longitude and latitude in the same column", "twoColumnsHint": "Longitude and latitude in separate columns"}, "columns": {"latLng": "Choose Longitude and Latitude", "lat": "Longitude", "lng": "Latitude"}}, "stepFour": {"code": {"title": "View Schema"}}, "stepFive": {"longitude": {"title": "Field Name"}}}, "organizationSettings": "Organization Settings", "organizationInfo": "Organization Information", "manageBasicOrgInfo": "Manage basic organization information", "organizationName": "Organization Name", "organization": "Organization", "edit": "Edit", "users": "Users", "totalUsers": "${count} Users", "activeUsers": "${count} Active", "errorDeletingRole": "An error occurred while deleting the role. Please try again.", "roleDeletedSuccess": "The role has been deleted successfully.", "deleteRoleConfirmation": "Are you sure you want to delete the role \"${role}\"? This action cannot be undone.", "errorLoadingRoles": "Error loading roles. Please try again.", "noRolesFound": "No roles found. Create a new role to get started.", "roleCreatedSuccess": "Role created successfully.", "roleUpdatedSuccess": "Role updated successfully.", "roles": "Roles", "roleManagement": "Role Management", "roleManagementDescription": "Manage roles and permissions for your organization", "newRole": "New Role", "createNewRole": "Create New Role", "createRoleDescription": "Add a new role to the organization. This will allow you to assign specific permissions to users.", "roleName": "Role Name", "roleNamePlaceholder": "Example: Project Manager", "roleDescriptionPlaceholder": "Describe the role responsibilities", "systemPermissions": "System Permissions", "manageRoles": "Manage Roles", "manageUsers": "Manage Users", "manageWorkspaces": "Manage Workspaces", "createRole": "Create Role", "editRole": "Edit", "deleteRole": "Delete", "usersCount": "${count} users", "descrption": "description", "editRoleDescription": "Modify role permissions and settings.", "loadingPermissions": "Loading permissions...", "saveChanges": "Save Changes", "roleNameRequired": "Role name is required", "add": "Add", "delete": "Delete", "role": "Role", "user": "User", "workspaceTitle": "Workspace", "addRole": "Add Role", "viewRole": "View Role", "editUser": "Edit User", "deleteUser": "Delete User", "viewUser": "View User", "addWorkspace": "Add Workspace", "editWorkspace": "Edit Workspace", "deleteWorkspace": "Delete Workspace", "viewWorkspace": "View Workspace", "userManagement": "User Management", "workspaceManagement": "Workspace Management", "generalManagerDescription": "Full permissions for all workspaces and settings", "workspaceManagerDescription": "Can manage assigned workspaces and their members", "editorDescription": "Can edit assigned workspaces", "viewerDescription": "Can only view assigned workspaces", "teamMembers": "Team Members", "teamMembersDescription": "Manage team members in the organization and their roles", "inviteUser": "Invite User", "searchTeamMembers": "Search for team members...", "active": "Active", "pending": "Pending", "resendInvitation": "Resend Invitation", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "sending": "Sending...", "sendInvitation": "Send Invitation", "userInvitedSuccessfully": "User invited successfully", "showing": "Showing", "noUsers": "No users found", "noSearchResults": "No search results found", "removeUser": "Remove User", "generalManager": "General Manager", "workspaceManager": "Workspace Manager", "editor": "Editor", "viewer": "Viewer", "workspaceAccess": "Workspace Access", "workspacePermissions": "Workspace Permissions", "workspacePermissionsDescription": "Manage access to workspaces for your organization", "search": "Search", "manageWorkspaceUsers": "Manage workspace users", "addUser": "Add User", "manager": "Manager", "member": "Member", "viewOnly": "View Only", "remove": "Remove", "inactive": "Inactive", "selected": "selected", "selectFromExistingUsers": "Select From Existing Users", "selectUsersToAddToWorkspace": "Select users to add to workspace ${workspaceName}", "searchUsers": "Search users", "noUsersMatchSearch": "No users match your search", "noUsersAvailableToAdd": "No users available to add", "specificPermissions": "Specific Permissions", "pleaseSelectAtLeastOneUser": "Please select at least one user", "errorAddingUsers": "Error adding users", "errorLoadingUsers": "Error loading users", "errorLoadingPermissions": "Error loading permissions", "permissions": "Permissions", "noPermissions": "No permissions", "selectPermissions": "Select permissions", "permissionsUpdatedSuccessfully": "Permissions updated successfully", "errorUpdatingPermissions": "Error updating permissions", "noWorkspaceSelected": "No workspace selected", "usersAddedSuccessfully": "Users added successfully", "errorRemovingUser": "Error removing user", "noUsersInWorkspace": "No users in this workspace", "pleaseSelectWorkspace": "Please select a workspace", "userRoleUpdatedSuccessfully": "User role updated successfully", "userActivatedSuccessfully": "User activated successfully", "userDeactivatedSuccessfully": "User deactivated successfully", "userRemovedSuccessfully": "User removed successfully", "activateAccount": "Activate Account", "deactivateAccount": "Deactivate Account", "removeUserConfirmation": "Are you sure you want to remove ${name} from the organization? This action cannot be undone.", "selectWorkspacePrompt": "Select a Workspace", "selectWorkspaceDescription": "Choose a workspace to manage its permissions", "schemaDiagram": "<PERSON><PERSON><PERSON>", "mapModes": {"details": "Details", "exploratory": "Exploratory"}, "selectLayers": "Select Layers", "selectAll": "Select All", "reset": "Reset", "selectLayersFirst": "Select layers first", "selectSource": "Select Source", "lastUpdated": "Last updated", "thumbnail": {"updating": "Updating...", "updated": "Updated!", "error": "Error", "button": "Update Thumbnail"}, "createWorkspaceModal": {"title": "Create New Workspace", "workspaceName": "Workspace Name", "workspaceNamePlaceholder": "Enter workspace name", "workspaceDescription": "Description (Optional)", "workspaceDescriptionPlaceholder": "Enter workspace description", "create": "Create Workspace", "cancel": "Cancel", "workspaceNameRequired": "Workspace name is required", "creating": "Creating...", "createWorkspaceSuccess": "Workspace created successfully", "createWorkspaceError": "Failed to create workspace"}}