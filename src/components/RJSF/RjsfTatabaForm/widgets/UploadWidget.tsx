import { WidgetProps } from "@rjsf/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import { useState } from "react";
import { showToast } from "@/shared/utils/toastConfig";
import { useTranslation } from "react-i18next";

const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const UploadWidget: React.FC<WidgetProps & { multiple?: boolean }> = ({
    id,
    onChange,
    uiSchema,
    multiple = false,
    formContext,
    ...props
}) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState<boolean>(false);
    const [preview, setPreview] = useState<string[]>(() => {
        const value = props.value;
        if (value) {
            if (typeof value === 'string') {
                return [value];
            } else if (Array.isArray(value)) {
                return value;
            }
        }
        return [];
    });

    const validateImage = (file: File) => {
        if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
            showToast.error(t('upload.validation.invalidType'));
            return false;
        }

        if (file.size > MAX_FILE_SIZE) {
            showToast.error(t('upload.validation.tooLarge'));
            return false;
        }

        return true;
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (!files) return;

        const validFiles: File[] = [];
        const newPreviews: string[] = [...preview];
        const responses: string[] = Array.isArray(props.value) ? [...props.value] : props.value ? [props.value] : [];

        setLoading(true);
        try {
            for (const file of Array.from(files)) {
                if (validateImage(file)) {
                    const response = await formContext.onFileChange(file);
                    if (response) {
                        validFiles.push(file);
                        responses.push(response);
                        const reader = new FileReader();
                        reader.onloadend = () => {
                            newPreviews.push(reader.result as string);
                            if (newPreviews.length === preview.length + validFiles.length) {
                                setPreview(newPreviews);
                                onChange(multiple ? responses : responses[responses.length - 1]);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                }
            }
            setLoading(false);
            if (validFiles.length > 0) {
                showToast.success(t('upload.success'));
            }
        } catch (error) {
            setLoading(false);
            console.error(error);
            showToast.error(t('upload.error'));
        }
    };

    const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        const files = e.dataTransfer.files;
        if (!files) return;

        const validFiles: File[] = [];
        const newPreviews: string[] = [...preview];
        const responses: string[] = Array.isArray(props.value) ? [...props.value] : props.value ? [props.value] : [];

        setLoading(true);
        try {
            for (const file of Array.from(files)) {
                if (validateImage(file)) {
                    const response = await formContext.onFileChange(file);
                    if (response) {
                        validFiles.push(file);
                        responses.push(response);
                        const reader = new FileReader();
                        reader.onloadend = () => {
                            newPreviews.push(reader.result as string);
                            if (newPreviews.length === preview.length + validFiles.length) {
                                setPreview(newPreviews);
                                onChange(multiple ? responses : responses[responses.length - 1]);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                }
            }
            setLoading(false);
            if (validFiles.length > 0) {
                showToast.success(t('upload.success'));
            }
        } catch (error) {
            setLoading(false);
            console.error(error);
            showToast.error(t('upload.error'));
        }
    };

    const handleClear = (fileIndex?: number) => {
        if (fileIndex !== undefined) {
            const updatedPreview = [...preview];
            updatedPreview.splice(fileIndex, 1);
            setPreview(updatedPreview);

            const currentValue = props.value;
            if (multiple && Array.isArray(currentValue)) {
                const updatedValue = [...currentValue];
                updatedValue.splice(fileIndex, 1);
                onChange(updatedValue.length ? updatedValue : undefined);
            } else {
                onChange(undefined);
            }
        } else {
            setPreview([]);
            onChange(undefined);
        }
    };

    return (
        <div className="space-y-2">
            <div
                className={`border-2 border-dashed rounded-lg p-6 hover:border-primary/50 transition-colors`}
                onDragOver={(e) => e.preventDefault()}
                onDrop={handleDrop}
            >
                <Input
                    id={id}
                    type="file"
                    className="hidden"
                    onChange={handleFileChange}
                    accept={ACCEPTED_IMAGE_TYPES.join(',')}
                    multiple={multiple}
                    disabled={props?.disabled}
                />
                <div className="flex flex-col items-center gap-4">
                    <ImageIcon className="h-12 w-12 text-muted-foreground" />
                    {(multiple || (!multiple && preview.length === 0)) && (
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById(id)?.click()}
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <span className="animate-spin mr-2">⌛</span>
                                    {t('buttons.uploading')}
                                </>
                            ) : (
                                <>
                                    <Upload className="mr-2 h-4 w-4" />
                                    {uiSchema?.['ui:uploadButtonText'] || 'رفع صورة'}
                                </>
                            )}
                        </Button>
                    )}
                    <p className="text-sm text-muted-foreground text-center">
                        {t('upload.dragAndDrop')}
                        <br />
                        <span className="text-xs">
                            {t('upload.supportedFormats')}
                        </span>
                    </p>
                </div>
                {preview.length > 0 && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                        {preview.map((img, index) => (
                            <div key={index} className="relative aspect-video rounded-lg overflow-hidden bg-gray-100">
                                <img
                                    src={img}
                                    alt="Preview"
                                    className="object-contain w-full h-full"
                                />
                                {!props?.disabled && !loading && (
                                    <Button
                                        type="button"
                                        variant="destructive"
                                        size="sm"
                                        className="absolute top-2 right-2 p-1 h-4"
                                        onClick={() => handleClear(index)}
                                        disabled={props?.disabled}
                                    >
                                        <X className="h-1 w-1" />
                                    </Button>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};
