import { WidgetProps } from "@rjsf/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Upload, X, File as FileIcon } from "lucide-react";
import { useState } from "react";
import { showToast } from "@/shared/utils/toastConfig";
import { useTranslation } from "react-i18next";

const ACCEPTED_FILE_EXTENSIONS = {
    images: ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'],
    documents: ['pdf', 'doc', 'docx']
};

const ACCEPTED_MIME_TYPES = {
    images: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/svg+xml',
        'image/webp',
    ],
    documents: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
};

const MAX_FILE_SIZE = 15 * 1024 * 1024; // 5MB

export const FileWidget: React.FC<WidgetProps & { multiple?: boolean }> = ({
    id,
    onChange,
    uiSchema,
    multiple = false,
    formContext,
    ...props
}) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState<boolean>(false);
    const [files, setFiles] = useState<{ name: string; type: string; preview?: string }[]>(() => {
        const value = props.value;
        if (value) {
            if (typeof value === 'string') {
                return [{ name: value.split('/').pop() || value, type: value.split('.').pop() || '' }];
            } else if (Array.isArray(value)) {
                return value.map(v => ({
                    name: v.split('/').pop() || v,
                    type: v.split('.').pop() || ''
                }));
            }
        }
        return [];
    });

    const getFileExtension = (filename: string): string => {
        return filename.split('.').pop()?.toLowerCase() || '';
    };

    const validateFile = (file: File) => {
        const extension = getFileExtension(file.name);
        const allowedTypes = [...ACCEPTED_FILE_EXTENSIONS.images, ...ACCEPTED_FILE_EXTENSIONS.documents];
        const isValidType = allowedTypes.includes(extension) ||
            [...ACCEPTED_MIME_TYPES.images, ...ACCEPTED_MIME_TYPES.documents].includes(file.type);

        if (!isValidType) {
            showToast.error(t('upload.validation.invalidType'));
            return false;
        }

        if (file.size > MAX_FILE_SIZE) {
            showToast.error(t('upload.validation.tooLarge'));
            return false;
        }

        return true;
    };

    const isImage = (fileType: string) => {
        return ACCEPTED_FILE_EXTENSIONS.images.includes(fileType.toLowerCase()) ||
            ACCEPTED_MIME_TYPES.images.includes(fileType);
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputFiles = e.target.files;
        if (!inputFiles) return;

        const validFiles: File[] = [];
        const newFiles: { name: string; type: string; preview?: string }[] = [...files];
        const responses: string[] = Array.isArray(props.value) ? [...props.value] : props.value ? [props.value] : [];

        setLoading(true);
        try {
            for (const file of Array.from(inputFiles)) {
                if (validateFile(file)) {
                    const response = await formContext.onFileChange(file);
                    if (response) {
                        validFiles.push(file);
                        responses.push(response);

                        const fileInfo = {
                            name: file.name,
                            type: file.type || getFileExtension(file.name)
                        } as any;

                        if (isImage(fileInfo.type)) {
                            const reader = new FileReader();
                            reader.onloadend = () => {
                                fileInfo.preview = reader.result as string;
                                newFiles.push(fileInfo);
                                if (newFiles.length === files.length + validFiles.length) {
                                    setFiles(newFiles);
                                    onChange(multiple ? responses : responses[responses.length - 1]);
                                }
                            };
                            reader.readAsDataURL(file);
                        } else {
                            newFiles.push(fileInfo);
                        }
                    }
                }
            }

            if (!isImage(validFiles[0]?.type)) {
                setFiles(newFiles);
                onChange(multiple ? responses : responses[responses.length - 1]);
            }

            if (validFiles.length > 0) {
                showToast.success(t('upload.success'));
            }
        } catch (error) {
            console.error(error);
            showToast.error(t('upload.error'));
        } finally {
            setLoading(false);
        }
    };

    const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        const droppedFiles = e.dataTransfer.files;
        if (!droppedFiles) return;

        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = multiple;
        input.accept = [...ACCEPTED_FILE_EXTENSIONS.images, ...ACCEPTED_FILE_EXTENSIONS.documents]
            .map(ext => `.${ext}`)
            .join(',');

        const dataTransfer = new DataTransfer();
        Array.from(droppedFiles).forEach(file => dataTransfer.items.add(file));
        input.files = dataTransfer.files;

        const event = new Event('change', { bubbles: true });
        input.dispatchEvent(event);

        handleFileChange({ target: input } as unknown as React.ChangeEvent<HTMLInputElement>);
    };

    const handleClear = (fileIndex?: number) => {
        if (fileIndex !== undefined) {
            const updatedFiles = [...files];
            updatedFiles.splice(fileIndex, 1);
            setFiles(updatedFiles);

            const currentValue = props.value;
            if (multiple && Array.isArray(currentValue)) {
                const updatedValue = [...currentValue];
                updatedValue.splice(fileIndex, 1);
                onChange(updatedValue.length ? updatedValue : undefined);
            } else {
                onChange(undefined);
            }
        } else {
            setFiles([]);
            onChange(undefined);
        }
    };

    return (
        <div className="space-y-2">
            <div
                className={`border-2 border-dashed rounded-lg p-6 hover:border-primary/50 transition-colors`}
                onDragOver={(e) => e.preventDefault()}
                onDrop={handleDrop}
            >
                <Input
                    id={id}
                    type="file"
                    className="hidden"
                    onChange={handleFileChange}
                    accept={[...ACCEPTED_FILE_EXTENSIONS.images, ...ACCEPTED_FILE_EXTENSIONS.documents]
                        .map(ext => `.${ext}`)
                        .join(',')}
                    multiple={multiple}
                    disabled={props?.disabled}
                />
                <div className="flex flex-col items-center gap-4">
                    <FileIcon className="h-12 w-12 text-muted-foreground" />
                    {(multiple || (!multiple && files.length === 0)) && (
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById(id)?.click()}
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <span className="animate-spin mr-2">⌛</span>
                                    {t('buttons.uploading')}
                                </>
                            ) : (
                                <>
                                    <Upload className="mr-2 h-4 w-4" />
                                    {uiSchema?.['ui:uploadButtonText'] || t('buttons.uploadFiles')}
                                </>
                            )}
                        </Button>
                    )}
                    <p className="text-sm text-muted-foreground text-center">
                        {t('upload.dragAndDropFiles')}
                        <br />
                        <span className="text-xs">
                            {[...ACCEPTED_FILE_EXTENSIONS.images, ...ACCEPTED_FILE_EXTENSIONS.documents]
                                .map(ext => ext.toUpperCase())
                                .join(', ')}
                        </span>
                    </p>
                </div>
                {files.length > 0 && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                        {files.map((file, index) => (
                            <div key={index} className="relative flex items-center p-2 rounded-lg bg-gray-100">
                                {isImage(file.type) && file.preview ? (
                                    <div className="relative w-full aspect-video rounded-lg overflow-hidden">
                                        <img
                                            src={file.preview}
                                            alt={file.name}
                                            className="object-contain w-full h-full"
                                        />
                                    </div>
                                ) : (
                                    <>
                                        <FileIcon className="h-6 w-6 mr-2 text-muted-foreground" />
                                        <span className="text-sm truncate flex-1">{file.name}</span>
                                    </>
                                )}
                                {!props?.disabled && !loading && (
                                    <Button
                                        type="button"
                                        variant="destructive"
                                        size="sm"
                                        className="absolute top-2 right-2 p-1 h-4"
                                        onClick={() => handleClear(index)}
                                        disabled={props?.disabled}
                                    >
                                        <X className="h-1 w-1" />
                                    </Button>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};
