import { WidgetProps } from '@rjsf/utils'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'

export function RadioWidget({
    id,
    schema,
    options,
    value,
    disabled,
    readonly,
    onChange,
}: WidgetProps) {
    const { enumOptions, enumDisabled } = options

    return (
        <div className="space-y-3 rtl">
            <RadioGroup
                value={`${value}`}
                onValueChange={(value) => onChange(schema.type === 'boolean' ? value !== 'false' : value)}
                className="flex flex-col space-y-2"
            >
                {enumOptions?.map((option: any, i: number) => {
                    const itemDisabled = enumDisabled?.includes(option.value)

                    return (
                        <div key={i} className="flex items-center gap-2 space-x-2 rtl">
                            <RadioGroupItem
                                value={`${option.value}`}
                                id={`${id}-${i}`}
                                disabled={disabled || itemDisabled || readonly}
                            />
                            <Label htmlFor={`${id}-${i}`}>{option.label}</Label>
                        </div>
                    )
                })}
            </RadioGroup>
        </div>
    )
}
