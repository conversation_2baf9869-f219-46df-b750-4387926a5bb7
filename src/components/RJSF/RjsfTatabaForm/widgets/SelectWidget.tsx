import { WidgetProps } from "@rjsf/utils";
import {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem
} from "@/components/ui/select";
import { JSONSchema7 } from "json-schema";

interface ExtendedJSONSchema extends JSONSchema7 {
    enumNames?: string[];
}

export const SelectWidget: React.FC<WidgetProps> = ({
    value,
    onChange,
    options,
    schema,
    label,
    disabled,
    readonly,
    required
}) => {
    const getOptions = () => {
        if (options.enumOptions) {
            return options.enumOptions;
        }

        const extendedSchema = schema as ExtendedJSONSchema;

        if (typeof schema.items === 'object' && schema.items && 'enum' in schema.items) {
            const itemsSchema = schema.items as ExtendedJSONSchema;
            return (itemsSchema?.enum as string[]).map((value, index) => ({
                value,
                label: itemsSchema.enumNames?.[index] || value
            }));
        }

        if (extendedSchema?.enum) {
            return (extendedSchema?.enum as string[]).map((value, index) => ({
                value,
                label: extendedSchema.enumNames?.[index] || value
            }));
        }

        return [];
    };

    return (
        <Select
            value={value?.toString() || '_empty'}
            onValueChange={(val) => onChange(val === '_empty' ? undefined : val)}
            disabled={disabled || readonly}
            dir="rtl" >
            <SelectTrigger className="w-full no-border-focus">
                <SelectValue placeholder={label} />
            </SelectTrigger>
            <SelectContent className="no-border-focus">
                {!required && (
                    <SelectItem value="_empty">اختر...</SelectItem>
                )}
                {getOptions().map((option: any) => (
                    <SelectItem
                        key={option.value}
                        value={option.value?.toString() || '_empty'}
                    >
                        {option.label}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    );
};
