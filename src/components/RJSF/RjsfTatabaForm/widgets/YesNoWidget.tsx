import { WidgetProps } from '@rjsf/utils'
import { Button } from '@/components/ui/button'

export function YesNoWidget({
    value,
    disabled,
    readonly,
    onChange,
    options
}: WidgetProps) {
    const { enumOptions = [] } = options

    return (
        <div className="space-y-2">
            {/* {label && (
                <Label htmlFor={id}>{label}</Label>
            )} */}

            <div className="flex gap-2">
                {enumOptions.map((option: any) => (
                    <Button
                        key={option.value}
                        type="button"
                        variant={value === option.value ? 'default' : 'outline'}
                        disabled={disabled || readonly}
                        onClick={() => onChange(option.value)}
                        className="flex-1"
                    >
                        {option.label}
                    </Button>
                ))}
            </div>
        </div>
    )
}
