import { WidgetProps } from '@rjsf/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Minus } from 'lucide-react'

export function UpDownWidget({
    id,
    value,
    disabled,
    readonly,
    onChange,
}: WidgetProps) {
    // Prevent event bubbling and form submission
    const handleButtonClick = (e: React.MouseEvent, action: () => void) => {
        e.preventDefault();
        e.stopPropagation();
        action();
    };

    const handleIncrement = () => {
        const newValue = (value || 0) + 1;
        onChange(newValue);
    };

    const handleDecrement = () => {
        const newValue = Math.max((value || 0) - 1, 0);
        onChange(newValue);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        onChange(e.target.value ? Number(e.target.value) : undefined);
    };

    return (
        <div className="space-y-2">
            <div className="flex items-center gap-2">
                <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={(e) => handleButtonClick(e, handleDecrement)}
                    disabled={disabled || readonly || !value || value < 1}
                >
                    <Minus className="h-4 w-4" />
                </Button>

                <Input
                    id={id}
                    type="number"
                    value={value || 0}
                    onChange={handleInputChange}
                    disabled={disabled || readonly}
                    className="w-20 text-center"
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                        }
                    }}
                />

                <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={(e) => handleButtonClick(e, handleIncrement)}
                    disabled={disabled || readonly}
                >
                    <Plus className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}