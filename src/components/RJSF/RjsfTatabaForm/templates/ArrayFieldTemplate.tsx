import { ArrayFieldTemplateProps } from '@rjsf/utils'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { isImageUri } from '@/shared/utils/tabelrecordHelper'
import { ImageGallery } from '@/components/RecordsTable/ImageGallery'

export function ArrayFieldTemplate({
    title,
    items,
    canAdd,
    onAddClick,
    uiSchema,
    disabled,
    readonly,
    formData,
}: ArrayFieldTemplateProps) {
    // Filter out image items for the gallery
    const imageItems = formData?.filter((item: any) => isImageUri(item)) || []
    const hasImages = imageItems.length > 0

    return (
        <div className="space-y-4">
            {title && !uiSchema?.['ui:titleHide'] && (
                <h3 className="text-lg font-semibold">{title}</h3>
            )}

            {/* Horizontal Image Gallery */}
            {hasImages && (
                <div className="border rounded-lg p-4 bg-blue-50">
                    <ImageGallery
                        images={imageItems}
                        displayMode="horizontal"
                        size="medium"
                    />
                </div>
            )}

            <div className="space-y-4">
                {items.map((element, index) => {
                    const itemValue = formData?.[index]
                    const isImage = isImageUri(itemValue)

                    if (isImage) {
                        return null
                    }
                    return (
                        <div
                            key={element.key}
                            className={`p-4 border rounded-lg ${isImage ? 'bg-blue-50' : 'bg-gray-50'}`}
                        >
                            {!uiSchema?.['ui:itemTitleHide'] && (
                                <div className="mb-2 font-medium">
                                    {uiSchema?.['ui:itemTitle']
                                        ? `${uiSchema['ui:itemTitle']} ${index + 1}`
                                        : `Item ${index + 1}`}
                                </div>
                            )}

                            <div className="flex items-start gap-4">
                                <div className="flex-1">
                                    {/* Hide the URL input for images */}
                                    {isImage ? (
                                        <div className="text-sm text-gray-600 italic">
                                            Image URL (hidden for preview)
                                        </div>
                                    ) : (
                                        element.children
                                    )}
                                </div>
                            </div>

                            {!uiSchema?.['ui:deleteItemButtonHide'] && element.hasRemove && (
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={element.onDropIndexClick(index)}
                                    disabled={disabled || readonly}
                                    className="mt-2"
                                >
                                    Remove
                                </Button>
                            )}
                        </div>
                    )
                })}
            </div>

            {!uiSchema?.['ui:addMoreItemsHide'] && canAdd && (
                <Button
                    onClick={onAddClick}
                    disabled={disabled || readonly}
                    className="w-full"
                >
                    <Plus className="mr-2 h-4 w-4" />
                    {uiSchema?.['ui:addMoreItemsTitle'] || 'Add Item'}
                </Button>
            )}
        </div>
    )
}
