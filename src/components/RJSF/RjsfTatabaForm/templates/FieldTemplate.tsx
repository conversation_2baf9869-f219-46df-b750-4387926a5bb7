import { FieldTemplateProps } from '@rjsf/utils'

export function FieldTemplate({
    id,
    children,
    displayLabel,
    label,
    required,
    rawErrors = [],
    rawHelp,
    rawDescription,
}: FieldTemplateProps) {
    return (
        <div className="space-y-2">
            {displayLabel && (
                <label htmlFor={id} className="block text-sm font-medium">
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </label>
            )}

            {children}

            {displayLabel && rawDescription && (
                <p className="text-sm text-gray-500">{rawDescription}</p>
            )}

            {rawErrors.length > 0 && (
                <ul className="list-disc list-inside text-sm text-red-500">
                    {rawErrors.map((error, i) => (
                        <li key={i}>{error}</li>
                    ))}
                </ul>
            )}

            {rawHelp && (
                <p className="text-sm text-gray-500">{rawHelp}</p>
            )}
        </div>
    )
}
