import Form from '@rjsf/core'
import validator from '@rjsf/validator-ajv8'
import { FieldTemplate } from '../Template/FieldTemplate'
import { SubmitButton } from '../widgets/SubmitButton'
import { TextWidget } from '../widgets/TextWidget'
import { SelectWidget } from '../widgets/SelectWidget'
import { SwitcherWidget } from '../widgets/SwitcherWidget'
import { FileUploadWidget } from '../widgets/FileUploadWidget'
import { RadioWidget } from '../widgets/RadioWidget'
import { useTranslation } from 'react-i18next'

const customTheme = {
  templates: {
    FieldTemplate,
    ButtonTemplates: {
      SubmitButton,
    },
  },
  widgets: {
    TextWidget,
    SelectWidget,
    SwitcherWidget,
    FileUploadWidget,
    radio: RadioWidget,
  },
}

export const CustomForm = (props: any) => {
  const { t } = useTranslation()

  return (
    <Form
      {...props}
      showErrorList={false}
      validator={validator}
      templates={customTheme.templates}
      widgets={customTheme.widgets}
      experimental_defaultFormStateBehavior={{
        allOf: 'populateDefaults',
      }}
      noHtml5Validate
      transformErrors={(errors) => {
        const modfiedErrors = errors?.map((err) => {
          if (
            err.name === 'required' ||
            err.name === 'minItems' ||
            err.name === 'type'
          ) {
            return { ...err, message: t('field.required') }
          }
          if (err.name === 'enum') {
            return {
              ...err,
              message: t('field.pleaseSelectFromExistingValues'),
            }
          }
          if (err.name === 'if') {
            return {
              ...err,
              message: '',
            }
          }
          return err
        })
        return modfiedErrors
      }}
    />
  )
}
