import { withTheme } from '@rjsf/core';
import validator from '@rjsf/validator-ajv8';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem } from '@/components/ui/select';
import { SelectTrigger, SelectValue } from '@radix-ui/react-select';
import { customWidgets } from '../RjsfTatabaForm/widgets';
import { ArrayFieldTemplate, FieldTemplate } from '../RjsfTatabaForm/templates';
import DatePicker from "react-multi-date-picker"
import TimePicker from "react-multi-date-picker/plugins/time_picker";
import arabic_ar from "react-date-object/locales/arabic_ar"
import arabic from "react-date-object/calendars/arabic"
import "react-multi-date-picker/styles/backgrounds/bg-dark.css"
import "react-multi-date-picker/styles/layouts/mobile.css"
import { useTranslation } from 'react-i18next';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { DateObject } from "react-multi-date-picker";


const Theme = {
    widgets: {
        // Text Widget using shadcn Input
        TextWidget: ({ value, onChange, disabled, schema, ...props }: any) => {
            return (
                <Input
                    required={props.required}
                    type={schema.type === 'integer' || schema.type === 'float' || schema.type === 'number' ? 'number' : schema.type === 'date' ? 'date' : 'text'}
                    value={value || ''}
                    disabled={disabled}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={props.label}
                    className={`no-border-focus rtl:text-right ltr:text-left ${props.error ? 'border-red-500' : ''}`}
                    step={schema.type === 'float' ? '0.01' : '1'}
                />
            )
        }
        ,

        // TextArea Widget
        TextareaWidget: ({ value, onChange, disabled, uiSchema, ...props }: any) => (
            <Textarea
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className={`min-h-[100px] no-border-focus rtl:text-right ltr:text-left ${props.error ? 'border-red-500' : ''}`}
                uiSchema={uiSchema}
                {...props}
            />
        ),

        // Number Widget with special handling
        NumberWidget: ({ value, onChange, ...props }: any) => (
            <Input
                type="number"
                value={value || ''}
                onChange={(e) => onChange(e.target.valueAsNumber)}
                className={`no-border-focus rtl:text-right ltr:text-left ${props.error ? 'border-red-500' : ''}`}
                {...props}
            />
        ),

        // Checkbox Widget with shadcn style
        CheckboxWidget:
            ({ value, onChange, label, disabled, required, id, ...props }: any) => {
                const { t, i18n } = useTranslation();

                return (
                    <div >
                        {label && (
                            <Label
                                htmlFor={id}
                                className={`${i18n.dir()} ${required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ""}`}
                            >
                                {label}
                            </Label>
                        )}
                        <Select
                            value={value === undefined ? "" : value ? "true" : "false"}
                            onValueChange={(val) => onChange(val === "true")}
                            disabled={disabled}
                            dir={i18n.dir()}
                        >
                            <SelectTrigger className={`w-full ${i18n.dir()}`}>
                                <SelectValue placeholder={t('selectValue')} />
                            </SelectTrigger>
                            <SelectContent className={`${i18n.dir()}`}>
                                <SelectItem value="true">{t('buttons.yes')}</SelectItem>
                                <SelectItem value="false">{t('buttons.no')}</SelectItem>
                            </SelectContent>
                        </Select>
                        {props.error && (
                            <p className="text-sm text-red-500 mt-1">{props.error}</p>
                        )}
                    </div>
                );
            },

        // Select Widget with shadcn Select
        SelectWidget: ({ options, value, onChange, disabled }: any) => {
            const { t, i18n } = useTranslation();

            return (
                <Select
                    value={value ?? ''}
                    onValueChange={onChange}
                    disabled={disabled}
                >
                    <SelectTrigger className={`w-full ${i18n.dir()}`}>
                        <SelectValue placeholder={t('selectValue')} />
                    </SelectTrigger>
                    <SelectContent>
                        {options.enumOptions.map(({ value, label }: any) => (
                            <SelectItem key={value} value={value || '_empty'}>
                                {label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            )
        },

        // Radio Widget with shadcn RadioGroup
        RadioWidget: ({ options, value, onChange, disabled, }: any) => (
            <RadioGroup
                value={value || ''}
                onValueChange={onChange}
                disabled={disabled}
                className="flex flex-col space-y-1"
            >
                {options.enumOptions.map(({ value, label }: any) => (
                    <div key={value} className="flex items-center space-x-2">
                        <RadioGroupItem value={value} id={value} />
                        <label htmlFor={value} className="text-sm font-medium leading-none">
                            {label}
                        </label>
                    </div>
                ))}
            </RadioGroup>
        ),

        DateWidget: ({ value, onChange }: any) => {

            // Create state to track current date value
            const [hijriDate, setHijriDate] = useState(() => {
                if (value) {
                    // If value is a string ISO date, create a new DateObject with Gregorian calendar
                    // and then convert it to Hijri (Arabic) calendar
                    try {
                        // Create a DateObject with gregorian calendar first
                        const gregorianDate = new DateObject({
                            date: value,
                            format: "YYYY-MM-DD"
                        });

                        // Then convert to Arabic/Hijri calendar
                        return new DateObject({
                            date: gregorianDate.toDate(),
                            calendar: arabic,
                            locale: arabic_ar
                        });
                    } catch (error) {
                        console.error("Error converting date:", error);
                        return null;
                    }
                }
                return null;
            });

            return (
                <DatePicker
                    value={hijriDate}
                    onChange={(dateObject: any) => {

                        if (dateObject) {
                            // Update local state with the hijri date object
                            setHijriDate(dateObject);

                            // Convert Hijri to Gregorian using the same approach as initialization
                            // Create a new DateObject with gregorian calendar
                            const gregorianDate = new DateObject({
                                date: dateObject.toDate(),
                                format: "YYYY-MM-DD"
                            });

                            // Format as ISO string for form data
                            const isoString = gregorianDate.format("YYYY-MM-DD");

                            // Pass the ISO string to form
                            onChange(isoString);
                        } else {
                            setHijriDate(null);
                            onChange(undefined);
                        }
                    }}
                    calendar={arabic}
                    locale={arabic_ar}
                    calendarPosition="bottom-right"
                    format="YYYY/MM/DD"
                    className="no-border-focus"
                    inputClass="w-full rounded-md border border-input px-3 py-2 rtl:text-right ltr:text-left"
                    // Disable all calendar switching options
                    hideOnScroll={false}
                    fixMainPosition={true}
                    // Ensure editable is false to prevent manual input issues
                    editable={false}
                    weekDays={["سبت", "أحد", "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة"]}
                />
            );
        },

        DateTimeWidget: ({ value, onChange }: any) => {
            // Create state to track current date value
            const [hijriDate, setHijriDate] = useState(() => {
                if (value) {
                    try {
                        // Convert Gregorian to Hijri
                        return new DateObject({
                            date: new Date(value),
                            calendar: arabic,
                            locale: arabic_ar
                        });
                    } catch (error) {
                        console.error("Error converting date:", error);
                        return null;
                    }
                }
                return null;
            });
            return (
                <DatePicker
                    value={hijriDate}
                    onChange={(dateObject: any) => {
                        if (dateObject) {
                            // Update local state with the hijri date object
                            setHijriDate(dateObject);

                            // Convert to Gregorian date and format as ISO string
                            const gregorianDate = dateObject.convert().toDate();
                            const isoString = gregorianDate.toISOString();

                            onChange(isoString);
                        } else {
                            setHijriDate(null);
                            onChange(undefined);
                        }
                    }}
                    calendar={arabic}
                    locale={arabic_ar}
                    calendarPosition="bottom-right"
                    format="YYYY/MM/DD HH:mm:ss"
                    className="no-border-focus"
                    inputClass="w-full rounded-md border border-input px-3 py-2 rtl:text-right ltr:text-left"
                    plugins={[
                        <TimePicker format="HH:mm" />
                    ]}
                    editable={false}
                    arrow={false}
                    hideOnScroll={false}
                    fixMainPosition={true}
                />
            );
        }
    },

    templates: {
        ObjectFieldTemplate: ({ title, description, properties }: any) => (
            <div className="space-y-4">
                {title && (
                    <h3 className="text-lg font-medium">{title}</h3>
                )}
                {
                    description && (
                        <p className="text-sm text-muted-foreground">{description}</p>
                    )
                }
                <div className="space-y-4">
                    {properties.map((prop: any) => prop.content)}
                </div>
            </div >
        ),
    }
};

export const ThemedForm = withTheme(Theme as any);

export const CustomThemedForm = (props: any) => {
    const { t } = useTranslation();

    return (
        <ThemedForm
            id="root_form"
            {...props}
            templates={{
                ArrayFieldTemplate,
                FieldTemplate,
                ...props.templates
            }}
            widgets={{ ...customWidgets, ...props.widgets }}
            showErrorList={false}
            validator={validator}
            experimental_defaultFormStateBehavior={{
                allOf: "populateDefaults",
            }}
            noHtml5Validate
            liveValidate={false}
            omitExtraErrors={true}
            onSubmit={(e: any) => {
                if (props.onSubmit) {
                    props.onSubmit(e);
                }
            }}
            formContext={{
                ...props.formContext,
                preventEnterSubmit: true
            }}
            transformErrors={(errors) => {
                const modfiedErrors = errors?.map((err) => {
                    if (
                        err.name === "required" ||
                        err.name === "minItems" ||
                        err.name === "type"
                    ) {
                        return { ...err, message: t('field.required') };
                    }
                    if (err.name === "enum") {
                        return {
                            ...err,
                            message: t('field.pleaseSelectFromExistingValues')
                        };
                    }
                    if (err.name === "if") {
                        return {
                            ...err,
                            message: "",
                        };
                    }
                    return err;
                });
                return modfiedErrors;
            }}
        >
            {props.children}
        </ThemedForm>
    );
};