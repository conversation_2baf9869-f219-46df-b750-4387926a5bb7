import { FieldTemplateProps } from '@rjsf/utils';
import { useTranslation } from 'react-i18next';

export const FieldTemplate: React.FC<FieldTemplateProps> = ({ id, label, required, children, rawErrors, uiSchema, schema }) => {
    const { t, i18n } = useTranslation();
    const customClassNames = uiSchema?.["ui:classNames"] || "";
    const index = schema?.['ui:options']?.index || '';

    const direction = i18n.dir();

    return (
        <div className={`mb-4 ${direction} ${customClassNames}`}>
            {label && (
                <label htmlFor={id} className="my-2 block text-sm font-medium text-gray-700">
                    {t(label)} {index}{required && <span className="text-red-500">*</span>}
                </label>
            )}
            {children}
            {rawErrors && (
                <ul className="my-1 text-red-600 text-sm">
                    {rawErrors.map((error, index) => (
                        <li key={index}>{t(label)} {error}</li>
                    ))}
                </ul>
            )}
        </div>
    );
}
