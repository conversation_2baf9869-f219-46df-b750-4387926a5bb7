

export const CustomFieldTemplate: React.FC<any> = (props) => {
    const { id, label, required, description, errors, children } = props
    console.log({ props, children, label, description });

    return (
        <div className="mb-8">
            <div className={label === 'رفع الملف' ? '' : 'flex gap-1'}>
                <label htmlFor={id} className="font-medium text-gray-700 mb-1">
                    {label} {required && <span className="text-red-500">*</span>}
                </label>
                {description && <p className="text-sm text-gray-500 mb-1">{description}</p>}
                {children}
            </div>
            {errors && <div className="text-red-500 text-sm">{errors}</div>}
        </div>
    )
}