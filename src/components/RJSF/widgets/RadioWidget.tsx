
import { WidgetProps } from "@rjsf/utils";
import { useTranslation } from "react-i18next";

export const RadioWidget: React.FC<WidgetProps> = ({ value, onChange, options, schema }) => {
    const { t, i18n } = useTranslation()
    const direction = i18n.dir()
    const enumOptions = schema.enumOptions || options.enumOptions || [];

    return (
        <div className="space-y-4" dir={direction}>
            {enumOptions.map((option: any) => {
                return (
                    <label
                        key={option.value}
                        className={`flex items-start gap-2 p-4 rounded-lg cursor-pointer transition-all duration-200 
                    ${value === option.value ? 'bg-gray-50 border-2 border-[#5E58EE]' : 'bg-[#F5F7F9] border-2 border-gray-200 hover:border-gray-300'}`}
                    >
                        <div className="flex items-center space-x-3">
                            <input
                                type="radio"
                                className="hidden"
                                value={option.value}
                                checked={value === option.value}
                                onChange={() => onChange(option.value)}
                                id={`radio-${option.value}`}
                            />
                            <div
                                className={`w-5 h-5 flex items-center justify-center border-2 rounded-full transition-colors 
                            ${value === option.value ? 'border-[#5E58EE]' : 'border-gray-300'}`}
                            >
                                {value === option.value && (
                                    <div className="w-2.5 h-2.5 bg-[#5E58EE] rounded-full"></div>
                                )}
                            </div>
                        </div>
                        <div className="flex flex-col items-start">
                            <span className="text-sm font-medium text-gray-700">
                                {t(option.label) || t(option.value)}
                            </span>
                            {option?.hint && (
                                <p className="text-xs text-gray-500 mt-1">
                                    {t(option?.hint)}
                                </p>
                            )}
                        </div>
                    </label>
                )
            })}
        </div>
    );
};
