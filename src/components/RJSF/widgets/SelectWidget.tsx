
import { WidgetProps } from "@rjsf/utils";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useTranslation } from "react-i18next";

export const SelectWidget: React.FC<WidgetProps> = ({ value, onChange, options, label, formContext }) => {
  const { i18n, t } = useTranslation()
  const direction = i18n.dir()
  const { columnSamples, selectedColumns = [] } = formContext;

  // Filter out already selected columns from options
  const availableOptions = options.enumOptions?.filter(
    option => !selectedColumns.includes(option.value) || option.value === value
  ) || [];

  const renderSampleValue = (sample: any) => {
    if (sample && typeof sample === 'object') {
      return (
        <div className="flex flex-col space-y-1">
          {Object.entries(sample).map(([key, value]) => (
            <div key={key} className="flex items-start">
              <span className="text-gray-500 font-medium min-w-[100px]">{key}:</span>
              <span className="text-gray-600">
                {typeof value === 'object' ? JSON.stringify(value) : value?.toString()}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return sample?.toString();
  };

  return (
    <>
      <Select value={value} onValueChange={(selectedValue) => onChange(selectedValue)} dir={direction}>
        <SelectTrigger className="no-border-focus">
          <SelectValue placeholder={t(label)} />
        </SelectTrigger>
        <SelectContent>
          {availableOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label || option.value}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {value && columnSamples[value] && (
        <div className="mt-2 text-sm text-gray-600 max-h-[30vh] overflow-auto">
          <div>{t('sampleData')}</div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <tbody>
                {columnSamples[value].map((sample: any, index: number) => (
                  <tr key={index} className="border-b">
                    <td className="py-2 px-3 bg-gray-50">
                      {renderSampleValue(sample)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </>
  );
};