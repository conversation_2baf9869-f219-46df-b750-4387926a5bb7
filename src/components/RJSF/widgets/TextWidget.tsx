import { Input } from '@/components/ui/input';
import { WidgetProps } from '@rjsf/utils';

export const TextWidget: React.FC<WidgetProps> = ({ id, value, onChange, placeholder }) => (
    <Input
        id={id}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="block w-full border-gray-300 rounded-md bg-#F5F7F9 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
    />
);

