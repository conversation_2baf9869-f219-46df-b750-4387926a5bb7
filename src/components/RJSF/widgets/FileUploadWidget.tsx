import { showToast } from '@/shared/utils/toastConfig';
import { WidgetProps } from '@rjsf/utils';
import { UploadCloud, Trash2, Loader } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const FileUploadWidget: React.FC<WidgetProps> = ({ onChange, label, formContext,
    value }) => {
    const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const { t } = useTranslation();

    const allowedExtensions = ['.kml', '.geojson', '.csv', '.gpkg'];

    const validateFileExtension = (fileName: string): boolean => {
        const extension = fileName.toLowerCase().slice(fileName.lastIndexOf('.'));
        return allowedExtensions.includes(extension);
    };

    const handleFileChange = useCallback(
        async (event: React.ChangeEvent<HTMLInputElement>) => {
            const onFileChange = formContext.onFileChange
            const file = event.target.files?.[0];

            if (file) {
                if (!validateFileExtension(file.name)) {
                    showToast.error(t('workspace.fileUpload.errors.unsupportedType'));
                    event.target.value = '';
                    return;
                }

                setLoading(true);
                try {
                    const response = await onFileChange(file);
                    const reader = new FileReader();
                    reader.onload = () => {
                        onChange(response);
                        setUploadedFileName(file.name);
                        setLoading(false);
                        if (response) {
                            showToast.success(t('workspace.fileUpload.success'));
                        } else {
                            setUploadedFileName(null);
                            handleRemoveFile()
                        }
                    };
                    reader.readAsDataURL(file);
                } catch (error) {
                    setLoading(false);
                    console.error(error);
                    showToast.error(t('workspace.fileUpload.errors.uploadFailed'));
                }
            }
        },
        [onChange]
    );
    const handleRemoveFile = useCallback(() => {
        onChange(null); // Clear the file data
        setUploadedFileName(null); // Reset the uploaded file name
    }, [onChange]);

    return (
        <div className="max-w-lg p-4 border-1 border-[#F5F7F9] rounded-md text-center bg-[#F5F7F9]">
            {loading ? (
                <div className="flex items-center justify-center space-x-2">
                    <Loader className="animate-spin h-5 w-5 text-gray-500" />
                    <span className="text-gray-500 text-sm">{t('workspace.fileUpload.processing')}</span>

                </div>
            ) : uploadedFileName ? (
                <div className="flex items-center justify-between bg-[#F5F7F9] px-4 py-2 rounded-md">
                    <span className="text-gray-600 text-sm">{uploadedFileName}</span>
                    <button onClick={handleRemoveFile} className="text-red-500">
                        <Trash2 className="h-5 w-5" />
                    </button>
                </div>
            ) : value ?
                <p className="text-xs text-start break-words whitespace-normal "> {value} </p> : (
                    <label htmlFor="file-upload" className="cursor-pointer">
                        <div className="flex flex-col items-center justify-center space-y-2">
                            <UploadCloud className="mx-auto h-8 w-8 text-[#8C8C8C]" />
                            <span className="text-gray-600">{t(label)}</span>
                            <p className="text-xs text-gray-400">
                                {t('workspace.fileUpload.supportedFormats')}
                            </p>
                        </div>
                    </label>
                )}
            <input
                id="file-upload"
                type="file"
                accept=".kml,.geojson,.csv,.gpkg"
                onChange={handleFileChange}
                style={{ display: 'none' }}
            />
        </div>
    );
};
