import { Switch } from '@/components/ui/switch';
import { WidgetProps } from '@rjsf/utils';

export const SwitcherWidget: React.FC<WidgetProps> = ({ value, onChange }) => {
  return (
    <label className="flex">
      <Switch
        checked={value || false}
        onCheckedChange={(checked) => onChange(checked)}
        className="bg-gray-300 data-[state=checked]:bg-[#5E58EE] transition-colors ltr"
      />
    </label>
  );
};

