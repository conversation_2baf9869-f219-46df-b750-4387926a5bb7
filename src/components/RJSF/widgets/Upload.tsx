import { Button } from "@/components/ui/button";
import { showToast } from '@/shared/utils/toastConfig';
import { WidgetProps } from '@rjsf/utils';
import { Loader } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useTranslation } from "react-i18next";

export const ImageUploadWidget: React.FC<WidgetProps> = ({
    id,
    onChange,
    value,
    formContext,
    required,
    placeholder
}) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState<boolean>(false);

    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

    const validateFileExtension = (fileName: string): boolean => {
        const extension = fileName.toLowerCase().slice(fileName.lastIndexOf('.'));
        return allowedExtensions.includes(extension);
    };

    const handleFileChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const onFileChange = formContext.onFileChange;

        if (file) {
            if (!validateFileExtension(file.name)) {
                showToast.error('Please upload a valid image file (JPG, PNG, GIF, WEBP)');
                event.target.value = '';
                return;
            }

            setLoading(true);
            try {
                const signedUrl = await onFileChange(file);
                onChange(signedUrl);
                setLoading(false);
                showToast.success('Image uploaded successfully');
            } catch (error) {
                console.error(error);

                setLoading(false);
                showToast.error('Error uploading image. Please try again.');
            }
        }
    }, [onChange, formContext]);

    return (
        <div className="space-y-2">
            <div className="flex items-center space-x-2">
                <input
                    id={id}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                    required={required}
                />
                <Button
                    variant="outline"
                    className="w-32"
                    asChild
                    disabled={loading}
                >
                    <label htmlFor={id} className="cursor-pointer">
                        {loading ? (
                            <Loader className="animate-spin h-4 w-4" />
                        ) : (
                            t('browse')
                        )}
                    </label>
                </Button>
                <span className="text-sm text-muted-foreground">
                    {value || placeholder || 'No image selected'}
                </span>
            </div>
            {value && (
                <div className="mt-2">
                    <img
                        src={value}
                        alt="Uploaded preview"
                        className="max-w-[200px] rounded-md"
                    />
                </div>
            )}
        </div>
    );
};
