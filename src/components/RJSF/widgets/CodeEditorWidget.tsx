import { useEffect, useState, useRef } from "react";
import { WidgetProps } from "@rjsf/utils";
import Editor from "@monaco-editor/react";
import { FormPreview } from "@/components/FormEditorPreview/RJSFFormPreview";
import { <PERSON><PERSON>, <PERSON>Up } from "lucide-react";

const tabs = ["JSON Schema", "UI Schema", "FormData"];

export const CodeEditorWidget = ({
    onChange, formContext
}: WidgetProps) => {
    const initialSchemas = formContext.initialSchemas

    const [activeTab, setActiveTab] = useState("JSON Schema");
    const [formData, setFormData] = useState(JSON.stringify('{}'));
    const [uiSchema, setUiSchema] = useState(JSON.stringify('{}'));
    const [jsonSchema, setJsonSchema] = useState(JSON.stringify('{}'));
    const [previewError, setPreviewError] = useState<string | null>(null);
    const [copySuccess, setCopySuccess] = useState<string | null>(null);
    const [showScrollTop, setShowScrollTop] = useState(false);
    const editorRef = useRef<any>(null);

    useEffect(() => {
        setFormData(JSON.stringify(initialSchemas.formData, null, 2));
        setUiSchema(JSON.stringify(initialSchemas.uiSchema, null, 2));
        setJsonSchema(JSON.stringify(initialSchemas.jsonSchema, null, 2));
    }, [formContext, initialSchemas]);

    useEffect(() => {
        // Clear copy success message after 2 seconds
        if (copySuccess) {
            const timer = setTimeout(() => {
                setCopySuccess(null);
            }, 2000);
            return () => clearTimeout(timer);
        }
    }, [copySuccess]);

    const getEditorValue = () => {
        switch (activeTab) {
            case "FormData": return formData;
            case "UI Schema": return uiSchema;
            case "JSON Schema": return jsonSchema;
            default: return "{}";
        }
    };

    const handleEditorChange = (value: string | undefined) => {
        if (!value) return;
        try {
            JSON.parse(value);
            setPreviewError(null);

            switch (activeTab) {
                case "FormData":
                    setFormData(value);
                    break;
                case "UI Schema":
                    setUiSchema(value);
                    break;
                case "JSON Schema":
                    setJsonSchema(value);
                    break;
            }

            onChange(value);
        } catch (error) {
            console.error("Invalid JSON format", error);
            setPreviewError("Invalid JSON format");
        }
    };

    const handleCopyToClipboard = () => {
        const textToCopy = getEditorValue();
        navigator.clipboard.writeText(textToCopy)
            .then(() => {
                setCopySuccess(`${activeTab} copied!`);
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
                setCopySuccess('Copy failed');
            });
    };

    /** Initializes Monaco editor and sets up scroll-to-top button logic. */
    const handleEditorDidMount = (editor: any) => {
        editorRef.current = editor;

        // Add scroll listener to show/hide scroll-to-top button
        editor.onDidScrollChange(() => {
            const scrollTop = editor.getScrollTop();
            setShowScrollTop(scrollTop > 100);
        });

        // Check initial content height
        const contentHeight = editor.getContentHeight();
        const viewportHeight = editor.getLayoutInfo().height;
        setShowScrollTop(contentHeight > viewportHeight);
    };

    /** Scrolls editor view back to top of JSON document. */
    const scrollToTop = () => {
        if (editorRef.current) {
            editorRef.current.setScrollPosition({ scrollTop: 0 });
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 ltr">
            <div className="flex items-center justify-between p-2 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                <div className="flex items-center gap-1">
                    {tabs.map((tab) => (
                        <button
                            key={tab}
                            onClick={() => setActiveTab(tab)}
                            className={`ltr px-4 py-1.5 text-sm font-medium rounded-md transition-colors
                            ${activeTab === tab
                                    ? "bg-white text-gray-900 shadow-sm"
                                    : "text-gray-600 hover:bg-gray-100"
                                }`}
                        >
                            {tab}
                        </button>
                    ))}
                </div>
                <div className="flex items-center">
                    {copySuccess && (
                        <span className="text-green-600 text-sm mr-2">{copySuccess}</span>
                    )}
                    <button
                        onClick={handleCopyToClipboard}
                        className="flex items-center gap-1 px-3 py-1.5 text-sm font-medium rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                        title={`Copy ${activeTab}`}
                    >
                        <Copy className="h-5 w-5 text-gray-700" />
                    </button>
                </div>
            </div>
            <div className="grid grid-cols-2 gap-4 p-4 max-h-[80vh]">
                <div className="border rounded-lg w-full ltr min-h-[400px] relative">
                    <Editor
                        height="100%"
                        defaultLanguage="json"
                        theme="vs-light"
                        value={getEditorValue()}
                        onChange={handleEditorChange}
                        onMount={handleEditorDidMount}
                        options={{
                            minimap: { enabled: false },
                            fontSize: 14,
                            lineNumbers: "on",
                            roundedSelection: false,
                            scrollBeyondLastLine: false,
                            scrollBeyondLastColumn: 0,
                            padding: { top: 10, bottom: 20 },
                            readOnly: false,
                            automaticLayout: true,
                            formatOnPaste: true,
                            formatOnType: true,
                        }}
                    />
                    {showScrollTop && (
                        <button
                            onClick={scrollToTop}
                            className="absolute bottom-4 right-5 p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                            title="Scroll to top"
                        >
                            <ArrowUp className="h-5 w-5 text-gray-700" />
                        </button>
                    )}
                </div>
                <div className="border rounded-lg p-4 h-[600px] flex flex-col">
                    <h3 className="text-sm font-medium text-gray-700 mb-4">Live Preview</h3>
                    <div className="flex-grow min-h-0">
                        {previewError ? (
                            <div className="p-4 bg-red-50 text-red-600 rounded-lg">
                                {previewError}
                            </div>
                        ) : (
                            <FormPreview
                                jsonSchema={jsonSchema}
                                uiSchema={uiSchema}
                                formData={formData}
                                onFormDataChange={(newFormData: any) =>
                                    setFormData(JSON.stringify(newFormData, null, 2))
                                }
                                onSkip={formContext.onSkip}
                                onSave={formContext.onSave}
                                onCancel={formContext.onCancel}
                                mutationLoading={formContext.mutationLoading}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};