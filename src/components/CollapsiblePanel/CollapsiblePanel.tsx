import React from 'react';
import { Panel } from "react-resizable-panels";
import { Button } from "@/components/ui/button";
import { Minimize2, Maximize2 } from "lucide-react";
import { ImperativePanelHandle } from "react-resizable-panels";
import { ThumbnailUploadButton } from '../Map/components/ThumbnailUploadButton/ThumbnailUploadButton';

interface CollapsiblePanelProps {
    title: string;
    isCollapsed: boolean;
    onToggle: () => void;
    panelRef: React.RefObject<ImperativePanelHandle>;
    defaultSize: number;
    minSize?: number
    children: React.ReactNode;
    icon?: any
}

const COLLAPSED_WIDTH = 32;

export const CollapsiblePanel: React.FC<CollapsiblePanelProps> = ({
    title,
    isCollapsed,
    onToggle,
    panelRef,
    defaultSize,
    minSize = 10,
    children, icon
}) => {
    return (
        <Panel
            ref={panelRef}
            defaultSize={defaultSize}
            minSize={minSize}
            collapsible={true}
            onCollapse={() => onToggle()}
            onExpand={() => onToggle()}
            style={isCollapsed
                ? { maxWidth: `${COLLAPSED_WIDTH}px`, minWidth: `${COLLAPSED_WIDTH}px`, width: `${COLLAPSED_WIDTH}px` }
                : {}}
            className={`relative ${title === 'Layers' || title === 'الطبقات' ? 'hidden' : 'block'}`}
        >
            {/* Collapsed state with title and maximize button */}
            <div
                className={`absolute top-0 left-0 w-8 h-full flex flex-col items-center justify-between py-2 cursor-pointer z-10 border-l border-gray-200 ${isCollapsed ? "visible" : "invisible"}`}
                onClick={onToggle}
            >
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                        e.stopPropagation();
                        onToggle();
                    }}
                    aria-label="Expand panel"
                    className="p-1 h-auto"
                >
                    <Maximize2 className="h-4 w-4" />
                </Button>
                <div
                    className="flex items-center justify-center flex-grow bg-white"
                    style={{ writingMode: "vertical-rl", textOrientation: "mixed", transform: "rotate(180deg)" }}
                >
                    <span className="text-sm font-medium">{title}</span>
                </div>
            </div>

            {/* Expanded state */}
            <div
                className={`flex flex-col h-full border-l border-gray-200 ${isCollapsed ? "invisible absolute" : "visible"}`}
                style={{ display: isCollapsed ? "none" : "flex" }}
            >
                <div className="flex items-center justify-between p-2 border-b border-gray-200">
                    <h3 className="font-semibold truncate">{title} </h3>
                    <div className='flex items-center justify-center'>
                        {icon && <ThumbnailUploadButton />}
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onToggle}
                            aria-label="Collapse panel"
                            className="p-1 h-auto"
                        >
                            <Minimize2 className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
                <div className="flex-grow overflow-hidden">
                    {children}
                </div>
            </div>
        </Panel>
    );
};