import { useState, useRef, useEffect } from 'react'
import { <PERSON>, CardHeader, CardTitle } from '@/components/ui/card'
import { Ellipsis, Trash, Calendar, Clock } from 'lucide-react'
import { Routes } from '@/shared/utils/routes'
import workspaceImg from '@/assets/workspase.webp'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '../ui/input'
import { showToast } from '@/shared/utils/toastConfig'
import { DeleteAlert } from './DeleteAlert'
import { useDispatch } from 'react-redux'
import { updateWorkspace, Workspace } from '@/shared/store/slices/workspaceSlice'
import { UPDATE_WORKSPACE } from '@/shared/graphQl/mutations/layers'
import { useMutation } from '@apollo/client'
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager'
import { useTranslation } from 'react-i18next'
import {
  formatDate,
  formatNumber,
  getFormattedSince,
} from '@/shared/utils/getRelativeTimeString'

interface WorkspaceCardProps {
  item: Workspace
  view: 'grid' | 'list'
}

export const WorkspaceCard: React.FC<WorkspaceCardProps> = ({ item, view }) => {
  const { t, i18n } = useTranslation()
  const direction = i18n.dir()
  const dispatch = useDispatch()
  const [isEditing, setIsEditing] = useState(false)
  const [newName, setNewName] = useState(item.name)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [showDeleteAlert, setShowDeleteAlert] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const [updateWorkspaceName] = useMutation(UPDATE_WORKSPACE)

  const { selectedOrg, navigateWithOrg } = useOrganizationManager()
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isEditing])

  const handleDeleteClick = () => {
    setDropdownOpen(false)
    setShowDeleteAlert(true)
  }

  const handleNameUpdate = async (force?: boolean) => {
    if (!force && dropdownOpen) return

    // Trim the new name once to reuse
    const trimmedNewName = newName.trim()

    if (trimmedNewName === '') {
      showToast.error(t('alerts.error.emptyName'))
      return
    }

    // Check if name actually changed
    if (trimmedNewName === item.name) {
      setIsEditing(false)
      return
    }

    await updateWorkspaceName({
      variables: {
        dataInput: {
          workspaceId: parseInt(item.id),
          name: trimmedNewName,
          orgId: selectedOrg?.id,
        },
      },
    })
      .then(() => {
        dispatch(
          updateWorkspace({
            id: item.id,
            updates: { name: trimmedNewName },
          })
        )
        showToast.success(t('alerts.success.nameUpdated'))
      })
      .catch(() => showToast.error(t('alerts.error.updateError')))

    setIsEditing(false)
  }

  const startEditing = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsEditing(true)
  }

  const handleCardClick = () => {
    if (!dropdownOpen && !isEditing && !showDeleteAlert) {
      localStorage.setItem('currentWorkspaceTitle', item.name)
      navigateWithOrg(`/${Routes.map}/${item.id}?type=${item.workspaceType}`, { newTab: true })
    }
  }

  const handleCloseDeleteAlert = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation()
    setShowDeleteAlert(false)
  }

  const layersCount = item?.layersData?.layersCount ?? 0
  const recordsCount = item?.layersData?.recordsCount ?? 0
  const formattedSince = getFormattedSince(item?.since)

  return (
    <Card
      className={`group relative bg-white border border-[#E1E1E1] rounded-lg overflow-hidden
                ${view === 'list' ? 'flex h-fit' : 'flex flex-col h-full'}`}
      onClick={handleCardClick}
    >
      <CardHeader
        className={`cursor-pointer p-2 bg-white
                    ${view === 'list' ? 'w-40 h-auto' : 'w-full h-64'}`}
      >
        <img
          src={item.thumbnail ?? workspaceImg}
          alt={item.name}
          className={`w-full h-full object-cover rounded-sm`}
          loading={item.thumbnail ? 'lazy' : 'eager'}
          decoding="async"
        />
      </CardHeader>

      <div className="flex-grow flex flex-col p-4">
        {/* Top metadata row */}
        <div className="flex justify-between items-center text-sm font-normal mb-3">
          <div className="flex items-center gap-2">
            <span className="flex items-center gap-1">
              {formatNumber(layersCount)} {t('workspace.card.layers', { count: layersCount })}
            </span>
            <span className="text-gray-300">|</span>
            <span>
              {formatNumber(recordsCount)} {t('workspace.card.rows')}
            </span>
          </div>
          <DropdownMenu
            open={dropdownOpen}
            onOpenChange={setDropdownOpen}
            dir={direction}
          >
            <DropdownMenuTrigger className="h-6 w-6 flex items-center justify-center rounded-full hover:bg-gray-50 no-border-focus">
              <Ellipsis className="h-5 w-5 text-gray-500 no-border-focus" />            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={startEditing} className="gap-2">
                <span>{t('common.rename')}</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDeleteClick}
                className="text-red-600 gap-2"
              >
                <span>{t('common.delete')}</span>
                <Trash className="h-4 w-4" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Title and description */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1 min-w-0">
            <CardTitle
              className="text-base font-semibold text-gray-900 truncate mb-1"
              onDoubleClick={startEditing}
            >
              {isEditing ? (
                <Input
                  ref={inputRef}
                  value={newName}
                  onClick={(e) => e.stopPropagation()}
                  onChange={(e) => setNewName(e.target.value)}
                  onBlur={() => handleNameUpdate()}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleNameUpdate(true)
                    if (e.key === 'Escape') {
                      setIsEditing(false)
                      setNewName(item.name)
                    }
                  }}
                  className="max-w-[200px] h-7 text-base font-semibold no-border-focus"
                />
              ) : (
                newName
              )}
            </CardTitle>
            <p className="text-sm font-normal text-gray-600 leading-relaxed line-clamp-2" title={item?.description}>
              {item.description || ' '}
            </p>
          </div>
        </div>

        {/* Bottom metadata - pushed to bottom */}
        <div className="mt-auto pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className={`flex items-center gap-4 ${direction}`}>
              {formattedSince && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{t('lastEdit')}</span>
                  <span className='font-medium text-gray-700'>{formattedSince}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{t('creationDate')}</span>
                <span className='font-medium text-gray-700'>{formatDate(item.created)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showDeleteAlert && (
        <div onClick={(e) => e.stopPropagation()}>
          <DeleteAlert
            workspaceId={item.id}
            workspaceName={item.name}
            onClose={handleCloseDeleteAlert}
          />
        </div>
      )}
    </Card>
  )
}