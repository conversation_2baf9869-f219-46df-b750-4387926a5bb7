import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Skeleton } from "../Skeleton/Skeleton"

interface WorkspaceCardSkeletonProps {
    view: "grid" | "list"
}

export const WorkspaceCardSkeleton: React.FC<WorkspaceCardSkeletonProps> = ({ view }) => {
    return (
        <Card className={view === "list" ? "flex" : ""}>
            <CardHeader className={`p-[.5rem]`}>
                <Skeleton className={`${view === "list" ? "h-[9rem] w-[10rem]" : "h-45 w-full"}`} />
            </CardHeader>
            <div className={view === "list" ? "flex-grow" : ""}>
                <CardContent className="p-3 pt-0">
                    <div className="flex justify-between items-center p-2">
                        <Skeleton className="h-6 w-[150px]" />
                    </div>
                    <Skeleton className="h-4 w-full mt-2" />
                </CardContent>
                <CardFooter>
                    <Skeleton className="h-4 w-[140px]" />
                </CardFooter>
            </div>
        </Card>
    )
}
