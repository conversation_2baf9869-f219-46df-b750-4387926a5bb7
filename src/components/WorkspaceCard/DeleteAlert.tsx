import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { showToast } from '@/shared/utils/toastConfig'
import { useMutation } from '@apollo/client'
import { DELETE_WORKSPACE } from '@/shared/graphQl/mutations'
import { useDispatch } from 'react-redux'
import { deleteWorkspace } from "@/shared/store/slices/workspaceSlice"
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager"
import { useTranslation } from "react-i18next"

interface DeleteAlertProps {
    workspaceId: string;
    workspaceName: string;
    onClose: () => void;
}

export const DeleteAlert: React.FC<DeleteAlertProps> = ({
    workspaceId,
    workspaceName,
    onClose
}) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const { selectedOrg } = useOrganizationManager()
    const [deleteWorkspaceFromBE] = useMutation(DELETE_WORKSPACE);
    const dispatch = useDispatch()
    const handleDelete = async () => {
        try {
            const { data } = await deleteWorkspaceFromBE({
                variables: {
                    dataInput: {
                        workspaceId: parseInt(workspaceId),
                        orgId: selectedOrg?.id
                    }
                }
            });
            if (data.deleteWorkspace.deleted) {
                dispatch(deleteWorkspace(workspaceId))
                showToast.success(t('alerts.success.workspaceDeleted'));
                onClose();
            }
        } catch (error) {
            showToast.error(t('alerts.error.workspaceDelete'));
            console.error(error);
        }
    };

    return (
        <AlertDialog open={true} onOpenChange={() => onClose()}>
            <AlertDialogContent className={`${direction}`}>
                <AlertDialogHeader className={`${direction}`}>
                    <AlertDialogTitle>{t('workspace.deleteConfirmTitle')}</AlertDialogTitle>
                    <AlertDialogDescription style={{ whiteSpace: 'pre-line' }}>
                        {t('workspace.deleteConfirmDescription', { name: workspaceName })}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className={`gap-2 ${direction}`}>
                    <AlertDialogCancel onClick={onClose} className="no-border-focus"> {t('common.cancel')} </AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                        {t('common.delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
