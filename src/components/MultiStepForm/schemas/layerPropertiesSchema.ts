export const layerPropertiesSchema = {
  type: 'object',
  properties: {
    layerName: {
      type: 'string',
      title: 'schema.stepOne.layerName.title',
    },
    layerDescription: {
      type: 'string',
      title: 'schema.stepOne.layerDescription.title',
    },
    layerColor: {
      type: 'string',
      title: 'schema.stepOne.layerColor.title',
      default: '#42FAC2',
    },
  },
  required: ['layerName'],
}

export const layerPropertiesUiSchema = {
  layerColor: {
    'ui:widget': 'color',
  },
}
