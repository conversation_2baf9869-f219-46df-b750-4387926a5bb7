import { FormField } from '../steps/designLayerSteps/LayerFormStep/types'

export const initialSchema = {
  type: 'object',
  properties: {
    field1: {
      type: 'string',
      title: 'الحقل 1',
    },
  },
  required: [],
}

export const initialFormFields: FormField[] = [
  {
    id: 'field1',
    name: 'field1',
    title: 'الحقل 1',
    type: 'string',
    required: false,
    isVisible: true,
  },
]
