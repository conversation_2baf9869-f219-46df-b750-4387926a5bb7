export const stepOneSchema = {
  type: 'object',
  properties: {
    layerName: {
      type: 'string',
      title: 'schema.stepOne.layerName.title',
    },
    layerDescription: {
      type: 'string',
      title: 'schema.stepOne.layerDescription.title',
    },
    layerColor: {
      type: 'string',
      title: 'schema.stepOne.layerColor.title',
      default: '#42FAC2',
    },
    showOnly: {
      type: 'boolean',
      title: 'schema.stepOne.showOnly.title',
      default: false,
    },
    fileUpload: {
      type: 'string',
      title: 'schema.stepOne.fileUpload.title',
      format: 'binary',
    },
  },
  required: ['layerName', 'fileUpload'],
}

export const stepOneUiSchema = {
  layerColor: {
    'ui:widget': 'color',
  },
  showOnly: {
    'ui:widget': 'SwitcherWidget',
    'ui:readonly': true,
  },
  fileUpload: {
    'ui:widget': 'FileUploadWidget',
  },
}

export const stepOneSchemaWithWorkspaceID = {
  type: 'object',
  properties: {
    layerName: {
      type: 'string',
      title: 'schema.stepOne.layerName.title',
    },
    layerColor: {
      type: 'string',
      title: 'schema.stepOne.layerColor.title',
      default: '#42FAC2',
    },
    showOnly: {
      type: 'boolean',
      title: 'schema.stepOne.showOnly.title',
      default: false,
    },
    fileUpload: {
      type: 'string',
      title: 'schema.stepOne.fileUpload.title',
      format: 'binary',
    },
  },
  required: ['layerName', 'fileUpload'],
}
