import { RJSFSchema } from '@rjsf/utils'

const createLongitudeProperty = (
  i: number,
  title: string = 'schema.stepFive.longitude.title'
) => ({
  type: 'string',
  title: `${title}`,
  enum: ['اختر...'],
  'ui:options': {
    index: i,
  },
})

const createLongitudeUiSchema = () => ({
  'ui:widget': 'SelectWidget',
  'ui:classNames': 'flex-1',
  'ui:options': {
    showSample: true,
  },
})

export const generateDynamicSchema = (columnCount: number) => {
  const properties: Record<string, RJSFSchema> = {}
  const finalCount = Math.min(columnCount, 6) // Limit to maximum 6 fields

  for (let i = 1; i <= finalCount; i++) {
    properties[`longitude${i}`] = createLongitudeProperty(i) as RJSFSchema
  }

  return {
    type: 'object',
    properties,
    required: ['longitude1'],
  }
}

export const generateDynamicUiSchema = (columnCount: number) => {
  const uiSchema: Record<string, any> = {
    'ui:direction': 'rtl',
    'ui:classNames': 'customRow',
  }

  const finalCount = Math.min(columnCount, 6)
  for (let i = 1; i <= finalCount; i++) {
    uiSchema[`longitude${i}`] = createLongitudeUiSchema()
  }

  return uiSchema
}
