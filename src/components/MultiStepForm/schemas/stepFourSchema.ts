import { CodeEditorWidget } from '@/components/RJSF/widgets/CodeEditorWidget'
import { RJSFSchema } from '@rjsf/utils'

export const schema: RJSFSchema = {
  type: 'object',
  required: ['code'],
  properties: {
    code: {
      type: 'string',
      title: 'schema.stepFour.code.title',
      default: '{}',
    },
  },
}

export const initialFormData = {
  code: JSON.stringify(
    {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          title: 'Name',
        },
        age: {
          type: 'number',
          title: 'Age',
        },
      },
    },
    null,
    2
  ),
}

const initialUiSchema = {
  name: {
    'ui:autofocus': true,
    'ui:placeholder': 'Enter your name',
  },
}

export const uiSchema = {
  code: {
    'ui:widget': CodeEditorWidget,
    'ui:options': {
      defaultValue: JSON.stringify(initialFormData, null, 2),
      defaultUiSchema: JSON.stringify(initialUiSchema, null, 2),
      defaultJsonSchema: JSON.stringify(initialFormData, null, 2),
    },
  },
}

export const widgets = {
  CodeEditorWidget,
}
