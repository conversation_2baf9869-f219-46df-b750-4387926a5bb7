export const schema = {
  type: 'object',
  properties: {
    indicatorType: {
      title: 'schema.stepThree.coordinateType.title',
      type: 'string',
      enumOptions: [
        {
          value: 'نقطة',
          label: 'schema.stepThree.coordinateType.point',
          hint: 'schema.stepThree.coordinateType.pointHint',
        },
        {
          value: 'أخرى',
          label: 'schema.stepThree.coordinateType.other',
          hint: 'schema.stepThree.coordinateType.otherHint',
        },
      ],
      default: 'نقطة',
    },
    columnLayout: {
      title: 'schema.stepThree.columnLayout.title',
      type: 'string',
      enumOptions: [
        {
          value: 'عمود',
          label: 'schema.stepThree.columnLayout.singleColumn',
          hint: 'schema.stepThree.columnLayout.singleColumnHint',
        },
        {
          value: 'عمودين',
          label: 'schema.stepThree.columnLayout.twoColumns',
          hint: 'schema.stepThree.columnLayout.twoColumnsHint',
        },
      ],
      default: 'عمود',
    },
  },
  dependencies: {
    indicatorType: {
      oneOf: [
        {
          properties: {
            indicatorType: { const: 'نقطة' },
            columnLayout: {
              title: 'schema.stepThree.columnLayout.title',
              type: 'string',
              enumOptions: [
                {
                  value: 'عمود',
                  label: 'schema.stepThree.columnLayout.singleColumn',
                  hint: 'schema.stepThree.columnLayout.singleColumnHint',
                },
                {
                  value: 'عمودين',
                  label: 'schema.stepThree.columnLayout.twoColumns',
                  hint: 'schema.stepThree.columnLayout.twoColumnsHint',
                },
              ],
            },
          },
        },
        {
          properties: {
            indicatorType: { const: 'أخرى' },
            columnLayout: {
              title: 'schema.stepThree.columnLayout.title',
              type: 'string',
              enumOptions: [
                {
                  value: 'عمود',
                  label: 'schema.stepThree.columnLayout.singleColumn',
                  hint: 'schema.stepThree.columnLayout.singleColumnHint',
                },
              ],
              default: 'schema.stepThree.columnLayout.singleColumn',
            },
          },
        },
      ],
    },
    columnLayout: {
      oneOf: [
        {
          properties: {
            columnLayout: { const: 'عمود' },
            latLngColumn: {
              type: 'string',
              title: 'schema.stepThree.columns.latLng',
              enum: ['اختر...'],
            },
          },
          required: ['latLngColumn'],
        },
        {
          properties: {
            columnLayout: { const: 'عمودين' },
            latColumn: {
              type: 'string',
              title: 'schema.stepThree.columns.lat',
              enum: ['اختر...'],
            },
            lngColumn: {
              type: 'string',
              title: 'schema.stepThree.columns.lng',
              enum: ['اختر...'],
            },
          },
          required: ['latColumn', 'lngColumn'],
        },
      ],
    },
  },
}

export const uiSchema = {
  indicatorType: {
    'ui:widget': 'radio',
  },
  columnLayout: {
    'ui:widget': 'radio',
  },
  latLngColumn: {
    'ui:widget': 'SelectWidget',
    'ui:options': {
      showSample: true,
    },
  },
  latColumn: {
    'ui:widget': 'SelectWidget',
    'ui:options': {
      showSample: true,
    },
  },
  lngColumn: {
    'ui:widget': 'SelectWidget',
    'ui:options': {
      showSample: true,
    },
  },
}
