import { Skeleton } from "@/components/Skeleton/Skeleton"

export const AdvancedFieldsSkeleton = () => {
    return (
        <div className="w-full py-8 px-4">
            <Skeleton className="h-8 w-48 mb-4" />
            <Skeleton className="h-4 w-64 mb-6" />
            <div className="space-y-4">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                ))}
                <div className="flex gap-4 mt-6">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                </div>
            </div>
        </div>
    )
}
