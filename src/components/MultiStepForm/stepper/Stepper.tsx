import { cn } from "@/lib/utils";

type StepperProps = {
  steps: string[];
  activeStep: number;
  onStepClick?: (stepIndex: number) => void;
  isStepEnabled: (stepIndex: number) => boolean;
  isStepCompleted: (stepIndex: number) => boolean;
};

export const Stepper: React.FC<StepperProps> = ({
  steps,
  activeStep,
  onStepClick,
  isStepEnabled,
  isStepCompleted
}) => {
  return (
    <div className="flex flex-col">
      {steps.map((step, index) => {
        const enabled = isStepEnabled(index);
        const completed = isStepCompleted(index);
        return (
          <div key={index} className="flex flex-col items-start">
            <div className="flex items-center space-x-2 gap-1">
              <div
                className={cn(
                  "flex items-center justify-center w-6 h-6 rounded-full border transition-colors",
                  activeStep === index
                    ? "bg-[#5E58EE] text-white border-[#5E58EE]"
                    : completed
                      ? "bg-green-500 text-white border-green-500"
                      : enabled
                        ? "text-gray-500 border-gray-300 cursor-pointer"
                        : "text-gray-300 border-gray-200 cursor-not-allowed"
                )}
                onClick={() => enabled && onStepClick?.(index)}
              >
                {completed ? "✓" : <span className="text-sm font-semibold">{index + 1}</span>}
              </div>
              <span
                className={cn(
                  "text-sm font-medium transition-colors",
                  activeStep === index
                    ? "text-black"
                    : completed
                      ? "text-green-500"
                      : enabled
                        ? "text-gray-500"
                        : "text-gray-300"
                )}
              >
                {step}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className="h-4 border-l border-gray-300 m-3" />
            )}
          </div>
        );
      })}
    </div>
  );
};