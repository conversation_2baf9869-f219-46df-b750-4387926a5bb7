import { useParams } from 'react-router-dom';
import { Stepper } from './stepper/Stepper';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import { StepProgressManager } from '@/shared/utils/stepProgress';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const stepConfigs = {
    [WorkspaceMethods.UPLOAD_FILE]: {
        steps: [
            'workspace.creation.steps.fileUpload',
            'workspace.creation.steps.setColumns',
            'workspace.creation.steps.aiReport',
            'workspace.creation.steps.showPlan',
            'workspace.creation.steps.summaryFields'
        ],
        dependencies: {
            0: [],
            1: [0],
            2: [1],
            3: [1],
            4: [3]
        }
    },
    [WorkspaceMethods.DESIGN_LAYER]: {
        steps: [
            'workspace.creation.steps.layerProperties',
            'workspace.creation.steps.layerForm'
        ],
        dependencies: {
            0: [],
            1: [0]
        }
    }
};

export const StepNavigation: React.FC = () => {
    const { t } = useTranslation();
    const { step = '0', method = WorkspaceMethods.UPLOAD_FILE } = useParams();
    const { navigateWithOrg } = useOrganizationManager();
    const currentStep = parseInt(step);

    const { steps, dependencies } = stepConfigs[method as keyof typeof stepConfigs];
    const translatedSteps = steps.map(step => t(step));

    const isStepCompleted = (stepIndex: number) => {
        const progress = StepProgressManager.getProgress(stepIndex, method);
        return progress.status === 'complete';
    };

    const isStepAccessible = (stepIndex: number) => {
        const stepDependencies = dependencies[stepIndex as keyof typeof dependencies];
        return stepDependencies.every(dep => isStepCompleted(dep));
    };

    useEffect(() => {
        if (!isStepAccessible(currentStep)) {
            const lastCompletedStep = Object.keys(dependencies)
                .map(Number)
                .reverse()
                .find(step => isStepCompleted(step)) || 0;

            navigateWithOrg(`/${Routes.createWorkspace}/${method}/${lastCompletedStep}`, {
                additionalParams: { resumed: 'true' },
                replace: true
            });
        }
    }, [currentStep, method]);

    const handleStepClick = (stepIndex: number) => {
        if (isStepAccessible(stepIndex)) {
            navigateWithOrg(`/${Routes.createWorkspace}/${method}/${stepIndex}`, {
                additionalParams: { resumed: 'true' }
            });
        }
    };

    return (
        <div className="w-50 p-8 pt-20">
            <Stepper
                steps={translatedSteps}
                activeStep={currentStep}
                onStepClick={handleStepClick}
                isStepEnabled={isStepAccessible}
                isStepCompleted={isStepCompleted}
            />
        </div>
    );
};