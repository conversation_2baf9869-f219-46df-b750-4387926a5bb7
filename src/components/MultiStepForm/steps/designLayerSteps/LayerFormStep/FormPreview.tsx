import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { useTranslation } from "react-i18next"
import { FormField } from "./types"


interface FormPreviewProps {
    fields: FormField[]
}

export default function FormPreview({ fields }: FormPreviewProps) {
    const [formData, setFormData] = useState<Record<string, any>>({})
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()

    useEffect(() => {
        // Reset form data when fields change
        setFormData({})
    }, [fields])

    const handleChange = (name: string, value: any) => {
        setFormData({ ...formData, [name]: value })
    }

    const renderField = (field: FormField) => {
        switch (field.type) {
            case "string":
                return (
                    <Input
                        id={field.name}
                        type="text"
                        value={formData[field.name] || ""}
                        onChange={(e) => handleChange(field.name, e.target.value)}
                        required={field.required}
                        className="w-full"
                    />
                )
            case "number":
                return (
                    <Input
                        id={field.name}
                        type="number"
                        value={formData[field.name] || ""}
                        onChange={(e) => handleChange(field.name, e.target.value)}
                        required={field.required}
                        className="w-full"
                    />
                )
            case "boolean":
                return (
                    <Checkbox
                        id={field.name}
                        checked={formData[field.name] || false}
                        onCheckedChange={(checked: boolean) => handleChange(field.name, checked)}
                    />
                )
            case "date":
                return (
                    <Input
                        id={field.name}
                        type="date"
                        value={formData[field.name] || ""}
                        onChange={(e) => handleChange(field.name, e.target.value)}
                        required={field.required}
                        className="w-full"
                    />
                )
            default:
                return null
        }
    }

    const visibleFields = fields.filter((field) => field.isVisible)

    return (
        <Card className="w-full">
            <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4">{t('workspace.layerForm.view')}</h2>
                <div className="space-y-4">
                    {visibleFields.map((field) => (
                        <div key={field.id} className="space-y-2">
                            <Label htmlFor={field.name} className={`block ${direction}`}>
                                {field.title}
                                {field.required && <span className="text-red-500">*</span>}
                            </Label>
                            {renderField(field)}
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    )
}

