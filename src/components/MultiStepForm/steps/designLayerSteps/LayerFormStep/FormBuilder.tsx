import { useState, useEffect } from "react"
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { Plus, HelpCircle } from "lucide-react"
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    type DragEndEvent,
} from "@dnd-kit/core"
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { SortableTableRow } from "./SortableTableRow"
import { useTranslation } from "react-i18next"
import { FieldType, FormBuilderProps, FormField } from "./types"
import { Routes, WorkspaceMethods } from "@/shared/utils/routes"
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager"
import { showToast } from "@/shared/utils/toastConfig"
import { useMutation } from "@apollo/client"
import { DESIGN_LAYER_JSON_SCHEMA } from "@/shared/graphQl/mutations/designLayer"
import { validateForm } from "@/shared/utils/designlayerValidation"

export default function FormBuilder({ fields: initialFields, onChange, setShowValidationMessage }: FormBuilderProps) {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const [fields, setFields] = useState<FormField[]>(initialFields);
    const { selectedOrg, navigateWithOrg } = useOrganizationManager();
    const [submitJsonSchema] = useMutation(DESIGN_LAYER_JSON_SCHEMA);

    useEffect(() => {
        setFields(initialFields);
    }, [initialFields]);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        }),
    );

    const updateFields = (newFields: FormField[]) => {
        setFields(newFields);
        onChange(newFields);
    };

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (active.id !== over?.id) {
            setFields((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over?.id);
                const newFields = arrayMove(items, oldIndex, newIndex);
                onChange(newFields);
                return newFields;
            });
        }
    };

    const handleFieldUpdate = (id: string, updates: Partial<FormField>) => {
        const newFields = fields.map((field) =>
            field.id === id ? { ...field, ...updates } : field
        );
        updateFields(newFields);
    };

    const handleToggleRequired = (id: string) => {
        handleFieldUpdate(id, { required: !fields.find(f => f.id === id)?.required });
    };

    const handleToggleVisible = (id: string) => {
        handleFieldUpdate(id, { isVisible: !fields.find(f => f.id === id)?.isVisible });
    };

    const handleTypeChange = (id: string, type: FieldType) => {
        handleFieldUpdate(id, { type });
    };

    const handleFieldChange = (id: string, key: keyof FormField, value: any) => {
        handleFieldUpdate(id, { [key]: value });
    };

    const handleDeleteField = (id: string) => {
        const newFields = fields.filter((field) => field.id !== id);
        updateFields(newFields);
    };

    const handleAddField = () => {
        const newId = `field${fields.length + 1}`;
        const newField: FormField = {
            id: newId,
            name: newId,
            title: `الحقل ${fields.length + 1}`,
            type: "string",
            required: false,
            isVisible: true,
        };
        updateFields([...fields, newField]);
    };

    const handleSubmitSchema = async () => {
        const formErrors = fields.map(field => ({
            field,
            errors: validateForm(field)
        }));

        const hasErrors = formErrors.some(item => Object.keys(item.errors).length > 0);

        if (hasErrors) {
            setShowValidationMessage(true);
            return;
        }

        const hasRequiredField = fields.some(field => field.required);
        const hasVisibleField = fields.some(field => field.isVisible);

        if (!hasRequiredField || !hasVisibleField) {
            setShowValidationMessage(true);
            return;
        }
        setShowValidationMessage(false);

        // Create and populate the schema
        const schemaData = {
            type: "object",
            properties: {},
            required: [],
        } as any;

        // Populate the schema with field data
        fields.forEach((field) => {
            schemaData.properties[field.name] = {
                type: field.type === "date" ? "string" : field.type,
                title: field.title,
            };

            if (field.type === "date") {
                schemaData.properties[field.name].format = "date";
            }

            if (field.required) {
                schemaData.required.push(field.name);
            }
        });

        try {
            const requestId = parseInt(localStorage.getItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`) ?? "");
            if (requestId) {
                const result = await submitJsonSchema({
                    variables: {
                        dataInput: {
                            orgId: parseInt(selectedOrg?.id),
                            workspaceRequestId: requestId,
                            jsonSchema: JSON.stringify(schemaData),
                            summaryFields: JSON.stringify(fields.filter(f => f.isVisible).map(f => f.name))
                        }
                    }
                });

                if (result?.data?.designLayerJsonSchema?.workspace?.name) {
                    localStorage.setItem('currentWorkspaceTitle', result.data.designLayerJsonSchema.workspace.name);
                    navigateWithOrg(`/${Routes.map}/${result.data.designLayerJsonSchema.workspace.id}?type=${WorkspaceMethods.DESIGN_LAYER}`);
                } else {
                    showToast.error(t('workspace.layerForm.alerts.error'));
                }
            } else {
                showToast.error(t('workspace.layerForm.alerts.requestIdMissing'));
            }
        } catch (error) {
            console.error("Schema submission error:", error);
            showToast.error(t('workspace.layerForm.alerts.error'));
        }
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/0?resumed=true`);
    };

    return (
        <div className="form-builder">
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                <Table>
                    <TooltipProvider>
                        <TableHeader>
                            <TableRow>
                                <TableHead className={`${direction} w-12`}></TableHead>
                                <TableHead className={`${direction} min-w-[70px]`}>{t('workspace.layerForm.fieldTitle')}<span className="text-red-500">*</span></TableHead>
                                <TableHead className={direction}>{t('workspace.layerForm.fieldType')}<span className="text-red-500">*</span></TableHead>
                                <TableHead className={`${direction} min-w-[70px]`}>{t('workspace.layerForm.identifier')}<span className="text-red-500">*</span></TableHead>
                                <TableHead className={direction}>
                                    <div className="flex items-center gap-1 min-w-24">
                                        {t('workspace.layerForm.requiredField')}
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <HelpCircle className="h-4 w-4 text-gray-400" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                {t('workspace.layerForm.requiredTooltip')}
                                            </TooltipContent>
                                        </Tooltip>
                                    </div>
                                </TableHead>
                                <TableHead className={direction}>
                                    <div className="flex items-center gap-1 min-w-28">
                                        {t('workspace.layerForm.showInSummary')}
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <HelpCircle className="h-4 w-4 text-gray-400" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                {t('workspace.layerForm.summaryTooltip')}
                                            </TooltipContent>
                                        </Tooltip>
                                    </div>
                                </TableHead>
                                <TableHead className={`${direction} w-12`}></TableHead>
                            </TableRow>
                        </TableHeader>
                    </TooltipProvider>
                    <TableBody>
                        <SortableContext items={fields} strategy={verticalListSortingStrategy}>
                            {fields.map((field) => (
                                <SortableTableRow
                                    key={field.id}
                                    field={field}
                                    onToggleRequired={handleToggleRequired}
                                    onToggleVisible={handleToggleVisible}
                                    onTypeChange={handleTypeChange}
                                    onFieldChange={handleFieldChange}
                                    onDeleteField={handleDeleteField}
                                    errors={validateForm(field)}
                                />
                            ))}
                        </SortableContext>
                    </TableBody>
                </Table>
            </DndContext>

            <div className="flex-col mt-4">
                <Button variant="ghost" onClick={handleAddField} className="text-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('workspace.layerForm.addField')}
                </Button>
                <div className="flex gap-4 mt-6">
                    <Button onClick={handlePrevious} variant="outline" className="text-gray-600 py-2 px-8 rounded-md">
                        {t('buttons.previous')}
                    </Button>
                    <Button
                        onClick={handleSubmitSchema}
                        className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                    >
                        {t('buttons.create')}
                    </Button>
                </div>
            </div>
        </div>
    );
}