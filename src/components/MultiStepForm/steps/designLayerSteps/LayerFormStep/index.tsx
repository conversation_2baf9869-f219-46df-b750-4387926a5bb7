import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Typography } from '@/components/ui/Typography';
import MonacoEditor from "@monaco-editor/react";
import { useCallback, useState } from 'react';
import FormPreview from './FormPreview';
import FormBuilder from './FormBuilder';
import { FormField } from "./types";
import { initialFormFields, initialSchema } from '../../../schemas/layerFormStepSchema';
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { useMutation } from '@apollo/client';
import { DESIGN_LAYER_JSON_SCHEMA } from '@/shared/graphQl/mutations/designLayer';
import { showToast } from '@/shared/utils/toastConfig';
import { AlertCircle } from 'lucide-react';

export const LayerFormStep = () => {
    const { i18n, t } = useTranslation()
    const direction = i18n.dir()
    const { selectedOrg, navigateWithOrg } = useOrganizationManager()
    const [submitJsonSchema] = useMutation(DESIGN_LAYER_JSON_SCHEMA);

    const [activeTab, setActiveTab] = useState("user-interface")
    const [showValidationMessage, setShowValidationMessage] = useState(false);
    const [schema, setSchema] = useState(JSON.stringify(initialSchema, null, 2))
    const [formFields, setFormFields] = useState<FormField[]>(initialFormFields)

    // Convert JSON schema to form fields
    const convertSchemaToFields = useCallback((parsedSchema: any): FormField[] => {
        try {
            return Object.entries(parsedSchema.properties).map(([name, prop]: [string, any]) => ({
                id: name,
                name,
                title: prop.title,
                type: prop.type === "string" && prop.format === "date" ? "date" : prop.type,
                required: parsedSchema.required?.includes(name) || false,
                isVisible: true,
            }))
        } catch (error) {
            console.error("Error converting schema to fields:", error)
            return []
        }
    }, [])

    // Update schema when editor changes
    const updateSchema = useCallback((value: string | undefined) => {
        if (!value) return

        setSchema(value)
        try {
            const parsedSchema = JSON.parse(value)
            const newFields = convertSchemaToFields(parsedSchema)
            setFormFields(newFields)
        } catch (error) {
            console.error("Invalid JSON:", error)
        }
    }, [convertSchemaToFields])

    // Convert form fields to JSON schema
    const convertFieldsToSchema = useCallback((fields: FormField[]) => {
        const properties = fields.reduce((acc, field) => {
            acc[field.name] = {
                type: field.type === "date" ? "string" : field.type,
                title: field.title
            }
            if (field.type === "date") acc[field.name].format = "date"
            return acc
        }, {} as Record<string, any>)

        const requiredFields = fields.filter((field) => field.required).map((field) => field.name)
        return { type: "object", properties, required: requiredFields }
    }, [])

    // Update form fields when builder changes
    const updateFormFields = useCallback((newFields: FormField[]) => {
        setFormFields(newFields)
        const newSchema = convertFieldsToSchema(newFields)
        setSchema(JSON.stringify(newSchema, null, 2))
    }, [convertFieldsToSchema])

    const handleSubmitAdvancedSchema = async () => {
        const hasRequiredField = formFields.some(field => field.required);
        const hasVisibleField = formFields.some(field => field.isVisible);

        if (!hasRequiredField || !hasVisibleField) {
            setShowValidationMessage(true);
            return;
        }
        setShowValidationMessage(false);

        try {
            const requestId = parseInt(localStorage.getItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`) ?? "");
            if (requestId) {
                const result = await submitJsonSchema({
                    variables: {
                        dataInput: {
                            orgId: parseInt(selectedOrg?.id),
                            workspaceRequestId: requestId,
                            jsonSchema: schema,
                            summaryFields: JSON.stringify(formFields.filter(f => f.isVisible).map(f => f.name))
                        }
                    }
                });
                localStorage.setItem('currentWorkspaceTitle', result.data.designLayerJsonSchema.workspace.name)
                navigateWithOrg(`/${Routes.map}/${result.data.designLayerJsonSchema.workspace.id}?type=${WorkspaceMethods.DESIGN_LAYER}`);
            }
        } catch (error) {
            console.error("Invalid JSON or submission error:", error);
            showToast.error(t('workspace.layerForm.alerts.error'));
        }
    }

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/0?resumed=true`);
    };

    return (
        <div className="py-8 px-8 max-w-full">
            <Typography variant="h1" className="text-2xl font-bold mb-2">
                {t('workspace.layerForm.title')}
            </Typography>
            <Typography className="text-gray-600 mb-6">
                {t('workspace.layerForm.description')}
            </Typography>

            <div className="flex gap-6">
                <div className="w-2/3">
                    <Tabs value={activeTab} onValueChange={setActiveTab} dir={direction}>
                        <TabsList className="mb-6 w-auto">
                            <TabsTrigger value="user-interface">
                                {t('workspace.layerForm.userInterface')}
                            </TabsTrigger>
                            <TabsTrigger value="advanced-editor">
                                {t('workspace.layerForm.advancedEditor')}
                            </TabsTrigger>
                        </TabsList>
                        {showValidationMessage && (
                            <div className="mb-4 space-y-2">
                                {!formFields.some(field => field.required) && (
                                    <div className="text-red-500 p-3 bg-red-50 rounded-md flex items-center gap-2 w-fit">
                                        <AlertCircle className="h-5 w-5" />
                                        {t('workspace.layerForm.validation.requiredFieldMissing')}
                                    </div>
                                )}
                                {!formFields.some(field => field.isVisible) && (
                                    <div className="text-red-500 p-3 bg-red-50 rounded-md flex items-center gap-2 w-fit">
                                        <AlertCircle className="h-5 w-5" />
                                        {t('workspace.layerForm.validation.visibleFieldMissing')}
                                    </div>
                                )}
                            </div>
                        )}
                        <TabsContent value="user-interface">
                            <FormBuilder fields={formFields} onChange={updateFormFields} setShowValidationMessage={setShowValidationMessage} />
                        </TabsContent>
                        <TabsContent value="advanced-editor">
                            <div className="border rounded-md ltr h-fit min-h-96">
                                <MonacoEditor
                                    height="60vh"
                                    language="json"
                                    value={schema}
                                    onChange={updateSchema}
                                    options={{
                                        minimap: { enabled: false },
                                        automaticLayout: true,
                                        scrollBeyondLastLine: false,
                                        fontSize: 14,
                                        lineNumbers: "on",
                                        renderValidationDecorations: "on",
                                    }}
                                />
                            </div>
                            <div className="flex gap-4 mt-6">
                                <Button onClick={handlePrevious} variant="outline" className="text-gray-600 py-2 px-8 rounded-md">
                                    {t('buttons.previous')}
                                </Button>
                                <Button
                                    onClick={handleSubmitAdvancedSchema}
                                    className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                                >
                                    {t('buttons.create')}
                                </Button>
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>

                <div className="w-1/3">
                    <h2 className="text-xl font-bold mb-4">{t('workspace.layerForm.preview')}</h2>
                    <FormPreview fields={formFields} />
                </div>
            </div>
        </div>
    )
}