import { TableCell, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Trash2, GripVertical } from "lucide-react"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { useTranslation } from "react-i18next"
import { FieldType, SortableTableRowProps } from "./types"

export const SortableTableRow = ({
    field,
    onToggleRequired,
    onToggleVisible,
    onTypeChange,
    onFieldChange,
    onDeleteField,
    errors
}: SortableTableRowProps) => {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: field.id });
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    const fieldTypeLabels: Record<FieldType, string> = {
        string: t('workspace.layerForm.fieldTypes.text'),
        number: t('workspace.layerForm.fieldTypes.number'),
        boolean: t('workspace.layerForm.fieldTypes.boolean'),
        date: t('workspace.layerForm.fieldTypes.date')
    };

    return (
        <TableRow ref={setNodeRef} style={style} {...attributes}>
            <TableCell>
                <Button variant="ghost" size="icon" className="cursor-move" {...listeners}>
                    <GripVertical className="h-4 w-4" />
                </Button>
            </TableCell>
            <TableCell>
                <div className="space-y-1">
                    <Input
                        value={field.title}
                        onChange={(e) => onFieldChange(field.id, "title", e.target.value)}
                        className={`min-w-[70px] no-border-focus ${direction} ${errors?.title ? 'border-red-500' : ''}`}
                    />
                    {errors?.title && (
                        <p className="text-sm text-red-500">{errors.title}</p>
                    )}
                </div>
            </TableCell>
            <TableCell>
                <div className="space-y-1">
                    <Select
                        dir={direction}
                        value={field.type}
                        onValueChange={(value) => onTypeChange(field.id, value as FieldType)}
                    >
                        <SelectTrigger className={direction} style={{ minWidth: '100px' }}>
                            <SelectValue>
                                {fieldTypeLabels[field.type]}
                            </SelectValue>
                        </SelectTrigger>
                        <SelectContent >
                            <SelectItem value="string">{t('workspace.layerForm.fieldTypes.text')}</SelectItem>
                            <SelectItem value="number">{t('workspace.layerForm.fieldTypes.number')}</SelectItem>
                            <SelectItem value="boolean">{t('workspace.layerForm.fieldTypes.boolean')}</SelectItem>
                            <SelectItem value="date">{t('workspace.layerForm.fieldTypes.date')}</SelectItem>
                        </SelectContent>
                    </Select>
                    {errors?.type && (
                        <p className="text-sm text-red-500">{errors.type}</p>
                    )}
                </div>
            </TableCell>
            <TableCell>
                <div className="space-y-1">
                    <Input
                        value={field.name}
                        onChange={(e) => onFieldChange(field.id, "name", e.target.value)}
                        className={`min-w-[70px] no-border-focus ${direction} ${errors?.name ? 'border-red-500' : ''}`}
                    />
                    {errors?.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                </div>
            </TableCell>
            <TableCell>
                <Checkbox
                    checked={field.required}
                    onCheckedChange={() => onToggleRequired(field.id)}
                    className="mx-auto block"
                />
            </TableCell>
            <TableCell>
                <Checkbox
                    checked={field.isVisible}
                    onCheckedChange={() => onToggleVisible(field.id)}
                    className="mx-auto block"
                />
            </TableCell>
            <TableCell>
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDeleteField(field.id)}
                    className="text-red-500 hover:text-red-700"
                >
                    <Trash2 className="h-4 w-4" />
                </Button>
            </TableCell>
        </TableRow>
    );
};