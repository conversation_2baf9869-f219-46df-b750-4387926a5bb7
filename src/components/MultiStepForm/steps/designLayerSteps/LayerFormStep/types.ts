export type FieldType = 'string' | 'number' | 'boolean' | 'date'

export interface FormField {
  id: string
  name: string
  title: string
  type: FieldType
  required: boolean
  isVisible: boolean
}

export interface FormSchema {
  type: string
  properties: Record<string, any>
  required: string[]
}

export interface FormBuilderProps {
  fields: FormField[]
  onChange: (fields: FormField[]) => void
  setShowValidationMessage: (show: boolean) => void
  errors?: Record<string, string>
}

export interface SortableTableRowProps {
  field: FormField
  onToggleRequired: (id: string) => void
  onToggleVisible: (id: string) => void
  onTypeChange: (id: string, type: FieldType) => void
  onFieldChange: (id: string, key: keyof FormField, value: any) => void
  onDeleteField: (id: string) => void
  errors?: any
}
