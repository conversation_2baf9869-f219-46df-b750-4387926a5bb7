import { useDispatch } from 'react-redux';
import { useMutation, useQuery } from '@apollo/client';
import validator from '@rjsf/validator-ajv8';
import { updateStepData } from '@/shared/store/slices/stepSlice';
import { showToast } from '@/shared/utils/toastConfig';
import { StepProgressManager } from '@/shared/utils/stepProgress';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import {
    CANCEL_DESIGN_LAYER_REQUEST,
    CREATE_DESIGN_LAYER_REQUEST,
    UPDATE_DESIGN_LAYER_REQUEST
} from '@/shared/graphQl/mutations/designLayer';
import { layerPropertiesSchema, layerPropertiesUiSchema } from '../../schemas/layerPropertiesSchema';
import { Button } from '@/components/ui/button';
import { Typography } from '@/components/ui/Typography';
import { CustomForm } from '@/components/RJSF/theme/CustomTheme';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { determineCurrentStep } from '@/shared/utils/generals';
import { IncompleteWorkspaceDialog } from '../../components/IncompleteWorkspaceDialog';
import { FETCH_DATASET_REQUESTS } from '@/shared/graphQl/queries/dataset';

interface FormData {
    layerName: string;
    layerColor: string;
    layerDescription?: string;
}

export const LayerPropertiesStep: React.FC = () => {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const { selectedOrg, navigateWithOrg } = useOrganizationManager();
    const [showDialog, setShowDialog] = useState(false);
    const location = useLocation();

    const savedProgress = StepProgressManager.getProgress(0, WorkspaceMethods.DESIGN_LAYER);
    const isReadOnly = savedProgress.status === 'complete';
    const existingRequestId = localStorage.getItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`);
    const isUpdating = !!existingRequestId;

    const [createDesignLayer, { loading: createLoading }] = useMutation(CREATE_DESIGN_LAYER_REQUEST, {
        onError: (error) => {
            console.error('Error creating design layer:', error);
        },
    });

    const [updateDesignLayer, { loading: updateLoading }] = useMutation(UPDATE_DESIGN_LAYER_REQUEST, {
        onError: (error) => {
            console.error('Error updating design layer:', error);
        },
    });

    const [cancelWorkspaceRequest] = useMutation(CANCEL_DESIGN_LAYER_REQUEST);

    const { data: requestsData } = useQuery(FETCH_DATASET_REQUESTS, {
        variables: {
            orgId: parseInt(selectedOrg?.id)
        }
    })

    useEffect(() => {
        if (requestsData?.workspaceRequests?.count > 0) {
            const designLayerRequest = requestsData.workspaceRequests.data.find(
                (request: any) => request.requestType === WorkspaceMethods.DESIGN_LAYER
            );

            if (designLayerRequest?.id) {
                const { layerData, id } = designLayerRequest;

                // Save step 0 data (Layer Properties)
                StepProgressManager.saveProgress(0, {
                    layerName: layerData.title,
                    layerColor: layerData.color,
                    layerDescription: layerData.description,
                    requestId: id
                }, WorkspaceMethods.DESIGN_LAYER);

                localStorage.setItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`, id.toString());
                setShowDialog(true);
            }
        } else {
            StepProgressManager.clearProgress(WorkspaceMethods.DESIGN_LAYER);
            localStorage.removeItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`);
        }
    }, [requestsData]);

    const handleSubmit = async ({ formData }: { formData: FormData }) => {
        try {
            if (isUpdating) {
                // Update existing request
                const { data } = await updateDesignLayer({
                    variables: {
                        dataInput: {
                            workspaceRequestId: Number(existingRequestId),
                            title: formData.layerName,
                            color: formData.layerColor,
                            description: formData.layerDescription || '',
                            // readOnly: false,
                            orgId: parseInt(selectedOrg?.id)
                        },
                    },
                });

                if (data?.updateDesignLayerRequest?.workspaceRequest) {
                    showToast.success(t('workspace.layerProperties.alerts.updateSuccess'));

                    dispatch(updateStepData({
                        step: 0,
                        data: { ...formData, requestId: existingRequestId },
                        method: WorkspaceMethods.DESIGN_LAYER
                    }));
                    navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/1?resumed=true`);
                }
            } else {
                // Create new request
                const { data } = await createDesignLayer({
                    variables: {
                        dataInput: {
                            title: formData.layerName,
                            color: formData.layerColor,
                            description: formData.layerDescription || '',
                            // readOnly: false,
                            orgId: parseInt(selectedOrg?.id)
                        },
                    },
                });

                if (data?.createDesignLayerRequest?.workspaceRequest) {
                    const requestId = data.createDesignLayerRequest.workspaceRequest.id;
                    localStorage.setItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`, requestId);
                    showToast.success(t('workspace.layerProperties.alerts.success'));

                    dispatch(updateStepData({
                        step: 0,
                        data: { ...formData, requestId },
                        method: WorkspaceMethods.DESIGN_LAYER
                    }));
                    navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/1?resumed=true`);
                }
            }
        } catch (error) {
            console.error('Error:', error);
            showToast.error(t('workspace.layerProperties.alerts.error'));
        }
    };

    const handleContinue = () => {
        const currentStep = determineCurrentStep(WorkspaceMethods.DESIGN_LAYER);
        setShowDialog(false);
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/${currentStep}?resumed=true`);
    };

    const handleDecline = async () => {
        const requestIdStr = localStorage.getItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`);
        const requestId = requestIdStr ? parseInt(requestIdStr) : null;

        if (requestId) {
            await cancelWorkspaceRequest({
                variables: {
                    closeInput: {
                        datasetRequestId: requestId,
                        orgId: +selectedOrg?.id
                    }
                }
            });
        }

        setShowDialog(false);
        StepProgressManager.clearProgress(WorkspaceMethods.DESIGN_LAYER);
        localStorage.removeItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`);
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/0?resumed=true`);
    };

    const loading = createLoading || updateLoading;
    const handleNext = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.DESIGN_LAYER}/1?resumed=true`);
    }

    return (
        <>
            {showDialog && !location.search.includes('resumed') && (
                <IncompleteWorkspaceDialog
                    onContinue={handleContinue}
                    onDecline={handleDecline}
                />
            )}
            <div className="max-w-lg py-8 px-4">
                <Typography variant="h1" className="text-2xl font-bold mb-2">
                    {t('workspace.layerProperties.title')}
                </Typography>
                <Typography className="text-gray-600 mb-6">
                    {t('workspace.layerProperties.description')}
                </Typography>
                <CustomForm
                    schema={layerPropertiesSchema}
                    uiSchema={layerPropertiesUiSchema}
                    validator={validator}
                    onSubmit={handleSubmit}
                    formData={savedProgress?.formData ?? {}}
                >

                    <div className="flex gap-4 mt-6">
                        <Button
                            type="submit"
                            variant="default"
                            className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                            disabled={loading}
                        >
                            {loading
                                ? t('buttons.processing')
                                : isUpdating
                                    ? t('buttons.update')
                                    : t('buttons.next')}
                        </Button>
                        {isReadOnly ?
                            <Button onClick={handleNext} variant="outline" className="text-gray-600 py-2 px-8 rounded-md">
                                {t('buttons.next')}
                            </Button> : ''}
                    </div>
                </CustomForm>
            </div>
        </>
    );
};