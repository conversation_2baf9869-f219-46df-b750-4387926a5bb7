
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateStepData } from '@/shared/store/slices/stepSlice';
import { CustomForm } from '@/components/RJSF/theme/CustomTheme';
import { Button } from '@/components/ui/button';
import { useMutation, useQuery } from '@apollo/client';
import { GET_DATASET_SAMPLES } from '@/shared/graphQl/queries/dataset';
import { CREATE_LOCATION_FIELD_MAPPING } from '@/shared/graphQl';
import { schema, uiSchema } from '../../schemas/stepThreeSchema';
import { showToast } from '@/shared/utils/toastConfig';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import { StepProgressManager } from '@/shared/utils/stepProgress';
import { SetColumnsSkeleton } from '../../skeletons/SetColumnsSkeleton';
import { createColumnSamples, updateSchemaWithAvailableColumns } from '@/shared/utils/formUtils';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { Typography } from '@/components/ui/Typography';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

export const SetColumnsStep: React.FC = () => {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const datasetRequestId = parseInt(localStorage.getItem('requestId') ?? '');
    const [formSchema, setFormSchema] = useState(schema);
    const [columnSamples, setColumnSamples] = useState<Record<string, string[]>>({});

    const savedProgress = StepProgressManager.getProgress(1, WorkspaceMethods.UPLOAD_FILE)
    const isReadOnly = savedProgress.status === 'complete'

    const [formData, setFormData] = useState(savedProgress?.formData ?? {});
    const { navigateWithOrg } = useOrganizationManager()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId')

    const { data: samplesData, loading } = useQuery(GET_DATASET_SAMPLES, {
        variables: {
            datasetRequestId, orgId
        },
        skip: !datasetRequestId || !orgId,
    });

    useEffect(() => {
        if (samplesData?.datasetSampleData?.data) {
            const samples = createColumnSamples(samplesData);

            setColumnSamples(samples);

            const allColumns = samplesData.datasetSampleData.data.map((item: { column: string }) => item.column);
            const updatedSchema = updateSchemaWithAvailableColumns(schema, formData, allColumns, samples, 'columns');
            setFormSchema(updatedSchema);
        }
    }, [samplesData, formData]);

    const handleChange = ({ formData }: any) => {
        const newFormData = formData
        if (newFormData.indicatorType !== formData.indicatorType) {
            // Reset fields dependent on `indicatorType`
            newFormData.columnLayout = 'عمود';
            delete newFormData.latLngColumn;
            delete newFormData.latColumn;
            delete newFormData.lngColumn;
        }

        if (newFormData.columnLayout !== formData.columnLayout) {
            // Reset fields dependent on `columnLayout`
            if (newFormData.columnLayout === 'عمود') {
                delete newFormData.latColumn;
                delete newFormData.lngColumn;
            } else if (newFormData.columnLayout === 'عمودين') {
                delete newFormData.latLngColumn;
            }
        }

        setFormData(newFormData);
    };


    const [createLocationFieldMapping, { loading: mutationLoading }] = useMutation(CREATE_LOCATION_FIELD_MAPPING);

    const handleSubmit = async ({ formData }: any) => {
        const input = {
            datasetRequestId: datasetRequestId,
            coordinateType: formData.indicatorType === 'نقطة' ? 'point' : 'other',
            latLonColumnNum: formData.columnLayout === 'عمود' ? 'column' : 'two_column',
            langLatColumn: formData.latLngColumn || '',
            latitudeColumn: formData.latColumn || '',
            longitudeColumn: formData.lngColumn || '',
            orgId
        };

        try {
            const response = await createLocationFieldMapping({
                variables: {
                    dataInput: input,
                },
            }) as any

            if (response?.errors) {
                const errorMessage = response.errors[0]?.extensions?.http?.reason?.error || 'حدث خطأ في تعيين الأعمدة';
                showToast.error(`${errorMessage}`);
                return;
            }
            showToast.success(t('workspace.setColumns.columnSettingsSaved'));
            dispatch(updateStepData({ step: 1, data: formData, method: WorkspaceMethods.UPLOAD_FILE }));
            navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/2?resumed=true`);
        } catch (error: any) {
            const errorMessage = error?.graphQLErrors?.[0]?.extensions?.http?.reason?.error || 'حدث خطأ في تعيين الأعمدة';
            showToast.error(`${errorMessage}`);
            console.error('Mutation error:', error);
        }
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/0?resumed=true`);
    };

    return (
        <div className="max-w-lg py-8 px-4">
            <Typography variant="h1" className="text-2xl font-bold mb-2">
                {t('workspace.setColumns.title')}
            </Typography>
            <Typography className="text-gray-600 mb-6">
                {t('workspace.setColumns.description')}
            </Typography>

            {loading ? (
                <SetColumnsSkeleton />) : (
                <CustomForm
                    schema={formSchema}
                    uiSchema={uiSchema}
                    onSubmit={handleSubmit}
                    onChange={handleChange}
                    formContext={{ columnSamples }}
                    formData={formData}
                >
                    <div className="flex justify-between items-center mt-6">
                        <Button
                            variant="outline"
                            onClick={handlePrevious}
                            className="text-gray-600 py-2 px-8 rounded-md"
                        >
                            {t('buttons.previous')}
                        </Button>
                        <Button
                            type="submit"
                            className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                            disabled={loading || isReadOnly}
                        >
                            {mutationLoading ? t('buttons.processing') : t('buttons.next')}
                        </Button>
                    </div>
                </CustomForm>
            )}
        </div>
    );
};