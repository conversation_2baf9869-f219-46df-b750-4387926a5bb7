import { useQuery, useMutation } from '@apollo/client';
import { GET_JSON_SCHEMAS } from '@/shared/graphQl/queries/dataset';
import { UPDATE_JSON_SCHEMAS, CREATE_WORKSPACE } from '@/shared/graphQl/mutations/dataset';
import { CustomForm } from "@/components/RJSF/theme/CustomTheme";
import { initialFormData, schema, uiSchema } from "../../schemas/stepFourSchema";
import { useState } from 'react';
import { showToast } from '@/shared/utils/toastConfig';
import { useDispatch } from 'react-redux';
import { updateStepData, resetSteps } from '@/shared/store/slices/stepSlice';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { Typography } from '@/components/ui/Typography';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

export const ShowPlanStep = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const [schemas, setSchemas] = useState({
        jsonSchema: {},
        uiSchema: {},
        formData: {}
    });
    const { navigateWithOrg } = useOrganizationManager()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId')
    const datasetRequestId = localStorage.getItem('requestId');

    const { data, loading } = useQuery(GET_JSON_SCHEMAS, {
        variables: {
            datasetRequestId: parseInt(datasetRequestId || '0'), orgId
        },
        skip: !datasetRequestId,
        onCompleted: (data) => {

            // Take first item from formData array as initial form data
            const initialFormData = data.jsonSchemas.formData[0] || {};

            setSchemas({
                jsonSchema: data.jsonSchemas.jsonSchema,
                uiSchema: {},
                formData: initialFormData
            });
        }
    });

    const [updateJsonSchemas, { loading: mutationLoading }] = useMutation(UPDATE_JSON_SCHEMAS);
    const [createWorkspaceLayer, { loading: skipMutationLoading }] = useMutation(CREATE_WORKSPACE);

    const handleSubmit = ({ formData }: { formData: any }) => {
        console.log('Form submitted:', formData);
    };

    const handleReset = () => {
        if (data?.jsonSchemas) {
            const initialFormData = data.jsonSchemas.formData[0] || {};
            setSchemas({
                jsonSchema: data.jsonSchemas.jsonSchema,
                uiSchema: {},
                formData: initialFormData
            });
        }
    };

    const handleSkip = async () => {
        const datasetRequestId = parseInt(localStorage.getItem('requestId') || '0');

        try {
            const result = await createWorkspaceLayer({
                variables: {
                    dataInput: {
                        datasetRequestId: datasetRequestId,
                        mapDataColumns: JSON.stringify([]),
                        orgId: orgId
                    }
                }
            });

            if (result.data?.createWorkspaceLayer?.workspace) {
                showToast.success(t('alerts.success.workspaceCreated'));
                dispatch(updateStepData({ step: 4, data: {}, method: WorkspaceMethods.UPLOAD_FILE }));
                dispatch(resetSteps({ method: WorkspaceMethods.UPLOAD_FILE }));
                localStorage.setItem('currentWorkspaceTitle', result.data.createWorkspaceLayer.workspace.name);
                navigateWithOrg(`/${Routes.map}/${result.data.createWorkspaceLayer.workspace.id}?fromCreation=true&type=${WorkspaceMethods.UPLOAD_FILE}`);
            }
        } catch (error: any) {
            console.error('Error creating workspace:', error);
        }
    };

    const formContext = {
        initialSchemas: schemas,
        onSave: async ({ formData, jsonSchema }: any) => {
            const datasetRequestId = parseInt(localStorage.getItem('requestId') || '0');

            try {
                await updateJsonSchemas({
                    variables: {
                        dataInput: {
                            datasetRequestId,
                            formData: JSON.stringify(formData),
                            jsonSchema: JSON.stringify(jsonSchema),
                            webUiJsonSchema: JSON.stringify(schemas.uiSchema),
                            orgId
                        }
                    }
                });
                dispatch(updateStepData({ step: 3, data: formData, method: WorkspaceMethods.UPLOAD_FILE }));
                navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/4?resumed=true`);
            } catch (error) {
                showToast.error(t('workspace.showPlan.errorUpdatingFiles'));
                console.error('Error updating schemas:', error);
            }
        },
        onCancel: () => {
            handleReset();
        },
        onSkip: handleSkip,
        mutationLoading: mutationLoading || skipMutationLoading,
    };

    return (
        <div className="w-full py-8 px-4">
            <Typography variant="h1" className="text-2xl font-bold mb-2">
                {t('workspace.showPlan.title')}
            </Typography>

            {loading ? (
                <div> {t('workspace.showPlan.loading')}</div>
            )
                : (
                    <CustomForm
                        schema={schema}
                        uiSchema={uiSchema}
                        formData={initialFormData}
                        onSubmit={handleSubmit}
                        liveValidate={true}
                        formContext={formContext}
                    >
                        <div />
                    </CustomForm>
                )}
        </div>
    );
};