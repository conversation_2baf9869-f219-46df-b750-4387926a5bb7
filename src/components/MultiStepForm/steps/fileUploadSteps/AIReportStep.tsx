import { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useQuery, useMutation } from '@apollo/client';
import { FETCH_DETAILED_DATASET_REQUEST } from '@/shared/graphQl/queries/dataset';
import { CREATE_WORKSPACE } from '@/shared/graphQl';
import { RootState } from '@/shared/store';
import { updateStepData, resetSteps } from '@/shared/store/slices/stepSlice';
import { Button } from '@/components/ui/button';
import { Download, Loader2, Maximize } from 'lucide-react';
import { showToast } from '@/shared/utils/toastConfig';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import { StepProgressManager } from '@/shared/utils/stepProgress';
import { AIReportSkeleton } from '../../skeletons/AIReportSkeleton';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

export const EDAReportStep: React.FC = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch();
    const stepData = useSelector((state: RootState) => state.steps.stepData[0]);
    const { navigateWithOrg } = useOrganizationManager()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');
    const requestId = stepData?.requestId ?? localStorage.getItem('requestId');
    const datasetRequestId = parseInt(requestId ?? "");

    const [fileContent, setFileContent] = useState<string | null>(null);
    const [fileName, setFileName] = useState<string>('report.html');
    const [selectedReportSource, setSelectedReportSource] = useState<string>('ydata');
    const iframeRef = useRef<HTMLIFrameElement>(null);

    const [createWorkspaceLayer, { loading: mutationLoading }] = useMutation(CREATE_WORKSPACE);

    const { data, loading, error, stopPolling } = useQuery(FETCH_DETAILED_DATASET_REQUEST, {
        variables: {
            pk: requestId, orgId
        },
        pollInterval: 20000,
        skip: !requestId || !orgId,
    });

    const edaReports = Array.isArray(data?.workspaceRequests?.data?.[0]?.dataset?.metaData?.eda_reports)
        ? data?.workspaceRequests?.data?.[0]?.dataset?.metaData?.eda_reports
        : [{
            source: 'ydata',
            status: data?.workspaceRequests?.data?.[0]?.dataset?.metaData?.eda_reports?.status,
            errors: data?.workspaceRequests?.data?.[0]?.dataset?.metaData?.eda_reports?.errors
        }];
    const selectedReport = edaReports.find((report: any) => report?.source === selectedReportSource);
    const reportStatus = selectedReport?.status;
    const reportPath = selectedReport?.report_path;

    useEffect(() => {
        if (reportStatus === 'PUBLISHED' && reportPath) {
            stopPolling();
            fetchReport(reportPath);
            StepProgressManager.saveProgress(2, null, WorkspaceMethods.UPLOAD_FILE);
        }
    }, [reportStatus, reportPath, selectedReportSource]);

    const fetchReport = async (url: string) => {
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Network response was not ok');
            const text = await response.text();
            setFileContent(text);
            // Update filename based on selected report
            setFileName(`${selectedReportSource}_report.html`);
        } catch (error) {
            console.error('Error fetching the report:', error);
            showToast.error(t('workspace.aiReport.errorFetchingReport'));
        }
    };

    const getStatusMessage = () => {
        switch (reportStatus) {
            case 'PUBLISHED':
                return t('workspace.aiReport.loadingReport');
            case 'REPORT_GENERATE_PENDING':
                return t('workspace.aiReport.generating');
            case 'IN_PROGRESS':
                return t('workspace.aiReport.inProgress');
            default:
                return '';
        }
    };

    if (error) {
        return <div>{t('workspace.aiReport.errorLoadingReport')}</div>;
    }

    const handleDownload = () => {
        if (fileContent) {
            const blob = new Blob([fileContent], { type: 'text/html' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const handleFullscreen = () => {
        if (iframeRef.current?.requestFullscreen) {
            iframeRef.current.requestFullscreen();
        }
    };

    const handleNext = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/3?resumed=true`);
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/1?resumed=true`);
    };

    const handleSkip = async () => {
        try {
            const result = await createWorkspaceLayer({
                variables: {
                    dataInput: {
                        datasetRequestId: datasetRequestId,
                        mapDataColumns: JSON.stringify([]), // Empty array for skip
                        orgId
                    }
                }
            });

            if (result.data?.createWorkspaceLayer?.workspace) {
                showToast.success(t('alerts.success.workspaceCreated'));
                dispatch(updateStepData({ step: 2, data: {}, method: WorkspaceMethods.UPLOAD_FILE }));
                dispatch(resetSteps({ method: WorkspaceMethods.UPLOAD_FILE }));
                localStorage.setItem('currentWorkspaceTitle', result.data.createWorkspaceLayer.workspace.name)
                navigateWithOrg(`/${Routes.map}/${result.data.createWorkspaceLayer.workspace.id}?fromCreation=true&type=${WorkspaceMethods.UPLOAD_FILE}`);
            }
        } catch (error: any) {
            // Extract the specific validation message
            const validationError = error.graphQLErrors?.[0]?.extensions?.http?.reason?.jsonSchema?.[0];
            if (validationError) {
                showToast.error(validationError);
            } else {
                showToast.error(t('alerts.error.workspaceCreateError'));
            }
            console.error('Error creating workspace:', error);
        }
    };

    const handleReportSourceChange = (source: string) => {
        setFileContent(null);
        setSelectedReportSource(source);
    };

    return (
        <div className="max-w-full py-8 px-4">
            <div className="flex justify-between items-center mb-4">
                <div className={direction}>
                    <h2 className="text-2xl font-semibold text-gray-800 py-2">  {t('workspace.aiReport.title')}</h2>
                    <p className="text-gray-600">{t('workspace.aiReport.description')}</p>
                </div>
                <div className="flex gap-2">
                    <Button onClick={handleFullscreen} className="text-gray-700 bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">
                        <Maximize className="w-5 h-5 inline-block mr-2" />
                        {t('workspace.aiReport.fullscreen')}
                    </Button>
                    <Button onClick={handleDownload} className="text-gray-700 bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">
                        <Download className="w-5 h-5 inline-block mr-2" />
                        {t('workspace.aiReport.download')}
                    </Button>
                </div>
            </div>

            {/* Report source selector */}
            {edaReports.length > 1 && (
                <div className="mb-4">
                    <div className="flex gap-2">
                        {edaReports?.map((report: any) => (
                            <Button
                                key={report.hash}
                                onClick={() => handleReportSourceChange(report.source)}
                                className={`px-4 py-2 rounded-md ${selectedReportSource === report.source
                                    ? 'bg-[#5E58EE] text-white'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                    }`}
                            >
                                {report.source.charAt(0).toUpperCase() + report.source.slice(1)} Report
                            </Button>
                        ))}
                    </div>
                </div>
            )}

            <div className="bg-gray-100 border border-gray-300 rounded-md h-[34rem] p-4 mt-6 overflow-y-auto">
                {loading ? (
                    <AIReportSkeleton />
                ) : !selectedReport ? (
                    <div className="flex items-center justify-center h-full">
                        <span className="text-gray-400">{t('workspace.aiReport.loadingReport')}</span>
                    </div>
                ) : reportStatus !== 'PUBLISHED' ? (
                    <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                        <span className="text-gray-400">{getStatusMessage()}</span>
                    </div>
                ) : fileContent ? (
                    <iframe
                        ref={iframeRef}
                        srcDoc={fileContent}
                        title="Report Preview"
                        className="w-full h-full border-none"
                    />
                ) : (
                    <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-500 mr-2" />
                        <span className="text-gray-400">{t('workspace.aiReport.loadingReport')}</span>
                    </div>
                )}
            </div>

            <div className="flex justify-between items-center mt-6">
                <Button variant="outline" onClick={handlePrevious} className="text-gray-600 py-2 px-8 rounded-md">
                    {t('buttons.previous')}
                </Button>
                <div className="flex gap-4">
                    <Button
                        variant="outline"
                        onClick={handleSkip}
                        className="text-gray-600 py-2 px-8 rounded-md"
                        disabled={mutationLoading}
                    >
                        {t('buttons.skipToWorkspace')}
                    </Button>
                    <Button
                        onClick={handleNext}
                        className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                        disabled={mutationLoading}
                    >
                        {t('buttons.next')}
                    </Button>
                </div>
            </div>
        </div>
    );
};