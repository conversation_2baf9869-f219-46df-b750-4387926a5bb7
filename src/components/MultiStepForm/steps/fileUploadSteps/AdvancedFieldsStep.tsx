import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@apollo/client";
import { GET_DATASET_SAMPLES } from "@/shared/graphQl/queries/dataset";
import { CREATE_WORKSPACE } from "@/shared/graphQl";
import { CustomForm } from "@/components/RJSF/theme/CustomTheme";
import { Button } from "@/components/ui/button";
import { generateDynamicSchema, generateDynamicUiSchema } from '../../schemas/stepFiveSchema';
import { useDispatch } from "react-redux";
import { updateStepData, resetSteps } from "@/shared/store/slices/stepSlice";
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { showToast } from "@/shared/utils/toastConfig";
import { AdvancedFieldsSkeleton } from "../../skeletons/AdvancedFieldsSkeleton";
import {
    createColumnSamples,
    createColumnMappings,
    updateSchemaWithAvailableColumns,
    getSelectedColumnNames
} from "@/shared/utils/formUtils";
import { StepProgressManager } from "@/shared/utils/stepProgress";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

export const AdvancedFieldsStep = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const datasetRequestId = parseInt(localStorage.getItem("requestId") ?? "");
    const [formSchema, setFormSchema] = useState({});
    const [uiSchema, setUiSchema] = useState({});
    const [columnSamples, setColumnSamples] = useState<Record<string, string[]>>({});
    const [formData, setFormData] = useState<any>({});
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');
    const { navigateWithOrg } = useOrganizationManager()
    const [createWorkspaceLayer, { loading: mutationLoading }] = useMutation(CREATE_WORKSPACE);

    const { data: samplesData, loading } = useQuery(GET_DATASET_SAMPLES, {
        variables: {
            datasetRequestId, orgId
        },
        skip: !datasetRequestId,
    });

    useEffect(() => {
        if (samplesData?.datasetSampleData?.data) {
            const samples = createColumnSamples(samplesData);
            const mappings = createColumnMappings(samplesData);
            setColumnSamples(samples);

            // Get step 2 data from StepProgressManager
            const step2Progress = StepProgressManager.getProgress(1, WorkspaceMethods.UPLOAD_FILE);
            const previouslySelectedColumns: string[] = [];

            if (step2Progress.formData) {
                // Check which layout was used and get corresponding columns
                if (step2Progress.formData.columnLayout === 'عمود') {
                    if (step2Progress.formData.latLngColumn) {
                        previouslySelectedColumns.push(step2Progress.formData.latLngColumn);
                    }
                } else if (step2Progress.formData.columnLayout === 'عمودين') {
                    if (step2Progress.formData.latColumn) previouslySelectedColumns.push(step2Progress.formData.latColumn);
                    if (step2Progress.formData.lngColumn) previouslySelectedColumns.push(step2Progress.formData.lngColumn);
                }
            }

            // Filter out previously selected columns
            const availableMappings = mappings.filter(mapping =>
                !previouslySelectedColumns.includes(mapping.column)
            );

            // Generate dynamic schema based on available column count
            const columnCount = availableMappings.length > 6 ? 6 : availableMappings.length;
            const dynamicSchema = generateDynamicSchema(columnCount);
            const dynamicUiSchema = generateDynamicUiSchema(columnCount);

            // Update schema with available columns
            const updatedSchema = updateSchemaWithAvailableColumns(
                dynamicSchema,
                formData,
                availableMappings,
                samples,
                'advanced'
            );

            setFormSchema(updatedSchema);
            setUiSchema(dynamicUiSchema);
        }
    }, [samplesData, formData]);

    const handleChange = ({ formData: newFormData }: any) => {
        setFormData(newFormData);
    };

    const handleSubmit = async ({ formData }: { formData: any }) => {
        try {
            // Get selected column names (these are already column names, not display names)
            const selectedColumns = getSelectedColumnNames(formData);

            const result = await createWorkspaceLayer({
                variables: {
                    dataInput: {
                        datasetRequestId: datasetRequestId,
                        mapDataColumns: JSON.stringify(selectedColumns), // Send column names to backend
                        orgId
                    }
                }
            });

            if (result.data?.createWorkspaceLayer?.workspace) {
                showToast.success(t('alerts.success.workspaceCreated'));
                dispatch(updateStepData({ step: 4, data: formData, method: WorkspaceMethods.UPLOAD_FILE }));
                dispatch(resetSteps({ method: WorkspaceMethods.UPLOAD_FILE }));
                localStorage.setItem('currentWorkspaceTitle', result.data.createWorkspaceLayer.workspace.name)
                navigateWithOrg(`/${Routes.map}/${result.data.createWorkspaceLayer.workspace.id}?fromCreation=true&type=${WorkspaceMethods.UPLOAD_FILE}`);
            }
        } catch (error: any) {
            // Extract the specific validation message
            const validationError = error.graphQLErrors?.[0]?.extensions?.http?.reason?.jsonSchema?.[0];
            if (validationError) {
                showToast.error(validationError);
            } else {
                showToast.error(t('alerts.error.workspaceCreateError'));
            }
            console.error('Error creating workspace:', error);
        }
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/3?resumed=true`);
    };

    const handleSkip = async () => {
        try {
            const result = await createWorkspaceLayer({
                variables: {
                    dataInput: {
                        datasetRequestId: datasetRequestId,
                        mapDataColumns: JSON.stringify([]), // Empty array for skip
                        orgId
                    }
                }
            });

            if (result.data?.createWorkspaceLayer?.workspace) {
                showToast.success(t('alerts.success.workspaceCreated'));
                dispatch(updateStepData({ step: 4, data: {}, method: WorkspaceMethods.UPLOAD_FILE }));
                dispatch(resetSteps({ method: WorkspaceMethods.UPLOAD_FILE }));
                localStorage.setItem('currentWorkspaceTitle', result.data.createWorkspaceLayer.workspace.name)
                navigateWithOrg(`/${Routes.map}/${result.data.createWorkspaceLayer.workspace.id}?fromCreation=true&type=${WorkspaceMethods.UPLOAD_FILE}`);
            }
        } catch (error: any) {
            console.error('Error creating workspace:', error);
        }
    };

    return (
        <div className="w-full py-8 px-4">
            <h2 className="text-xl font-bold mb-4 text-gray-800">
                {t('workspace.advancedFields.title')}
            </h2>
            <p className="text-sm text-gray-500 mb-6">{t('workspace.advancedFields.description')}</p>
            {loading ? (
                <AdvancedFieldsSkeleton />
            ) : samplesData ? (
                <CustomForm
                    schema={formSchema}
                    uiSchema={uiSchema}
                    onSubmit={handleSubmit}
                    onChange={handleChange}
                    formContext={{ columnSamples }}
                    formData={formData}
                >
                    <div className="flex justify-between items-center mt-6">
                        <Button variant="outline" onClick={handlePrevious} className="text-gray-600 py-2 px-8 rounded-md">
                            {t('buttons.previous')}
                        </Button>
                        <div className="flex gap-4">
                            <Button
                                variant="outline"
                                onClick={handleSkip}
                                className="text-gray-600 py-2 px-8 rounded-md"
                                disabled={mutationLoading}
                            >
                                {t('buttons.skipToWorkspace')}
                            </Button>
                            <Button
                                type="submit"
                                className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                            >
                                {mutationLoading ? t('buttons.processing') : t('buttons.create')}
                            </Button>
                        </div>
                    </div>
                </CustomForm>
            ) : (
                <div>{t('workspace.advancedFields.noData')}</div>
            )}
        </div>
    );
};