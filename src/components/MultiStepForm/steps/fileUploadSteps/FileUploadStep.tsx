import { useState, useEffect } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useMutation, useQuery } from '@apollo/client';
import validator from '@rjsf/validator-ajv8';
import { updateStepData } from '@/shared/store/slices/stepSlice';
import { CREATE_DATASET, CANCEL_DATASET_REQUEST } from '@/shared/graphQl/mutations/dataset';
import { FETCH_DATASET_REQUESTS } from '@/shared/graphQl/queries/dataset';
import { showToast } from '@/shared/utils/toastConfig';
import { SignedUrlService } from '@/shared/graphQl/services/signedUrl.service';
import { StepProgressManager } from '@/shared/utils/stepProgress';
import { Routes, WorkspaceMethods } from '@/shared/utils/routes';
import { stepOneSchema, stepOneSchemaWithWorkspaceID, stepOneUiSchema } from '../../schemas/stepOneSchema';
import { Button } from '@/components/ui/button';
import { Typography } from '@/components/ui/Typography';
import { CustomForm } from '@/components/RJSF/theme/CustomTheme';
import { IncompleteWorkspaceDialog } from '../../components/IncompleteWorkspaceDialog';
import { determineCurrentStep, filterEmptyProperties } from '@/shared/utils/generals';
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useTranslation } from 'react-i18next';

interface FormData {
    layerName: string;
    layerColor: string;
    fileUpload: File;
    layerDescription?: string;
    showOnly: boolean;
}

export const FileUploadStep: React.FC = () => {
    const [showDialog, setShowDialog] = useState(false);
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const UploadSignedUrlFile = new SignedUrlService();
    const location = useLocation();
    const [searchParams] = useSearchParams();

    // Get workspaceId from URL parameters
    const workspaceId = searchParams.get('workspaceId');
    const isAddingToExistingWorkspace = !!workspaceId;

    const savedProgress = StepProgressManager.getProgress(0, WorkspaceMethods.UPLOAD_FILE);
    const isReadOnly = savedProgress.status === 'complete';

    const { navigateWithOrg } = useOrganizationManager();
    const orgId = searchParams.get('orgId');

    // Mutation to create a dataset
    const [createDataset, { loading }] = useMutation(CREATE_DATASET, {
        onError: (error) => {
            console.error('Error creating dataset:', error);
            showToast.error(t('workspace.fileUpload.alerts.uploadError'));
        },
    });

    const [cancelWorkspaceRequest] = useMutation(CANCEL_DATASET_REQUEST);

    const { data: requestsData } = useQuery(FETCH_DATASET_REQUESTS, {
        variables: {
            orgId
        }
    });

    useEffect(() => {
        if (requestsData?.workspaceRequests?.count > 0) {
            const uploadFileRequest = requestsData.workspaceRequests.data.find(
                (request: any) => request.requestType === WorkspaceMethods.UPLOAD_FILE
            );

            if (uploadFileRequest?.id) {
                const { layerData, dataset, id } = uploadFileRequest;
                // Check if this request already has a workspace ID
                const existingWorkspaceId = dataset?.workspace?.id;
                const effectiveWorkspaceId = existingWorkspaceId || workspaceId;
                // Save step 0 data (File Upload)
                StepProgressManager.saveProgress(0, {
                    layerName: layerData.title,
                    layerColor: layerData.color,
                    fileUpload: dataset.file,
                    layerDescription: layerData.description,
                    showOnly: layerData.read_only,
                    requestId: id,
                    workspaceId: effectiveWorkspaceId
                }, WorkspaceMethods.UPLOAD_FILE);

                // Save step 1 data (Set Columns)
                if (layerData.location_field_mapping) {
                    const setColumnsData = {
                        coordinateType: layerData.location_field_mapping.coordinate_type === 'point' ? 'نقطة' : 'أخرى',
                        columnLayout: layerData.location_field_mapping.lat_lon_column_num === 'two_column' ? 'عمودين' : 'عمود',
                        latColumn: layerData.location_field_mapping.latitude_column,
                        lngColumn: layerData.location_field_mapping.longitude_column,
                        latLngColumn: layerData.location_field_mapping.lang_lat_column
                    };
                    // Filter out empty properties
                    const filteredSetColumnsData = filterEmptyProperties(setColumnsData);
                    StepProgressManager.saveProgress(1, filteredSetColumnsData, WorkspaceMethods.UPLOAD_FILE);
                }

                // Save step 2 data (AI Report)
                if (dataset.metaData.eda_report?.status === 'PUBLISHED') {
                    StepProgressManager.saveProgress(2, {
                        reportPath: dataset.metaData.eda_report.report_path
                    }, WorkspaceMethods.UPLOAD_FILE);
                }

                // Save step 3 data (JSON Schema)
                if (layerData.json_schema) {
                    StepProgressManager.saveProgress(3, {
                        jsonSchema: layerData.json_schema,
                        webUiJsonSchema: layerData.web_ui_json_schema
                    }, WorkspaceMethods.UPLOAD_FILE);
                }

                localStorage.setItem('requestId', id.toString());
                setShowDialog(true);
            }
        } else {
            StepProgressManager.clearProgress(WorkspaceMethods.UPLOAD_FILE);
            localStorage.removeItem('requestId');
        }
    }, [requestsData]);

    const handleContinue = () => {
        const currentStep = determineCurrentStep();
        setShowDialog(false);

        // Preserve workspaceId in URL when continuing
        const params = new URLSearchParams(searchParams);
        params.set('resumed', 'true');
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/${currentStep}?${params.toString()}`);
    };

    const handleDecline = async () => {
        const requestId = parseInt(requestsData?.workspaceRequests?.data[0]?.id);

        if (requestId) {
            await cancelWorkspaceRequest({
                variables: {
                    closeInput: {
                        datasetRequestId: requestId,
                        orgId
                    }
                }
            });
        }

        setShowDialog(false);
        StepProgressManager.clearProgress(WorkspaceMethods.UPLOAD_FILE);
        localStorage.removeItem('requestId');

        // Preserve workspaceId in URL when declining
        const params = new URLSearchParams(searchParams);
        params.set('resumed', 'true');

        // If we're adding to an existing workspace and declining, go back to that workspace
        if (isAddingToExistingWorkspace) {
            navigateWithOrg(`/${Routes.map}/${workspaceId}`);
        } else {
            navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/0?${params.toString()}`);
        }
    };

    const handleSubmit = async ({ formData }: { formData: FormData }) => {
        try {
            // Prepare variables for the mutation
            const dataInput: {
                title: string;
                color: string;
                datasetFile: File;
                description: string;
                readOnly: boolean;
                orgId: any;
                workspaceId?: number;
            } = {
                title: formData.layerName,
                color: formData.layerColor,
                datasetFile: formData.fileUpload,
                description: formData.layerDescription || '',
                readOnly: formData.showOnly,
                orgId
            };

            // Add workspaceId if we're adding to an existing workspace
            if (isAddingToExistingWorkspace) {
                dataInput.workspaceId = parseInt(workspaceId);
            }

            // Trigger dataset creation mutation
            const { data } = await createDataset({
                variables: {
                    dataInput
                }
            });

            if (data?.createDataset?.datasetRequest) {
                const requestId = data.createDataset.datasetRequest.id;
                localStorage.setItem('requestId', requestId);
                showToast.success(t('workspace.fileUpload.alerts.uploadSuccess'));

                dispatch(updateStepData({
                    step: 0,
                    data: { ...formData, requestId },
                    method: WorkspaceMethods.UPLOAD_FILE
                }));

                // Preserve workspaceId in URL when navigating
                const params = new URLSearchParams(searchParams);
                params.set('resumed', 'true');
                navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/1?${params.toString()}`);
            }
        } catch (error) {
            console.error('Error creating dataset:', error);
        }
    };
    // Handle file changes
    const onFileChange = async (file: File) => {
        try {
            return await UploadSignedUrlFile.uploadViaSignedUrl(file);
        }
        catch (error) {
            console.error('Error uploading file:', error);
            showToast.error(t('workspace.fileUpload.alerts.uploadError'));
        }
    };

    return (
        <>
            {showDialog && !location.search.includes('resumed') && (
                <IncompleteWorkspaceDialog
                    onContinue={handleContinue}
                    onDecline={handleDecline}
                />
            )}
            <div className="max-w-lg py-8 px-4">
                <Typography variant="h1" className="text-2xl font-bold mb-2">
                    {t('workspace.fileUpload.title')}
                </Typography>
                <Typography className="text-gray-600 mb-6">
                    {t('workspace.fileUpload.description')}
                </Typography>
                <CustomForm
                    formContext={{ onFileChange }}
                    schema={workspaceId ? stepOneSchemaWithWorkspaceID : stepOneSchema}
                    uiSchema={stepOneUiSchema}
                    validator={validator}
                    onSubmit={handleSubmit}
                    formData={savedProgress?.formData ?? {}}
                >
                    <Button
                        type="submit"
                        variant="default"
                        className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                        disabled={loading || isReadOnly || showDialog}
                    >
                        {loading ? t('buttons.processing') : t('buttons.next')}
                    </Button>
                </CustomForm>
            </div>
        </>
    );
};