import { FileUploadStep } from './fileUploadSteps/FileUploadStep';
import { EDAReportStep } from './fileUploadSteps/AIReportStep';
import { SetColumnsStep } from './fileUploadSteps/SetColumnsStep';
import { AdvancedFieldsStep } from './fileUploadSteps/AdvancedFieldsStep';
import { ShowPlanStep } from './fileUploadSteps/ShowPlanStep';
import { useParams } from 'react-router-dom';
import { LayerPropertiesStep } from './designLayerSteps/LayerPropertiesStep';
import { LayerFormStep } from './designLayerSteps/LayerFormStep';
import { WorkspaceMethods } from '@/shared/utils/routes';

export const FormSteps: React.FC = () => {
    const { step = '0', method = WorkspaceMethods.UPLOAD_FILE } = useParams();
    const currentStep = parseInt(step);

    const fileUploadSteps = {
        0: <FileUploadStep />,
        1: <SetColumnsStep />,
        2: <EDAReportStep />,
        3: <ShowPlanStep />,
        4: <AdvancedFieldsStep />
    };

    const designLayerSteps = {
        0: <LayerPropertiesStep />,
        1: <LayerFormStep />
    };

    const steps = method === WorkspaceMethods.UPLOAD_FILE ? fileUploadSteps : designLayerSteps as any;

    return <div>{steps[currentStep] || steps[0]}</div>;
};

