import { Button } from "@/components/ui/button"
import {
    <PERSON>,
    Card<PERSON>ontent,
    CardFooter,
} from "@/components/ui/card"
import { useTranslation } from "react-i18next"

interface IncompleteWorkspaceDialogProps {
    onContinue: () => void
    onDecline: () => void
    onCancel?: () => void
}

export const IncompleteWorkspaceDialog: React.FC<IncompleteWorkspaceDialogProps> = ({
    onContinue,
    onDecline,

}) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()

    return (
        <Card className="fixed top-[15rem] rtl:left-[15rem] ltr:right-[-12rem] -translate-x-1/2 -translate-y-1/2 w-[400px] shadow-lg" dir={direction}>
            <CardContent className="pt-6">
                <div className="space-y-2">
                    <h3 className="text-lg font-semibold">{t('workspace.incomplete.title')}</h3>
                    <p className="text-sm text-muted-foreground">
                        {t('workspace.incomplete.description')}
                    </p>
                </div>
            </CardContent>
            <CardFooter className="flex justify-start gap-2">
                <Button
                    className="bg-[#6366f1] hover:bg-[#5558e7]"
                    onClick={onContinue}
                >
                    {t('buttons.yes')}
                </Button>
                <Button
                    variant="outline"
                    onClick={onDecline}
                >
                    {t('buttons.no')}
                </Button>
            </CardFooter>
        </Card>
    )
}