import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Check, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { setSortBy, setSortOrder } from "@/shared/store/slices/workspaceSlice";
import { RootState } from "@/shared/store/store";
import { useTranslation } from "react-i18next";

export type SortField = "created" | "last_visited";
export type SortOrder = "asc" | "desc";

export const SortFilterDropdown: React.FC = () => {
    const dispatch = useDispatch();
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const sortField = useSelector((state: RootState) => state.workspace.sortBy);
    const sortOrder = useSelector((state: RootState) => state.workspace.sortOrder || "desc");
    const [open, setOpen] = React.useState(false);


    const handleSortFieldChange = (field: SortField) => {
        dispatch(setSortBy(field));
        setOpen(false);
    };

    const handleSortOrderChange = (order: SortOrder) => {
        dispatch(setSortOrder(order));
        setOpen(false);
    };

    return (
        <DropdownMenu open={open} onOpenChange={setOpen} dir={direction}>
            <DropdownMenuTrigger asChild className="no-border-focus">
                <Button
                    variant="outline"
                    className="w-40 justify-between gap-2"
                >
                    {t('sortFiler')}
                    <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-40">
                <DropdownMenuLabel>{t('sortBy')}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                    <DropdownMenuItem onClick={() => handleSortFieldChange("created")}>
                        <div className="flex items-center justify-between w-full mx-1">
                            <span>{t('creationDate')}</span>
                            {sortField === "created" && <Check className="h-4 w-4" />}
                        </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSortFieldChange("last_visited")}>
                        <div className="flex items-center justify-between w-full mx-1">
                            <span>{t('lastVisited')}</span>
                            {sortField === "last_visited" && <Check className="h-4 w-4" />}
                        </div>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>{t('order')}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                    <DropdownMenuItem onClick={() => handleSortOrderChange("asc")}>
                        <div className="flex items-center justify-between w-full mx-1">
                            <span>{t('oldestFirst')}</span>
                            {sortOrder === "asc" && <Check className="h-4 w-4" />}
                        </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSortOrderChange("desc")}>
                        <div className="flex items-center justify-between w-full mx-1">
                            <span>{t('newestFirst')}</span>
                            {sortOrder === "desc" && <Check className="h-4 w-4" />}
                        </div>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};