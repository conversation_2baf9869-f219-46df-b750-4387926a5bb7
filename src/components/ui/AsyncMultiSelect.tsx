import React, { useCallback, useState } from 'react';
import Select, { MultiValue, MenuListProps, components } from 'react-select';
import { useTranslation } from 'react-i18next';

export interface SelectOption {
  value: string;
  label: string;
  [key: string]: any;
}

interface PaginatedLoadResult {
  options: SelectOption[];
  hasMore: boolean;
  total: number;
}

interface AsyncMultiSelectProps {
  loadOptions: (inputValue: string, offset?: number) => Promise<SelectOption[] | PaginatedLoadResult>;
  defaultOptions?: SelectOption[];
  value?: SelectOption[];
  onChange?: (selected: SelectOption[]) => void;
  placeholder?: string;
  isDisabled?: boolean;
  isClearable?: boolean;
  closeMenuOnSelect?: boolean;
  menuPlacement?: 'auto' | 'bottom' | 'top';
  className?: string;
  id?: string;
  name?: string;
  isMulti?: boolean;
  supportsPagination?: boolean;
}

export const AsyncMultiSelect: React.FC<AsyncMultiSelectProps> = ({
  loadOptions,
  defaultOptions = [],
  value = [],
  onChange,
  placeholder = 'layerSettings.filters.select.select',
  isDisabled = false,
  isClearable = true,
  menuPlacement = 'auto',
  className,
  id,
  name,
  isMulti = true,
}) => {
  const [allOptions, setAllOptions] = useState<SelectOption[]>(defaultOptions);
  const [hasMore, setHasMore] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSearchTerm, setCurrentSearchTerm] = useState('');
  const { t, i18n } = useTranslation();
  const direction = i18n.dir();

  const handleSearch = useCallback(
    async (inputValue: string) => {
      setIsLoading(true);
      try {
        const result = await loadOptions(inputValue, 0);
        const newOptions = Array.isArray(result) ? result : result.options;
        const hasMoreResults = Array.isArray(result) ? false : result.hasMore;

        setCurrentSearchTerm(inputValue);
        setAllOptions(newOptions);
        setHasMore(hasMoreResults);
      } catch (err) {
        console.error('Search failed:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [loadOptions]
  );

  const handleLoadMore = useCallback(async () => {
    if (!hasMore || isLoading) return;

    setIsLoading(true);
    try {
      const result = await loadOptions(currentSearchTerm, allOptions.length);
      const newOptions = Array.isArray(result) ? result : result.options;
      const hasMoreResults = Array.isArray(result) ? false : result.hasMore;

      setAllOptions((prev) => [...prev, ...newOptions]);
      setHasMore(hasMoreResults);
    } catch (err) {
      console.error('Load more failed:', err);
    } finally {
      setIsLoading(false);
    }
  }, [loadOptions, currentSearchTerm, allOptions.length, hasMore, isLoading]);

  const handleChange = (newValue: MultiValue<SelectOption> | SelectOption | null) => {
    if (isMulti) {
      onChange?.([...(newValue as MultiValue<SelectOption>)]);
    } else {
      onChange?.(newValue ? [newValue as SelectOption] : []);
    }
  };

  const MenuList = (props: MenuListProps<SelectOption, boolean>) => {
    return (
      <components.MenuList {...props}>
        {props.children}
        {hasMore && (
          <div
            className="px-3 py-2 text-center text-sm text-gray-500 hover:bg-gray-100 cursor-pointer border-t"
            onClick={handleLoadMore}
          >
            {isLoading ? t('loading') : t('loadMore')}
          </div>
        )}
      </components.MenuList>
    );
  };

  return (
    <Select
      isMulti={isMulti}
      value={isMulti ? value : value?.[0] || null}
      onChange={handleChange}
      options={allOptions}
      onInputChange={(input, { action }) => {
        if (action === 'input-change') {
          handleSearch(input);
        }
        return input;
      }}
      placeholder={t(placeholder)}
      isDisabled={isDisabled}
      isClearable={isClearable}
      closeMenuOnSelect={isMulti ? false : true}
      blurInputOnSelect={isMulti ? false : true}
      backspaceRemovesValue={false}
      className={`async-multi-select ${direction} ${className || ''}`}
      classNamePrefix="select"
      menuPlacement={menuPlacement}
      loadingMessage={() => t('loading')}
      noOptionsMessage={({ inputValue }) =>
        inputValue
          ? isLoading
            ? t('loading')
            : t('layerSettings.filters.select.noResults')
          : t('layerSettings.filters.select.startTyping')
      }
      id={id}
      name={name}
      components={{ MenuList }}
      styles={{
        valueContainer: (provided) => ({
          ...provided,
          maxHeight: '200px',
          overflow: 'auto',
        }),
        menuList: (provided) => ({
          ...provided,
          maxHeight: '200px',
          overflowY: 'auto',
          position: 'relative',
          paddingBottom: 0,
        }),
        menu: (provided) => ({
          ...provided,
          maxHeight: '200px',
        }),
        control: (provided, state) => ({
          ...provided,
          borderColor: state.isFocused ? 'black' : provided.borderColor,
          boxShadow: state.isFocused ? '0 0 0 1px black' : provided.boxShadow,
          '&:hover': {
            borderColor: state.isFocused ? 'black' : provided.borderColor,
          },
        }),
      }}
    />
  );
};