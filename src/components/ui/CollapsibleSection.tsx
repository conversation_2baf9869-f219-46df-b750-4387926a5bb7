import React, { ReactNode } from 'react';
import { MinusIcon, Plus } from 'lucide-react';

interface CollapsibleSectionProps {
    title: string;
    isOpen: boolean;
    onToggle: () => void;
    children: ReactNode;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
    title,
    isOpen,
    onToggle,
    children,
}) => {
    return (
        <div className="border-none !m-0">
            <button
                onClick={onToggle}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 transition-colors"
            >
                <span className="text-sm font-medium">{title}</span>
                {isOpen ? (
                    <MinusIcon className="h-4 w-4" />
                ) : (
                    <Plus className="h-4 w-4" />
                )}
            </button>
            {isOpen && (
                <div className="p-3 px-0 border-t space-y-4">
                    {children}
                </div>
            )}
        </div>
    );
};