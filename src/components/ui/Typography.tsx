import React from 'react';

interface TypographyProps extends React.HTMLAttributes<HTMLParagraphElement> {
    variant?: 'h1' | 'h2' | 'h3' | 'body' | 'caption';
}

export const Typography: React.FC<TypographyProps> = ({
    variant = 'body',
    children,
    ...props
}) => {
    const classNames = {
        h1: 'text-4xl font-bold',
        h2: 'text-3xl font-semibold',
        h3: 'text-2xl font-medium',
        body: 'text-base',
        caption: 'text-sm text-gray-500',
    };

    return (
        <p className={classNames[variant]} {...props}>
            {children}
        </p>
    );
};
