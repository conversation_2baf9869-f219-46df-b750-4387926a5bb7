import { useEffect, useState } from 'react'
import { Button } from "@/components/ui/button"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronDown } from 'lucide-react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { Routes } from '@/shared/utils/routes'
import { setSelectedWorkspace } from '@/shared/store/slices/workspaceSlice'
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import { setChatActiveLayers, setSelectedMainLayer, setSelectedTableLayer } from '@/shared/store/slices/mapSlice'

export const WorkspaceDropdown = () => {
    const { t, i18n } = useTranslation();
    const { workspaceId } = useParams();
    const direction = i18n.dir()
    const dispatch = useDispatch()
    const [isOpen, setIsOpen] = useState(false)
    const recentWorkspaces = useSelector((state: RootState) => state.workspace.workspaces)
    const { navigateWithOrg } = useOrganizationManager()
    const [currentWorkspaceName, setCurrentWorkspaceName] = useState(
        localStorage.getItem('currentWorkspaceTitle') ?? t('recentWorkspaces')
    )

    useEffect(() => {
        const currentWorkspace = recentWorkspaces.find(w => String(w.id) === workspaceId)

        if (currentWorkspace) {
            dispatch(setSelectedWorkspace(currentWorkspace))
            setCurrentWorkspaceName(currentWorkspace.name)
        }
    }, [workspaceId, recentWorkspaces])

    const handleWorkspaceClick = (workspace: any) => {
        if (workspace) {
            dispatch(setSelectedMainLayer(null));
            dispatch(setSelectedWorkspace(workspace));
            dispatch(setSelectedTableLayer(null))
            dispatch(setChatActiveLayers([]));

            document.title = workspace.name;
            localStorage.setItem('currentWorkspaceTitle', workspace.name);
            setCurrentWorkspaceName(workspace.name);

            navigateWithOrg(`/${Routes.map}/${workspace.id}?type=${workspace.workspaceType}`);
        }
    }

    const handleViewAllClick = () => {
        localStorage.removeItem('currentWorkspaceTitle')
        document.title = 'GeoCore'
        dispatch(setSelectedWorkspace(null))
        navigateWithOrg(`/${Routes.marketSpaces}`)
    }

    return (
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen} >
            <DropdownMenuTrigger asChild className='border-none p-0 m-0 h-fit' >
                <Button
                    variant="ghost"
                    className="flex items-center  border-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                >
                    {currentWorkspaceName}
                    <ChevronDown className="rtl:mr-2 ltr:ml-2 h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center" className={direction}>
                {recentWorkspaces.map((workspace) => (
                    <DropdownMenuItem
                        key={workspace.id}
                        className="flex items-center"
                        onClick={() => handleWorkspaceClick(workspace)}
                    >
                        {workspace.thumbnail && (
                            <img
                                src={workspace.thumbnail}
                                alt={workspace.name}
                                className="w-6 h-6 rtl:ml-2 ltr:mr-2"
                            />
                        )}
                        {workspace.name}
                    </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleViewAllClick} className='text-center text-[#7E7AF2] flex justify-center' >
                    {t('allWorkspaces')}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
