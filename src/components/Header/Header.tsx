import { useState } from 'react'
import logo from "@/assets/logo.svg"
import { UserDropdown } from '../UserDropdown/UserDropdown'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuGroup,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { OrganizationSwitcher } from '../OrganizationSwitcher/OrganizationSwitcher'
import { useLocation } from 'react-router-dom'
import { ArrowLeftRight, Settings, ChevronDown } from "lucide-react"
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager'
import { useTranslation } from 'react-i18next'
import LanguageSwitcher from '../LanguageSwitcher'
import { Routes } from '@/shared/utils/routes'
import { Button } from "@/components/ui/button"

export const Header = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const location = useLocation()
    const [showOrgModal, setShowOrgModal] = useState(false)
    const [dropdownOpen, setDropdownOpen] = useState(false)
    const { selectedOrg, navigateWithOrg, count } = useOrganizationManager()

    const isMap = location.pathname.includes('map')

    const handleOrgModalToggle = () => {
        setDropdownOpen(false)
        setShowOrgModal(!showOrgModal)
    }

    const handleLogoClick = () => {
        localStorage.removeItem('currentWorkspaceTitle')
        document.title = 'GeoCore'
        navigateWithOrg(`/${Routes.marketSpaces}`)
    }

    return (
        <header className="w-full bg-white py-2 px-2 md:px-4 lg:px-6 border-b border-r-slate-300">
            <div className="mx-auto flex items-center justify-between">
                <div className='flex items-center justify-center gap-2'>
                    <img
                        src={logo}
                        alt="GeoCore Logo"
                        className="w-8 h-8 object-contain cursor-pointer"
                        onClick={handleLogoClick}
                    />
                    <hr className="w-px h-6 bg-gray-400 mx-4" />
                    <h4 className="font-[500]">{t("home_page")}</h4>
                </div>
                <div className="flex items-center space-x-4 gap-4">
                    {!isMap && (
                        <>
                            <LanguageSwitcher />
                            {count ? (
                                <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen} dir={direction}>
                                    <DropdownMenuTrigger asChild className='no-border-focus'>
                                        <Button
                                            variant="outline"
                                            className="flex items-center justify-between gap-2 w-auto min-w-[9rem] px-3"
                                            dir={direction}
                                        >
                                            <div className="flex items-center gap-2">
                                                {selectedOrg?.settings?.full_logo && (
                                                    <img
                                                        src={selectedOrg.settings.full_logo}
                                                        alt={`${selectedOrg.settings.name} logo`}
                                                        className="w-6 h-6 rounded-full object-contain flex-shrink-0 "
                                                        onError={(e) => {
                                                            e.currentTarget.style.display = 'none';
                                                        }}
                                                    />
                                                )}
                                                <span className="truncate">{selectedOrg?.settings?.name}</span>
                                            </div>
                                            <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="w-40">
                                        <DropdownMenuGroup>
                                            <DropdownMenuItem onClick={handleOrgModalToggle} disabled={count <= 1}>
                                                <ArrowLeftRight className="h-4 w-4 mr-2" />
                                                {t('switchOrganization')}
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => navigateWithOrg(`/dashboard/organization/settings`)}>
                                                <Settings className="h-4 w-4 mr-2" />
                                                {t('organizationSettings')}
                                            </DropdownMenuItem>
                                        </DropdownMenuGroup>
                                    </DropdownMenuContent>
                                    <OrganizationSwitcher
                                        open={showOrgModal}
                                        onOpenChange={setShowOrgModal}
                                    />
                                </DropdownMenu>
                            ) : ""}
                        </>
                    )}
                    <UserDropdown />
                </div>
            </div>
        </header>
    )
}