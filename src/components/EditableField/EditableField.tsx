import { useState, KeyboardEvent, FocusEvent } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Edit, Check, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface EditableFieldProps {
    value: string;
    onSave: (newValue: string) => void;
    className?: string;
    inputClassName?: string;
    placeholder?: string;
    label?: string;
    disabled?: boolean;
}

export const EditableField = ({
    value,
    onSave,
    className,
    inputClassName,
    placeholder,
    label,
    disabled = false,
}: EditableFieldProps) => {
    const [isEditing, setIsEditing] = useState(false);
    const [newValue, setNewValue] = useState(value);

    const handleEdit = () => {
        if (disabled) return;
        setIsEditing(true);
    };

    const handleSave = () => {
        onSave(newValue);
        setIsEditing(false);
    };

    const handleCancel = () => {
        setNewValue(value);
        setIsEditing(false);
    };

    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter") {
            handleSave();
        }
        if (e.key === "Escape") {
            handleCancel();
        }
    };

    const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
        // Only handle blur if not clicking on the save/cancel buttons
        if (
            !e.relatedTarget ||
            !(e.relatedTarget as HTMLElement).classList.contains("edit-action-btn")
        ) {
            handleSave();
        }
    };

    return (
        <div className={cn("relative", className)}>
            {label && <div className="text-sm font-medium mb-1">{label}</div>}
            {isEditing ? (
                <div className="flex items-center gap-2">
                    <Input
                        type="text"
                        value={newValue}
                        onChange={(e) => setNewValue(e.target.value)}
                        onKeyDown={handleKeyDown}
                        onBlur={handleBlur}
                        className={cn("no-border-focus", inputClassName)}
                        placeholder={placeholder}
                        autoFocus
                    />
                    <div className="flex gap-1">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 edit-action-btn"
                            onClick={handleSave}
                        >
                            <Check className="h-4 w-4 text-green-600" />
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 edit-action-btn"
                            onClick={handleCancel}
                        >
                            <X className="h-4 w-4 text-red-600" />
                        </Button>
                    </div>
                </div>
            ) : (
                <div
                    className="flex items-center justify-between"
                    onDoubleClick={handleEdit}
                >
                    <div className={cn("text-sm", disabled && "opacity-70")}>
                        {value || placeholder}
                    </div>
                    {!disabled && (
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={handleEdit}
                        >
                            <Edit className="h-4 w-4" />
                        </Button>
                    )}
                </div>
            )}
        </div>
    );
};
