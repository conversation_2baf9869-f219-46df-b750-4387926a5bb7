import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON> } from './ui/button'
import { Globe } from 'lucide-react'

const LanguageSwitcher = () => {
  const { i18n } = useTranslation()

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en'
    localStorage.setItem('preferredLanguage', newLang)
    i18n.changeLanguage(newLang)
  }

  return (
    <Button
      onClick={toggleLanguage}
      variant="ghost"
      className="rounded-sm hover:bg-transparent flex items-center gap-2"
    >
      {i18n.language === 'en' ? 'العربية' : 'English'}
      <Globe className="h-4 w-4" />
    </Button>
  )
}

export default LanguageSwitcher
