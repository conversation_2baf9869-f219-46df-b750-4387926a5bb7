import React from 'react';
import { EnhancedSchemaVisualizer } from '../EnhancedSchemaVisualizer';

// Your complex schema example
const complexSchema = {
  "type": "object",
  "allOf": [
    {
      "if": {
        "properties": {
          "realestate_multiple_land": {
            "const": "yes"
          }
        }
      },
      "then": {
        "$ref": "#/definitions/multiple_lands_yes"
      }
    },
    {
      "if": {
        "properties": {
          "realestate_multiple_land": {
            "const": "no"
          }
        }
      },
      "then": {
        "properties": {
          "land_realestate_facts": {
            "$ref": "#/definitions/land_realestate_facts"
          }
        }
      }
    }
  ],
  "title": "تفاصيل العقار",
  "$schema": "http://json-schema.org/draft-07/schema#",
  "required": [
    "realestate_multiple_land"
  ],
  "properties": {
    "field_employee_data": {
      "type": "object",
      "title": "حقائق الماسح الميداني",
      "properties": {
        "extra_images": {
          "$ref": "#/definitions/multiple_images_upload",
          "title": "صور إضافيه"
        },
        "descriptive_proof": {
          "type": "string",
          "title": "الإثبات الوصفي"
        }
      }
    },
    "realestate_multiple_land": {
      "$ref": "#/definitions/yes_no",
      "title": "هل العقار يقع على أكثر من أرض؟",
      "default": "no"
    }
  },
  "definitions": {
    "yes_no": {
      "enum": [
        "yes",
        "no"
      ],
      "type": "string",
      "default": "no",
      "enumNames": [
        "نعم",
        "لا"
      ]
    },
    "multiple_images_upload": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "multiple_lands_yes": {
      "type": "object",
      "allOf": [
        {
          "if": {
            "properties": {
              "form_filled_before": {
                "const": "yes"
              }
            }
          },
          "then": {
            "required": [
              "original_form_number"
            ],
            "properties": {
              "original_form_number": {
                "type": "number",
                "title": "ماهو رقم النموذج الأساس؟",
                "minimum": 1
              }
            }
          }
        },
        {
          "if": {
            "properties": {
              "form_filled_before": {
                "const": "no"
              }
            }
          },
          "then": {
            "properties": {
              "land_realestate_facts": {
                "$ref": "#/definitions/land_realestate_facts"
              }
            }
          }
        }
      ],
      "properties": {
        "form_filled_before": {
          "$ref": "#/definitions/yes_no",
          "title": "هل تم تعبئة بيانات العقار في نموذج آخر؟",
          "default": "no"
        }
      }
    },
    "land_realestate_facts": {
      "type": "object",
      "title": "",
      "required": [
        "building_exists",
        "realestate_images"
      ],
      "properties": {
        "building_exists": {
          "$ref": "#/definitions/yes_no",
          "title": "هل يوجد مبنى؟",
          "default": "no"
        },
        "realestate_images": {
          "$ref": "#/definitions/multiple_images_upload",
          "title": "صورة للعقار",
          "minItems": 1
        }
      }
    }
  }
};

/**
 * Test component for the enhanced schema visualizer with complex schema
 */
export const TestComplexSchema: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Enhanced Schema Visualizer Test</h1>
      <p className="text-gray-600 mb-6">
        Testing with a complex JSON schema containing conditional logic, definitions, and references.
      </p>
      
      <div className="border rounded-lg p-4">
        <EnhancedSchemaVisualizer 
          jsonSchema={complexSchema}
          className="border-0"
        />
      </div>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Schema Features Tested:</h2>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          <li>Complex conditional logic with allOf and if/then/else</li>
          <li>Nested definitions with circular references</li>
          <li>Enum values with enumNames</li>
          <li>Array types with item constraints</li>
          <li>Required field validation</li>
          <li>Default values</li>
          <li>Minimum/maximum constraints</li>
          <li>Multi-level property nesting</li>
        </ul>
      </div>
    </div>
  );
};
