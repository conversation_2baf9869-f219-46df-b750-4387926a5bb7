import { SchemaAnalyzer } from '../core/SchemaAnalyzer';
import { GraphGenerator } from '../core/GraphGenerator';

// Test the enhanced schema visualizer with your complex schema
const testComplexSchema = () => {
  const complexSchema = {
    "type": "object",
    "allOf": [
      {
        "if": {
          "properties": {
            "realestate_multiple_land": {
              "const": "yes"
            }
          }
        },
        "then": {
          "$ref": "#/definitions/multiple_lands_yes"
        }
      },
      {
        "if": {
          "properties": {
            "realestate_multiple_land": {
              "const": "no"
            }
          }
        },
        "then": {
          "properties": {
            "land_realestate_facts": {
              "$ref": "#/definitions/land_realestate_facts"
            }
          }
        }
      }
    ],
    "title": "تفاصيل العقار",
    "required": [
      "realestate_multiple_land"
    ],
    "properties": {
      "field_employee_data": {
        "type": "object",
        "title": "حقائق الماسح الميداني",
        "properties": {
          "extra_images": {
            "$ref": "#/definitions/multiple_images_upload",
            "title": "صور إضافيه"
          },
          "descriptive_proof": {
            "type": "string",
            "title": "الإثبات الوصفي"
          }
        }
      },
      "realestate_multiple_land": {
        "$ref": "#/definitions/yes_no",
        "title": "هل العقار يقع على أكثر من أرض؟",
        "default": "no"
      }
    },
    "definitions": {
      "yes_no": {
        "enum": ["yes", "no"],
        "type": "string",
        "default": "no",
        "enumNames": ["نعم", "لا"]
      },
      "multiple_images_upload": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "multiple_lands_yes": {
        "type": "object",
        "allOf": [
          {
            "if": {
              "properties": {
                "form_filled_before": {
                  "const": "yes"
                }
              }
            },
            "then": {
              "required": ["original_form_number"],
              "properties": {
                "original_form_number": {
                  "type": "number",
                  "title": "ماهو رقم النموذج الأساس؟",
                  "minimum": 1
                }
              }
            }
          }
        ],
        "properties": {
          "form_filled_before": {
            "$ref": "#/definitions/yes_no",
            "title": "هل تم تعبئة بيانات العقار في نموذج آخر؟",
            "default": "no"
          }
        }
      },
      "land_realestate_facts": {
        "type": "object",
        "required": ["building_exists", "realestate_images"],
        "properties": {
          "building_exists": {
            "$ref": "#/definitions/yes_no",
            "title": "هل يوجد مبنى؟",
            "default": "no"
          },
          "realestate_images": {
            "$ref": "#/definitions/multiple_images_upload",
            "title": "صورة للعقار",
            "minItems": 1
          }
        }
      }
    }
  };

  console.log('🧪 Testing Enhanced Schema Visualizer');
  console.log('=====================================');

  // Test schema analysis
  const analyzer = new SchemaAnalyzer();
  const analysisResult = analyzer.analyze(complexSchema);

  console.log('📊 Analysis Results:');
  console.log(`- Definitions found: ${analysisResult.context.definitions.size}`);
  console.log(`- Conditionals found: ${analysisResult.context.conditionals.size}`);
  console.log(`- Validation rules: ${analysisResult.context.validationRules.size}`);
  console.log(`- Errors: ${analysisResult.errors.length}`);
  console.log(`- Warnings: ${analysisResult.warnings.length}`);

  if (analysisResult.errors.length > 0) {
    console.log('❌ Errors:', analysisResult.errors);
  }

  // Test graph generation
  const generator = new GraphGenerator();
  const graph = generator.generateGraph(complexSchema, analysisResult.context);

  console.log('\n🎨 Graph Generation Results:');
  console.log(`- Total nodes: ${graph.nodes.length}`);
  console.log(`- Total edges: ${graph.edges.length}`);
  console.log(`- Max depth: ${graph.metadata.maxDepth}`);
  console.log(`- Has conditionals: ${graph.metadata.hasConditionals}`);
  console.log(`- Has definitions: ${graph.metadata.hasDefinitions}`);

  // Log node types
  const nodeTypes = graph.nodes.reduce((acc, node) => {
    const type = node.data.nodeType;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  console.log('\n📋 Node Types:');
  Object.entries(nodeTypes).forEach(([type, count]) => {
    console.log(`- ${type}: ${count}`);
  });

  console.log('\n✅ Test completed successfully!');
  
  return {
    analysisResult,
    graph,
    success: analysisResult.errors.length === 0
  };
};

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  (window as any).testEnhancedSchemaVisualizer = testComplexSchema;
}

export { testComplexSchema };
