import { Node, Edge } from '@xyflow/react';

// Core schema types
export interface JSONSchema {
  type?: string;
  title?: string;
  description?: string;
  properties?: Record<string, JSONSchema>;
  items?: JSONSchema;
  required?: string[];
  enum?: any[];
  enumNames?: string[];
  $ref?: string;
  definitions?: Record<string, JSONSchema>;
  allOf?: JSONSchema[];
  anyOf?: JSONSchema[];
  oneOf?: JSONSchema[];
  if?: JSONSchema;
  then?: JSONSchema;
  else?: JSONSchema;
  dependencies?: Record<string, JSONSchema | string[]>;
  dependentSchemas?: Record<string, JSONSchema>;
  const?: any;
  default?: any;
  format?: string;
  pattern?: string;
  minimum?: number;
  maximum?: number;
  minItems?: number;
  maxItems?: number;
  minLength?: number;
  maxLength?: number;
  [key: string]: any;
}

// Node types for the visualizer
export enum NodeType {
  ROOT = 'root',
  PROPERTY = 'property',
  DEFINITION = 'definition',
  CONDITIONAL = 'conditional',
  VALIDATION = 'validation',
  ENUM = 'enum',
  ARRAY = 'array',
  OBJECT = 'object',
  REFERENCE = 'reference',
  LOGICAL_OPERATOR = 'logical_operator'
}

// Edge types for different relationships
export enum EdgeType {
  PROPERTY = 'property',
  REFERENCE = 'reference',
  CONDITIONAL = 'conditional',
  VALIDATION = 'validation',
  LOGICAL = 'logical'
}

// Validation rule types
export interface ValidationRule {
  type: 'required' | 'format' | 'pattern' | 'range' | 'length' | 'items';
  value?: any;
  message?: string;
}

// Conditional logic representation
export interface ConditionalLogic {
  id: string;
  type: 'if-then-else' | 'allOf' | 'anyOf' | 'oneOf';
  condition?: JSONSchema;
  branches: ConditionalBranch[];
  parentPath: string;
}

export interface ConditionalBranch {
  id: string;
  condition?: JSONSchema;
  schema: JSONSchema;
  isDefault?: boolean;
}

// Schema analysis context
export interface SchemaContext {
  definitions: Map<string, JSONSchema>;
  resolvedRefs: Map<string, JSONSchema>;
  conditionals: Map<string, ConditionalLogic>;
  validationRules: Map<string, ValidationRule[]>;
  schemaPath: string[];
}

// Enhanced node data
export interface BaseNodeData {
  id: string;
  nodeType: NodeType;
  label: string;
  schemaPath: string;
  schema: JSONSchema;
  isRequired?: boolean;
  validationRules?: ValidationRule[];
  description?: string;
}

export interface PropertyNodeData extends BaseNodeData {
  nodeType: NodeType.PROPERTY;
  propertyType: string;
  format?: string;
  isArray?: boolean;
  hasNestedProperties?: boolean;
}

export interface DefinitionNodeData extends BaseNodeData {
  nodeType: NodeType.DEFINITION;
  definitionName: string;
  referencedBy: string[];
}

export interface ConditionalNodeData extends BaseNodeData {
  nodeType: NodeType.CONDITIONAL;
  conditionalLogic: ConditionalLogic;
  activeCondition?: string;
}

export interface EnumNodeData extends BaseNodeData {
  nodeType: NodeType.ENUM;
  enumValues: any[];
  enumNames?: string[];
}

export interface ReferenceNodeData extends BaseNodeData {
  nodeType: NodeType.REFERENCE;
  refPath: string;
  resolvedSchema?: JSONSchema;
}

export interface LogicalOperatorNodeData extends BaseNodeData {
  nodeType: NodeType.LOGICAL_OPERATOR;
  operator: 'allOf' | 'anyOf' | 'oneOf';
  operands: JSONSchema[];
}

export type NodeData = 
  | PropertyNodeData 
  | DefinitionNodeData 
  | ConditionalNodeData 
  | EnumNodeData 
  | ReferenceNodeData 
  | LogicalOperatorNodeData;

// Enhanced edge data
export interface EdgeData {
  edgeType: EdgeType;
  label?: string;
  isConditional?: boolean;
  condition?: JSONSchema;
  validationRule?: ValidationRule;
}

// Layout configuration
export interface LayoutConfig {
  nodeSpacing: {
    horizontal: number;
    vertical: number;
  };
  levelSpacing: number;
  conditionalBranchOffset: number;
  definitionAreaOffset: number;
}

// Graph generation result
export interface SchemaGraph {
  nodes: Node<NodeData>[];
  edges: Edge<EdgeData>[];
  layout: LayoutConfig;
  metadata: {
    totalNodes: number;
    totalEdges: number;
    maxDepth: number;
    hasConditionals: boolean;
    hasDefinitions: boolean;
  };
}

// Analysis result
export interface SchemaAnalysisResult {
  context: SchemaContext;
  graph: SchemaGraph;
  errors: string[];
  warnings: string[];
}
