import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ReactFlow,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  Panel,
  MiniMap
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useTranslation } from 'react-i18next';
import { Maximize2, Minimize2 } from 'lucide-react';

import { SchemaAnalyzer } from './core/SchemaAnalyzer';
import { GraphGenerator } from './core/GraphGenerator';
import { EnhancedSchemaNode } from './components/nodes/EnhancedSchemaNode';
import { JSONSchema, SchemaAnalysisResult } from './types';

interface EnhancedSchemaVisualizerProps {
  jsonSchema: JSONSchema | string;
  className?: string;
}

/**
 * Enhanced schema visualizer with support for complex JSON schemas
 */
export const EnhancedSchemaVisualizer: React.FC<EnhancedSchemaVisualizerProps> = ({
  jsonSchema,
  className = ''
}) => {
  const { t } = useTranslation();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [analysisResult, setAnalysisResult] = useState<SchemaAnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Memoize node types
  const nodeTypes = useMemo(() => ({
    schemaNode: EnhancedSchemaNode
  }), []);

  // Memoize analyzer and generator
  const analyzer = useMemo(() => new SchemaAnalyzer(), []);
  const generator = useMemo(() => new GraphGenerator(), []);

  /**
   * Process the schema and generate the graph
   */
  const processSchema = useCallback(async (schema: JSONSchema | string) => {
    if (!schema) {
      setNodes([]);
      setEdges([]);
      setAnalysisResult(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Parse schema if it's a string
      const parsedSchema: JSONSchema = typeof schema === 'string' 
        ? JSON.parse(schema) 
        : schema;

      // Analyze the schema
      const result = analyzer.analyze(parsedSchema);
      
      if (result.errors.length > 0) {
        setError(`Schema analysis errors: ${result.errors.join(', ')}`);
        return;
      }

      // Generate the graph
      const graph = generator.generateGraph(parsedSchema, result.context);
      
      // Update the analysis result with the generated graph
      const finalResult: SchemaAnalysisResult = {
        ...result,
        graph
      };

      setAnalysisResult(finalResult);
      setNodes(graph.nodes as any);
      setEdges(graph.edges as any);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Failed to process schema: ${errorMessage}`);
      console.error('Schema processing error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [analyzer, generator]);

  // Process schema when it changes
  useEffect(() => {
    processSchema(jsonSchema);
  }, [jsonSchema, processSchema]);

  /**
   * Toggle fullscreen mode
   */
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  /**
   * Handle escape key to exit fullscreen
   */
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  /**
   * Render loading state
   */
  const renderLoading = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
        <div className="text-gray-500">Analyzing schema...</div>
      </div>
    </div>
  );

  /**
   * Render error state
   */
  const renderError = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center max-w-md">
        <div className="text-red-500 text-lg mb-2">⚠️ Error</div>
        <div className="text-gray-600 text-sm">{error}</div>
      </div>
    </div>
  );

  /**
   * Render empty state
   */
  const renderEmpty = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="text-gray-400 text-4xl mb-2">📋</div>
        <div className="text-gray-500">
          {t('workspace.advancedFields.noData') || 'No schema data available'}
        </div>
      </div>
    </div>
  );

  /**
   * Render statistics panel
   */
  const renderStatsPanel = () => {
    if (!analysisResult) return null;

    const { graph, context } = analysisResult;
    const { metadata } = graph;

    return (
      <Panel position="top-left" className="bg-white p-3 rounded-lg shadow-md border">
        <div className="text-sm font-medium mb-2">Schema Statistics</div>
        <div className="space-y-1 text-xs text-gray-600">
          <div>Nodes: {metadata.totalNodes}</div>
          <div>Edges: {metadata.totalEdges}</div>
          <div>Max Depth: {metadata.maxDepth}</div>
          <div>Definitions: {context.definitions.size}</div>
          <div>Conditionals: {context.conditionals.size}</div>
        </div>
      </Panel>
    );
  };

  /**
   * Render legend panel
   */
  const renderLegend = () => (
    <Panel position="top-right" className="bg-white p-3 rounded-lg shadow-md border">
      <div className="text-sm font-medium mb-2">Legend</div>
      <div className="space-y-2 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-100 border-2 border-green-500 rounded"></div>
          <span>Root Schema</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-100 border border-blue-500 rounded"></div>
          <span>Property</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-100 border border-purple-500 rounded dashed"></div>
          <span>Definition</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-orange-100 border border-orange-500 rounded"></div>
          <span>Reference</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-100 border border-red-500 rounded"></div>
          <span>Enum</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-100 border border-green-500 rounded-full"></div>
          <span>Conditional</span>
        </div>
      </div>
    </Panel>
  );

  /**
   * Render fullscreen toggle button
   */
  const renderFullscreenButton = () => (
    <Panel position="bottom-right" className="bg-white rounded-lg shadow-md border">
      <button
        onClick={toggleFullscreen}
        className="p-2 hover:bg-gray-100 transition-colors rounded-lg"
        title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
      >
        {isFullscreen ? (
          <Minimize2 className="w-4 h-4 text-gray-600" />
        ) : (
          <Maximize2 className="w-4 h-4 text-gray-600" />
        )}
      </button>
    </Panel>
  );

  // Main render
  const containerClasses = isFullscreen
    ? "fixed inset-0 z-50 bg-white"
    : `h-[75vh] w-full border rounded-lg ${className}`;

  return (
    <div className={containerClasses}>
      {isLoading ? (
        renderLoading()
      ) : error ? (
        renderError()
      ) : nodes.length === 0 ? (
        renderEmpty()
      ) : (
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-right"
          defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
          minZoom={0.1}
          maxZoom={2}
        >
          <Controls />
          <Background color="#f0f0f0" gap={16} size={1} />
          <MiniMap
            nodeColor="#e2e8f0"
            maskColor="rgba(0, 0, 0, 0.1)"
            position="bottom-left"
          />
          {renderStatsPanel()}
          {renderLegend()}
          {renderFullscreenButton()}
        </ReactFlow>
      )}
    </div>
  );
};
