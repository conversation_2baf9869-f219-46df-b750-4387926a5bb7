import React from 'react';
import { BaseSchemaNode } from './BaseSchemaNode';
import { ReferenceNodeData } from '../../types';

interface ReferenceNodeProps {
  data: ReferenceNodeData;
}

/**
 * Reference node component for $ref properties
 */
export const ReferenceNode: React.FC<ReferenceNodeProps> = ({ data }) => {
  const renderReferenceDetails = () => {
    const { refPath, resolvedSchema } = data;
    
    return (
      <div className="mt-2 space-y-1">
        <div className="text-xs text-blue-600 font-mono bg-blue-50 rounded px-2 py-1">
          {refPath}
        </div>
        
        {resolvedSchema && (
          <>
            {resolvedSchema.type && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Resolves to:</span> {resolvedSchema.type}
              </div>
            )}
            
            {resolvedSchema.enum && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Enum values:</span> {resolvedSchema.enum.length} options
              </div>
            )}
            
            {resolvedSchema.properties && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Properties:</span> {Object.keys(resolvedSchema.properties).length} fields
              </div>
            )}
            
            {resolvedSchema.required && resolvedSchema.required.length > 0 && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Required:</span> {resolvedSchema.required.length} fields
              </div>
            )}
          </>
        )}
        
        {!resolvedSchema && (
          <div className="text-xs text-red-600">
            Reference not resolved
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseSchemaNode data={data}>
      {renderReferenceDetails()}
    </BaseSchemaNode>
  );
};
