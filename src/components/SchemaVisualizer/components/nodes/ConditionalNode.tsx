import React from 'react';
import { BaseSchemaNode } from './BaseSchemaNode';
import { ConditionalNodeData } from '../../types';

interface ConditionalNodeProps {
  data: ConditionalNodeData;
}

/**
 * Conditional node component for conditional logic (if/then/else, allOf, anyOf, oneOf)
 */
export const ConditionalNode: React.FC<ConditionalNodeProps> = ({ data }) => {
  const renderConditionalDetails = () => {
    const { conditionalLogic } = data;
    
    const getOperatorColor = (type: string) => {
      switch (type) {
        case 'if-then-else':
          return 'text-green-600 bg-green-50';
        case 'allOf':
          return 'text-blue-600 bg-blue-50';
        case 'anyOf':
          return 'text-orange-600 bg-orange-50';
        case 'oneOf':
          return 'text-purple-600 bg-purple-50';
        default:
          return 'text-gray-600 bg-gray-50';
      }
    };

    const getOperatorSymbol = (type: string) => {
      switch (type) {
        case 'if-then-else':
          return '?:';
        case 'allOf':
          return '∧';
        case 'anyOf':
          return '∨';
        case 'oneOf':
          return '⊕';
        default:
          return '?';
      }
    };

    return (
      <div className="mt-2 space-y-2">
        <div className={`text-xs font-mono rounded px-2 py-1 ${getOperatorColor(conditionalLogic.type)}`}>
          <span className="font-bold mr-1">{getOperatorSymbol(conditionalLogic.type)}</span>
          {conditionalLogic.type}
        </div>
        
        <div className="text-xs text-gray-600">
          <span className="font-medium">Branches:</span> {conditionalLogic.branches.length}
        </div>
        
        {conditionalLogic.condition && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Has condition</span>
          </div>
        )}
        
        <div className="space-y-1">
          {conditionalLogic.branches.map((branch, index) => (
            <div key={branch.id} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium">
                Branch {index + 1}
                {branch.isDefault && ' (default)'}
              </span>
              {branch.condition && (
                <div className="text-gray-500 mt-1">
                  Has condition
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <BaseSchemaNode data={data}>
      {renderConditionalDetails()}
    </BaseSchemaNode>
  );
};
