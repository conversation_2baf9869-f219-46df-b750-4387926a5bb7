import React from 'react';
import { Handle, Position } from '@xyflow/react';
import { NodeType, BaseNodeData, ValidationRule } from '../../types';

interface BaseSchemaNodeProps {
  data: BaseNodeData;
  children?: React.ReactNode;
  className?: string;
  showHandles?: boolean;
}

const getNodeTypeLabel = (nodeType: NodeType): string => {
  switch (nodeType) {
    case NodeType.ROOT:
      return 'Root Schema';
    case NodeType.PROPERTY:
      return 'Property';
    case NodeType.DEFINITION:
      return 'Definition';
    case NodeType.REFERENCE:
      return 'Reference';
    case NodeType.ENUM:
      return 'Enumeration';
    case NodeType.CONDITIONAL:
      return 'Conditional Logic';
    case NodeType.LOGICAL_OPERATOR:
      return 'Logical Operator';
    default:
      return 'Schema Element';
  }
};

/**
 * Base component for all schema nodes with common styling and functionality
 */
export const BaseSchemaNode: React.FC<BaseSchemaNodeProps> = ({
  data,
  children,
  className = '',
  showHandles = true
}) => {
  const getNodeStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      minWidth: '200px',
      maxWidth: '320px',
      borderRadius: '12px',
      border: '2px solid',
      padding: '14px 18px',
      backgroundColor: '#ffffff',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
      fontSize: '14px',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer'
    };

    switch (data.nodeType) {
      case NodeType.ROOT:
        return {
          ...baseStyle,
          backgroundColor: '#f0f9ff',
          borderColor: '#0ea5e9',
          borderWidth: '3px',
          boxShadow: '0 6px 16px rgba(14, 165, 233, 0.15)'
        };

      case NodeType.PROPERTY: {
        const isResolved = (data as any).resolvedFrom;
        return {
          ...baseStyle,
          backgroundColor: data.isRequired
            ? '#fef2f2'
            : isResolved
              ? '#f0f9ff'
              : '#f8fafc',
          borderColor: data.isRequired
            ? '#ef4444'
            : isResolved
              ? '#3b82f6'
              : '#64748b',
          borderWidth: '2px',
          boxShadow: data.isRequired
            ? '0 4px 12px rgba(239, 68, 68, 0.15)'
            : isResolved
              ? '0 4px 12px rgba(59, 130, 246, 0.15)'
              : '0 4px 12px rgba(0, 0, 0, 0.08)'
        };
      }
      
      case NodeType.DEFINITION:
        return {
          ...baseStyle,
          backgroundColor: '#faf5ff',
          borderColor: '#8b5cf6',
          borderStyle: 'dashed',
          boxShadow: '0 4px 12px rgba(139, 92, 246, 0.15)'
        };

      case NodeType.REFERENCE:
        return {
          ...baseStyle,
          backgroundColor: '#fffbeb',
          borderColor: '#f59e0b',
          boxShadow: '0 4px 12px rgba(245, 158, 11, 0.15)'
        };

      case NodeType.ENUM:
        return {
          ...baseStyle,
          backgroundColor: '#fef3f2',
          borderColor: '#f97316',
          boxShadow: '0 4px 12px rgba(249, 115, 22, 0.15)'
        };
      
      case NodeType.CONDITIONAL:
        return {
          ...baseStyle,
          backgroundColor: '#f0fdf4',
          borderColor: '#22c55e',
          borderRadius: '16px',
          boxShadow: '0 4px 12px rgba(34, 197, 94, 0.15)'
        };

      case NodeType.LOGICAL_OPERATOR:
        return {
          ...baseStyle,
          backgroundColor: '#f0f9ff',
          borderColor: '#3b82f6',
          borderRadius: '50%',
          minWidth: '90px',
          minHeight: '90px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'
        };
      
      default:
        return baseStyle;
    }
  };

  const getNodeIcon = (): JSX.Element => {
    const iconProps = {
      width: "16",
      height: "16",
      className: "inline-block mr-2",
      fill: "currentColor"
    };

    switch (data.nodeType) {
      case NodeType.ROOT:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
        );
      case NodeType.PROPERTY:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
        );
      case NodeType.DEFINITION:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M5,19V5H19V19H5M17,17H7V15H17V17M17,13H7V11H17V13M17,9H7V7H17V9Z"/>
          </svg>
        );
      case NodeType.REFERENCE:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z"/>
          </svg>
        );
      case NodeType.ENUM:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M7,5H21V7H7V5M7,13V11H21V13H7M4,4.5A1.5,1.5 0 0,1 5.5,6A1.5,1.5 0 0,1 4,7.5A1.5,1.5 0 0,1 2.5,6A1.5,1.5 0 0,1 4,4.5M4,10.5A1.5,1.5 0 0,1 5.5,12A1.5,1.5 0 0,1 4,13.5A1.5,1.5 0 0,1 2.5,12A1.5,1.5 0 0,1 4,10.5M7,19V17H21V19H7M4,16.5A1.5,1.5 0 0,1 5.5,18A1.5,1.5 0 0,1 4,19.5A1.5,1.5 0 0,1 2.5,18A1.5,1.5 0 0,1 4,16.5Z"/>
          </svg>
        );
      case NodeType.CONDITIONAL:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M7,10L12,15L17,10H7Z"/>
            <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
          </svg>
        );
      case NodeType.LOGICAL_OPERATOR:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H9V9H11V17M15,17H13V9H15V17Z"/>
          </svg>
        );
      default:
        return (
          <svg {...iconProps} viewBox="0 0 24 24">
            <path d="M13,9V3.5L18.5,9M6,2C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6Z"/>
          </svg>
        );
    }
  };

  const renderValidationBadges = (rules: ValidationRule[]) => {
    if (!rules || rules.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {rules.map((rule, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
            title={rule.message}
          >
            {rule.type}
          </span>
        ))}
      </div>
    );
  };

  const getTooltipText = (): string => {
    const parts = [
      `${getNodeTypeLabel(data.nodeType)}: ${data.label}`,
      data.description && `Description: ${data.description}`,
      data.schema.type && `Type: ${data.schema.type}`,
      data.isRequired && 'Required field',
      (data as any).resolvedFrom && `Resolved from: ${(data as any).resolvedFrom.definitionName}`
    ].filter(Boolean);

    return parts.join('\n');
  };

  return (
    <div
      className={`schema-node ${className}`}
      style={getNodeStyle()}
      title={getTooltipText()}
    >
      {showHandles && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            style={{
              background: '#555',
              width: '8px',
              height: '8px'
            }}
          />
          <Handle
            type="source"
            position={Position.Right}
            style={{
              background: '#555',
              width: '8px',
              height: '8px'
            }}
          />
        </>
      )}

      <div className="flex items-start gap-2">
        <div className="flex-shrink-0 mt-0.5 text-gray-600">
          {getNodeIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-gray-900 truncate">
              {data.label}
            </h3>
            {data.isRequired && (
              <span className="text-red-500 font-bold" title="Required field">
                *
              </span>
            )}
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {getNodeTypeLabel(data.nodeType)}
            </span>
          </div>

          {data.schema.type && (
            <div className="text-xs text-gray-600 mb-1">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                {data.schema.type}
              </span>
            </div>
          )}

          {data.schema.format && (
            <div className="text-xs text-gray-600">
              <span className="font-medium">Format:</span> {data.schema.format}
            </div>
          )}

          {data.description && (
            <div className="text-xs text-gray-500 mt-2 line-clamp-2">
              {data.description}
            </div>
          )}

          {data.validationRules && renderValidationBadges(data.validationRules)}

          {children}
        </div>
      </div>
    </div>
  );
};
