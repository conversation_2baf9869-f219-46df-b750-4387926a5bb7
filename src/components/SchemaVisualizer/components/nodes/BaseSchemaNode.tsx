import React from 'react';
import { Handle, Position } from '@xyflow/react';
import { NodeType, BaseNodeData, ValidationRule } from '../../types';

interface BaseSchemaNodeProps {
  data: BaseNodeData;
  children?: React.ReactNode;
  className?: string;
  showHandles?: boolean;
}

/**
 * Base component for all schema nodes with common styling and functionality
 */
export const BaseSchemaNode: React.FC<BaseSchemaNodeProps> = ({
  data,
  children,
  className = '',
  showHandles = true
}) => {
  const getNodeStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      minWidth: '150px',
      maxWidth: '300px',
      borderRadius: '8px',
      border: '2px solid',
      padding: '12px',
      backgroundColor: '#ffffff',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      fontSize: '14px',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    };

    switch (data.nodeType) {
      case NodeType.ROOT:
        return {
          ...baseStyle,
          backgroundColor: '#f6ffed',
          borderColor: '#52c41a',
          borderWidth: '3px'
        };
      
      case NodeType.PROPERTY:
        return {
          ...baseStyle,
          backgroundColor: data.isRequired ? '#fff2f5' : '#e6f7ff',
          borderColor: data.isRequired ? '#ff4d6d' : '#1890ff',
          borderWidth: data.isRequired ? '2px' : '1px'
        };
      
      case NodeType.DEFINITION:
        return {
          ...baseStyle,
          backgroundColor: '#f9f0ff',
          borderColor: '#722ed1',
          borderStyle: 'dashed'
        };
      
      case NodeType.REFERENCE:
        return {
          ...baseStyle,
          backgroundColor: '#fff7e6',
          borderColor: '#fa8c16'
        };
      
      case NodeType.ENUM:
        return {
          ...baseStyle,
          backgroundColor: '#fff2e8',
          borderColor: '#fa541c'
        };
      
      case NodeType.CONDITIONAL:
        return {
          ...baseStyle,
          backgroundColor: '#f6ffed',
          borderColor: '#52c41a',
          borderRadius: '16px'
        };
      
      case NodeType.LOGICAL_OPERATOR:
        return {
          ...baseStyle,
          backgroundColor: '#e6f7ff',
          borderColor: '#1890ff',
          borderRadius: '50%',
          minWidth: '80px',
          minHeight: '80px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        };
      
      default:
        return baseStyle;
    }
  };

  const getNodeIcon = (): string => {
    switch (data.nodeType) {
      case NodeType.ROOT:
        return '🏠';
      case NodeType.PROPERTY:
        return '📝';
      case NodeType.DEFINITION:
        return '📚';
      case NodeType.REFERENCE:
        return '🔗';
      case NodeType.ENUM:
        return '📋';
      case NodeType.CONDITIONAL:
        return '🔀';
      case NodeType.LOGICAL_OPERATOR:
        return '⚡';
      default:
        return '📄';
    }
  };

  const renderValidationBadges = (rules: ValidationRule[]) => {
    if (!rules || rules.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {rules.map((rule, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
            title={rule.message}
          >
            {rule.type}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div
      className={`schema-node ${className}`}
      style={getNodeStyle()}
    >
      {showHandles && (
        <>
          <Handle
            type="target"
            position={Position.Left}
            style={{
              background: '#555',
              width: '8px',
              height: '8px'
            }}
          />
          <Handle
            type="source"
            position={Position.Right}
            style={{
              background: '#555',
              width: '8px',
              height: '8px'
            }}
          />
        </>
      )}

      <div className="flex items-start gap-2">
        <span className="text-lg" role="img" aria-label={data.nodeType}>
          {getNodeIcon()}
        </span>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-1">
            <h3 className="font-semibold text-gray-900 truncate">
              {data.label}
            </h3>
            {data.isRequired && (
              <span className="text-red-500 font-bold" title="Required field">
                *
              </span>
            )}
          </div>

          {data.schema.type && (
            <div className="text-xs text-gray-600 mt-1">
              <span className="font-medium">Type:</span> {data.schema.type}
            </div>
          )}

          {data.schema.format && (
            <div className="text-xs text-gray-600">
              <span className="font-medium">Format:</span> {data.schema.format}
            </div>
          )}

          {data.description && (
            <div className="text-xs text-gray-500 mt-2 line-clamp-2">
              {data.description}
            </div>
          )}

          {data.validationRules && renderValidationBadges(data.validationRules)}

          {children}
        </div>
      </div>
    </div>
  );
};
