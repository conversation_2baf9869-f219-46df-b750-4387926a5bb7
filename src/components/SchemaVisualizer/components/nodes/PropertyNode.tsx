import React from 'react';
import { BaseSchemaNode } from './BaseSchemaNode';
import { PropertyNodeData } from '../../types';

interface PropertyNodeProps {
  data: PropertyNodeData;
}

/**
 * Property node component for schema properties
 */
export const PropertyNode: React.FC<PropertyNodeProps> = ({ data }) => {
  const renderPropertyDetails = () => {
    return (
      <div className="mt-2 space-y-1">
        {/* Show resolved definition info */}
        {data.resolvedFrom && (
          <div className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded border border-blue-200">
            <span className="font-medium">Definition:</span> {data.resolvedFrom.definitionName}
          </div>
        )}

        {data.isArray && (
          <div className="text-xs text-purple-600 font-medium">
            Array
          </div>
        )}

        {data.schema.enum && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Options:</span> {data.schema.enum.length} values
          </div>
        )}

        {data.schema.default !== undefined && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Default:</span> {String(data.schema.default)}
          </div>
        )}

        {data.hasNestedProperties && (
          <div className="text-xs text-blue-600 font-medium">
            Has nested properties
          </div>
        )}

        {data.schema.pattern && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Pattern:</span> {data.schema.pattern}
          </div>
        )}

        {(data.schema.minimum !== undefined || data.schema.maximum !== undefined) && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Range:</span>
            {data.schema.minimum !== undefined && ` min: ${data.schema.minimum}`}
            {data.schema.maximum !== undefined && ` max: ${data.schema.maximum}`}
          </div>
        )}

        {(data.schema.minLength !== undefined || data.schema.maxLength !== undefined) && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Length:</span>
            {data.schema.minLength !== undefined && ` min: ${data.schema.minLength}`}
            {data.schema.maxLength !== undefined && ` max: ${data.schema.maxLength}`}
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseSchemaNode data={data}>
      {renderPropertyDetails()}
    </BaseSchemaNode>
  );
};
