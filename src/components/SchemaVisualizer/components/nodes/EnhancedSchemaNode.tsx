import React from 'react';
import { NodeType, NodeData } from '../../types';
import { PropertyNode } from './PropertyNode';
import { EnumNode } from './EnumNode';
import { ReferenceNode } from './ReferenceNode';
import { DefinitionNode } from './DefinitionNode';
import { ConditionalNode } from './ConditionalNode';
import { BaseSchemaNode } from './BaseSchemaNode';

interface EnhancedSchemaNodeProps {
  data: NodeData;
}

/**
 * Main schema node component that routes to appropriate node type
 */
export const EnhancedSchemaNode: React.FC<EnhancedSchemaNodeProps> = ({ data }) => {
  switch (data.nodeType) {
    case NodeType.ROOT:
    case NodeType.PROPERTY:
      return <PropertyNode data={data as any} />;
    
    case NodeType.ENUM:
      return <EnumNode data={data as any} />;
    
    case NodeType.REFERENCE:
      return <ReferenceNode data={data as any} />;
    
    case NodeType.DEFINITION:
      return <DefinitionNode data={data as any} />;
    
    case NodeType.CONDITIONAL:
      return <ConditionalNode data={data as any} />;
    
    case NodeType.LOGICAL_OPERATOR:
      return (
        <BaseSchemaNode data={data}>
          <div className="text-center font-bold text-lg">
            {(data as any).operator?.toUpperCase()}
          </div>
        </BaseSchemaNode>
      );
    
    default:
      return <BaseSchemaNode data={data} />;
  }
};
