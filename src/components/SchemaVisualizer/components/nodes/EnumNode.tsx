import React, { useState } from 'react';
import { BaseSchemaNode } from './BaseSchemaNode';
import { EnumNodeData } from '../../types';

interface EnumNodeProps {
  data: EnumNodeData;
}

/**
 * Enum node component for displaying enum values
 */
export const EnumNode: React.FC<EnumNodeProps> = ({ data }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const maxDisplayValues = 3;

  const renderEnumValues = () => {
    const { enumValues, enumNames } = data;
    const displayValues = isExpanded ? enumValues : enumValues.slice(0, maxDisplayValues);
    const hasMore = enumValues.length > maxDisplayValues;

    return (
      <div className="mt-2">
        <div className="text-xs text-gray-600 mb-1">
          <span className="font-medium">Values ({enumValues.length}):</span>
        </div>
        
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {displayValues.map((value, index) => {
            const displayName = enumNames && enumNames[index] ? enumNames[index] : value;
            const actualIndex = isExpanded ? index : index;
            
            return (
              <div
                key={actualIndex}
                className="flex items-center justify-between text-xs bg-gray-50 rounded px-2 py-1"
              >
                <span className="font-mono text-blue-600 truncate">
                  {String(value)}
                </span>
                {enumNames && enumNames[actualIndex] && (
                  <span className="text-gray-500 ml-2 truncate">
                    {enumNames[actualIndex]}
                  </span>
                )}
              </div>
            );
          })}
        </div>

        {hasMore && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            {isExpanded ? 'Show less' : `Show ${enumValues.length - maxDisplayValues} more...`}
          </button>
        )}
      </div>
    );
  };

  return (
    <BaseSchemaNode data={data}>
      {renderEnumValues()}
    </BaseSchemaNode>
  );
};
