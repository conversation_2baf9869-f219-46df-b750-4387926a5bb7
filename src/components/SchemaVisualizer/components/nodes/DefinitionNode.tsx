import React from 'react';
import { BaseSchemaNode } from './BaseSchemaNode';
import { DefinitionNodeData } from '../../types';

interface DefinitionNodeProps {
  data: DefinitionNodeData;
}

/**
 * Definition node component for schema definitions
 */
export const DefinitionNode: React.FC<DefinitionNodeProps> = ({ data }) => {
  const renderDefinitionDetails = () => {
    const { definitionName, referencedBy, schema } = data;
    
    return (
      <div className="mt-2 space-y-1">
        <div className="text-xs text-purple-600 font-mono bg-purple-50 rounded px-2 py-1">
          #{definitionName}
        </div>
        
        {schema.type && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Type:</span> {schema.type}
          </div>
        )}
        
        {schema.enum && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Enum:</span> {schema.enum.length} values
          </div>
        )}
        
        {schema.properties && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Properties:</span> {Object.keys(schema.properties).length}
          </div>
        )}
        
        {schema.required && schema.required.length > 0 && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Required:</span> {schema.required.length} fields
          </div>
        )}
        
        {referencedBy.length > 0 && (
          <div className="text-xs text-gray-600">
            <span className="font-medium">Used by:</span> {referencedBy.length} references
          </div>
        )}
        
        {(schema.allOf || schema.anyOf || schema.oneOf) && (
          <div className="text-xs text-orange-600 font-medium">
            Contains conditional logic
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseSchemaNode data={data}>
      {renderDefinitionDetails()}
    </BaseSchemaNode>
  );
};
