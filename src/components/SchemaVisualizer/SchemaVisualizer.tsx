import { useCallback, useEffect, useMemo } from 'react';
import {
    <PERSON>act<PERSON>low,
    Node,
    Edge,
    Controls,
    Background,
    useNodesState,
    useEdgesState,
    MarkerType,
    // Position
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useTranslation } from 'react-i18next';
import { SchemaNode } from './SchemaNode';

interface SchemaVisualizerProps {
    jsonSchema: any;
}

export const SchemaVisualizer = ({ jsonSchema }: SchemaVisualizerProps) => {
    const { t } = useTranslation();
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);

    const nodeTypes = useMemo(() => ({ schemaNode: SchemaNode }), []);

    const convertSchemaToGraph = useCallback((schema: any) => {
        if (!schema) return { nodes: [], edges: [] };

        const nodes: Node[] = [];
        const edges: Edge[] = [];
        let nodeId = 0;

        // Helper function to create a node
        const createNode = (
            label: string,
            x: number,
            y: number,
            data: any
        ) => {
            const id = `node-${nodeId++}`;
            nodes.push({
                id,
                type: 'schemaNode',
                position: { x, y },
                data: {
                    label,
                    ...data
                },
            });
            return id;
        };

        // Helper function to create an edge
        const createEdge = (source: string, target: string, animated: boolean = false) => {
            const id = `edge-${source}-${target}`;
            edges.push({
                id,
                source,
                target,
                type: 'smoothstep',
                animated,
                markerEnd: {
                    type: MarkerType.ArrowClosed,
                    width: 15,
                    height: 15,
                },
            });
            return id;
        };

        // Create root node
        const rootId = createNode(
            schema.title || 'Root Schema',
            0,
            0,
            {
                isRoot: true,
                type: schema.type || 'object',
                description: schema.description,
            }
        );

        // Extract required fields
        const requiredFields = schema.required || [];

        // Store definitions for reference but don't create nodes for them
        const definitionsMap = new Map();

        if (schema.definitions) {
            Object.entries(schema.definitions).forEach(([defName, defSchema]: [string, any]) => {
                definitionsMap.set(`#/definitions/${defName}`, defSchema);
            });
        }

        // Process properties
        let yOffset = 100;
        const xOffsetProperties = 300;

        if (schema.properties) {
            const propertyKeys = Object.keys(schema.properties);

            propertyKeys.forEach((key) => {
                const property = schema.properties[key];
                const isRequired = requiredFields.includes(key);

                // Check if property has a $ref
                if (property.$ref) {
                    const refPath = property.$ref;
                    const definitionSchema = definitionsMap.get(refPath);

                    // Create property node with reference
                    const propertyId = createNode(
                        key,
                        xOffsetProperties,
                        yOffset,
                        {
                            isProperty: true,
                            isRequired,
                            $ref: refPath,
                            type: definitionSchema?.type || 'reference',
                            description: definitionSchema?.description || property.description,
                        }
                    );

                    // Connect root to property
                    createEdge(rootId, propertyId);

                    // If definition has enum, create enum node directly connected to this property
                    if (definitionSchema?.enum) {
                        const enumId = createNode(
                            `enum values`,
                            xOffsetProperties + 300,
                            yOffset,
                            {
                                isEnum: true,
                                enum: definitionSchema.enum,
                            }
                        );

                        createEdge(propertyId, enumId);
                    }

                    yOffset += 100;
                } else {
                    // Regular property without reference
                    const propertyId = createNode(
                        key,
                        xOffsetProperties,
                        yOffset,
                        {
                            isProperty: true,
                            isRequired,
                            type: property.type || 'any',
                            format: property.format,
                            enum: property.enum,
                            description: property.description,
                        }
                    );

                    // Connect root to property
                    createEdge(rootId, propertyId);

                    // If property has enum, create enum node
                    if (property.enum) {
                        const enumId = createNode(
                            `enum values`,
                            xOffsetProperties + 300,
                            yOffset,
                            {
                                isEnum: true,
                                enum: property.enum,
                            }
                        );

                        createEdge(propertyId, enumId);
                        yOffset += 70;
                    }

                    // Handle nested objects
                    if (property.type === 'object' && property.properties) {
                        const nestedProps = Object.keys(property.properties);
                        const nestedRequired = property.required || [];

                        nestedProps.forEach((nestedKey, index) => {
                            const nestedProp = property.properties[nestedKey];
                            const isNestedRequired = nestedRequired.includes(nestedKey);

                            const nestedId = createNode(
                                nestedKey,
                                xOffsetProperties + 300,
                                yOffset + (index * 70),
                                {
                                    isProperty: true,
                                    isRequired: isNestedRequired,
                                    type: nestedProp.type || 'any',
                                    format: nestedProp.format,
                                    enum: nestedProp.enum,
                                    description: nestedProp.description,
                                }
                            );

                            // Connect property to nested property
                            createEdge(propertyId, nestedId);

                            // Also connect root to nested property with dashed line
                            const dashEdgeId = `dash-edge-${rootId}-${nestedId}`;
                            edges.push({
                                id: dashEdgeId,
                                source: rootId,
                                target: nestedId,
                                type: 'smoothstep',
                                animated: false,
                                style: { strokeDasharray: '5,5' },
                                markerEnd: {
                                    type: MarkerType.ArrowClosed,
                                    width: 15,
                                    height: 15,
                                },
                            });

                            // If nested property has a $ref, handle it
                            if (nestedProp.$ref) {
                                const refSchema = definitionsMap.get(nestedProp.$ref);
                                if (refSchema?.enum) {
                                    const enumId = createNode(
                                        `enum values`,
                                        xOffsetProperties + 600,
                                        yOffset + (index * 70),
                                        {
                                            isEnum: true,
                                            enum: refSchema.enum,
                                        }
                                    );

                                    createEdge(nestedId, enumId);
                                }
                            }
                        });

                        // Adjust yOffset based on number of nested properties
                        yOffset += Math.max(70 * nestedProps.length, 100);
                    } else if (property.type === 'array' && property.items) {
                        // Handle array items
                        const itemsId = createNode(
                            `${key} items`,
                            xOffsetProperties + 300,
                            yOffset,
                            {
                                isArray: true,
                                type: property.items.type || 'any',
                                format: property.items.format,
                                enum: property.items.enum,
                                description: property.items.description,
                            }
                        );

                        // Connect property to items
                        createEdge(propertyId, itemsId);

                        // Check if items has a $ref
                        if (property.items.$ref) {
                            const refSchema = definitionsMap.get(property.items.$ref);

                            if (refSchema?.enum) {
                                const enumId = createNode(
                                    `enum values`,
                                    xOffsetProperties + 600,
                                    yOffset,
                                    {
                                        isEnum: true,
                                        enum: refSchema.enum,
                                    }
                                );

                                createEdge(itemsId, enumId);
                            }
                        }

                        // Also connect root to items with dashed line
                        const dashEdgeId = `dash-edge-${rootId}-${itemsId}`;
                        edges.push({
                            id: dashEdgeId,
                            source: rootId,
                            target: itemsId,
                            type: 'smoothstep',
                            animated: false,
                            style: { strokeDasharray: '5,5' },
                            markerEnd: {
                                type: MarkerType.ArrowClosed,
                                width: 15,
                                height: 15,
                            },
                        });

                        // If items is an object with properties, process those too
                        if (property.items.type === 'object' && property.items.properties) {
                            const itemProps = Object.keys(property.items.properties);
                            const itemRequired = property.items.required || [];

                            itemProps.forEach((itemKey, index) => {
                                const itemProp = property.items.properties[itemKey];
                                const isItemRequired = itemRequired.includes(itemKey);

                                const itemPropId = createNode(
                                    itemKey,
                                    xOffsetProperties + 600,
                                    yOffset + (index * 70),
                                    {
                                        isProperty: true,
                                        isRequired: isItemRequired,
                                        type: itemProp.type || 'any',
                                        format: itemProp.format,
                                        enum: itemProp.enum,
                                        description: itemProp.description,
                                    }
                                );

                                // Connect items to item property
                                createEdge(itemsId, itemPropId);

                                // Also connect root to item property with dashed line
                                const dashEdgeId = `dash-edge-${rootId}-${itemPropId}`;
                                edges.push({
                                    id: dashEdgeId,
                                    source: rootId,
                                    target: itemPropId,
                                    type: 'smoothstep',
                                    animated: false,
                                    style: { strokeDasharray: '5,5' },
                                    markerEnd: {
                                        type: MarkerType.ArrowClosed,
                                        width: 15,
                                        height: 15,
                                    },
                                });

                                // If item property has a $ref, handle it
                                if (itemProp.$ref) {
                                    const refSchema = definitionsMap.get(itemProp.$ref);
                                    if (refSchema?.enum) {
                                        const enumId = createNode(
                                            `enum values`,
                                            xOffsetProperties + 900,
                                            yOffset + (index * 70),
                                            {
                                                isEnum: true,
                                                enum: refSchema.enum,
                                            }
                                        );

                                        createEdge(itemPropId, enumId);
                                    }
                                }
                            });

                            // Adjust yOffset based on number of item properties
                            yOffset += Math.max(70 * itemProps.length, 100);
                        } else {
                            yOffset += 100;
                        }
                    } else {
                        yOffset += 100;
                    }
                }
            });
        }

        return { nodes, edges };
    }, []);

    useEffect(() => {
        if (jsonSchema) {
            try {
                const parsedSchema = typeof jsonSchema === 'string'
                    ? JSON.parse(jsonSchema)
                    : jsonSchema;

                const { nodes: schemaNodes, edges: schemaEdges } = convertSchemaToGraph(parsedSchema);
                setNodes(schemaNodes as any);
                setEdges(schemaEdges as any);
            } catch (error) {
                console.error('Error parsing JSON schema:', error);
            }
        }
    }, [jsonSchema, convertSchemaToGraph, setNodes, setEdges]);

    return (
        <div className="h-[75vh] w-full border rounded-lg">
            {nodes.length > 0 ? (
                <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    nodeTypes={nodeTypes}
                    fitView
                    attributionPosition="bottom-right"
                >
                    <Controls />
                    <Background color="#f0f0f0" gap={12} size={1} />
                    {/* <Panel position="top-left" className="bg-white p-2 rounded shadow-md">
                        <div className="text-sm font-medium">{t('schemaLegend')}:</div>
                        <div className="flex flex-wrap gap-2 mt-1">
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded mr-1"></div>
                                <span className="text-xs">Root</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded mr-1"></div>
                                <span className="text-xs">Property</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-purple-100 border border-purple-300 rounded mr-1"></div>
                                <span className="text-xs">Array</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded mr-1"></div>
                                <span className="text-xs">Enum</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded mr-1"></div>
                                <span className="text-xs">Required</span>
                            </div>
                            <div className="flex items-center">
                                <div className="w-8 h-0.5 border-t border-dashed border-gray-400 mr-1"></div>
                                <span className="text-xs">Root connection</span>
                            </div>
                        </div>
                    </Panel> */}
                </ReactFlow>
            ) : (
                <div className="flex items-center justify-center h-full">
                    <div className="text-gray-500">
                        {t('workspace.advancedFields.noData')}
                    </div>
                </div>
            )}
        </div>
    );
};
