import { Position } from '@xyflow/react';
import { Handle } from '@xyflow/react';


// Custom node component for schema properties
export const SchemaNode = ({ data }: { data: any }) => {
    // Determine styling based on node type
    let nodeStyle: React.CSSProperties = {};

    if (data.isRoot) {
        nodeStyle = { backgroundColor: '#f6ffed', borderColor: '#b7eb8f', borderWidth: '2px' };
    } else if (data.isProperty) {
        nodeStyle = {
            backgroundColor: data.isRequired ? '#fff2f5' : '#e6f7ff',
            borderColor: data.isRequired ? '#ff4d6d' : '#91d5ff',
            borderWidth: data.isRequired ? '2px' : '1px'
        };
    } else if (data.isType) {
        nodeStyle = { backgroundColor: '#f0f0f0', borderColor: '#ddd' };
    } else if (data.isArray) {
        nodeStyle = { backgroundColor: '#f9f0ff', borderColor: '#d3adf7' };
    } else if (data.isObject) {
        nodeStyle = { backgroundColor: '#e6f7ff', borderColor: '#91d5ff' };
    } else if (data.isEnum) {
        nodeStyle = { backgroundColor: '#fff2e8', borderColor: '#ffbb96' };
    }

    return (
        <div
            className="px-4 py-2 border rounded-md shadow-sm"
            style={nodeStyle}
        >
            <Handle type="target" position={Position.Left} />
            <div className="font-medium">
                {data.label}
                {data.isRequired && (
                    <span className="text-red-500 ml-1">*</span>
                )}
            </div>
            {data.type && (
                <div className="text-xs text-gray-600">
                    {data.type}
                </div>
            )}
            {data.format && (
                <div className="text-xs text-gray-500">
                    format: {data.format}
                </div>
            )}
            {data.enum && (
                <div className="text-xs text-gray-500">
                    enum: [{data.enum.join(', ')}]
                </div>
            )}
            {data.description && (
                <div className="text-xs text-gray-500 mt-1">
                    {data.description}
                </div>
            )}
            {data.$ref && (
                <div className="text-xs text-blue-500">
                    $ref: {data.$ref}
                </div>
            )}
            <Handle type="source" position={Position.Right} />
        </div>
    );
};