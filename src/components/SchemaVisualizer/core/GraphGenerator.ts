import { Node, <PERSON>, <PERSON>erType } from '@xyflow/react';
import { 
  JSONSchema, 
  SchemaContext, 
  NodeType, 
  EdgeType, 
  NodeData,
  EdgeData,
  SchemaGraph,
  PropertyNodeData,
  DefinitionNodeData,
  EnumNodeData,
  ReferenceNodeData,
  ConditionalNodeData,
  LogicalOperatorNodeData,
  LayoutConfig
} from '../types';

/**
 * Graph generation engine that converts analyzed schema into React Flow nodes and edges
 */
export class GraphGenerator {
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;
  private nodes: Node<NodeData>[] = [];
  private edges: Edge<EdgeData>[] = [];
  private layout: LayoutConfig;
  private context: SchemaContext;
  private levelNodeCounts: Map<number, number> = new Map(); // Track nodes per level
  private nodePositions: Map<string, { x: number; y: number }> = new Map(); // Track all positions

  constructor(layout?: Partial<LayoutConfig>) {
    this.layout = {
      nodeSpacing: { horizontal: 350, vertical: 120 }, // Increased spacing
      levelSpacing: 250,
      conditionalBranchOffset: 180,
      definitionAreaOffset: 600, // Not used anymore but kept for compatibility
      ...layout
    };
  }

  /**
   * Generate graph from analyzed schema context
   */
  public generateGraph(schema: JSONSchema, context: SchemaContext): SchemaGraph {
    this.reset();
    this.context = context;

    try {
      // Generate root node
      const rootNode = this.createRootNode(schema);
      this.nodes.push(rootNode);

      // Generate property nodes (definitions are now resolved inline)
      this.generatePropertyNodes(schema, rootNode.id, 0, 0);

      // Generate conditional nodes
      this.generateConditionalNodes();

      // Calculate final positions
      this.calculateLayout();

      return {
        nodes: this.nodes,
        edges: this.edges,
        layout: this.layout,
        metadata: {
          totalNodes: this.nodes.length,
          totalEdges: this.edges.length,
          maxDepth: this.calculateMaxDepth(),
          hasConditionals: context.conditionals.size > 0,
          hasDefinitions: context.definitions.size > 0
        }
      };
    } catch (error) {
      console.error('Graph generation failed:', error);
      return {
        nodes: [],
        edges: [],
        layout: this.layout,
        metadata: {
          totalNodes: 0,
          totalEdges: 0,
          maxDepth: 0,
          hasConditionals: false,
          hasDefinitions: false
        }
      };
    }
  }

  /**
   * Create root node
   */
  private createRootNode(schema: JSONSchema): Node<NodeData> {
    const nodeData: PropertyNodeData = {
      id: this.generateNodeId(),
      nodeType: NodeType.ROOT,
      label: schema.title || 'Root Schema',
      schemaPath: '',
      schema,
      propertyType: schema.type || 'object',
      description: schema.description,
      hasNestedProperties: !!schema.properties
    };

    return {
      id: nodeData.id,
      type: 'schemaNode',
      position: { x: 0, y: 0 },
      data: nodeData
    };
  }

  /**
   * Generate property nodes recursively
   */
  private generatePropertyNodes(
    schema: JSONSchema, 
    parentId: string, 
    level: number, 
    siblingIndex: number
  ): void {
    if (!schema.properties) return;

    const requiredFields = schema.required || [];
    const propertyKeys = Object.keys(schema.properties);

    propertyKeys.forEach((propName, index) => {
      const propSchema = schema.properties![propName];
      const isRequired = requiredFields.includes(propName);
      
      // Handle $ref properties - resolve inline instead of creating separate reference nodes
      if (propSchema.$ref) {
        const resolvedSchema = this.context.resolvedRefs.get(propSchema.$ref);
        if (resolvedSchema) {
          // Create a resolved property node that includes the definition inline
          const resolvedNode = this.createResolvedReferenceNode(
            propName,
            propSchema,
            resolvedSchema,
            level + 1,
            index,
            isRequired
          );
          this.nodes.push(resolvedNode);
          this.createEdge(parentId, resolvedNode.id, EdgeType.PROPERTY);

          // Handle resolved schema properties recursively
          if (resolvedSchema.type === 'object' && resolvedSchema.properties) {
            this.generatePropertyNodes(resolvedSchema, resolvedNode.id, level + 1, index);
          }

          // Handle resolved enum values
          if (resolvedSchema.enum) {
            const enumNode = this.createEnumNode(propName, resolvedSchema, level + 2, index);
            this.nodes.push(enumNode);
            this.createEdge(resolvedNode.id, enumNode.id, EdgeType.PROPERTY);
          }
        } else {
          // Fallback to reference node if resolution failed
          const refNode = this.createReferenceNode(propName, propSchema, level + 1, index, isRequired);
          this.nodes.push(refNode);
          this.createEdge(parentId, refNode.id, EdgeType.REFERENCE);
        }
      } else {
        // Regular property
        const propNode = this.createPropertyNode(propName, propSchema, level + 1, index, isRequired);
        this.nodes.push(propNode);
        this.createEdge(parentId, propNode.id, EdgeType.PROPERTY);

        // Handle enum properties
        if (propSchema.enum) {
          const enumNode = this.createEnumNode(propName, propSchema, level + 2, index);
          this.nodes.push(enumNode);
          this.createEdge(propNode.id, enumNode.id, EdgeType.PROPERTY);
        }

        // Handle nested objects
        if (propSchema.type === 'object' && propSchema.properties) {
          this.generatePropertyNodes(propSchema, propNode.id, level + 1, index);
        }

        // Handle arrays
        if (propSchema.type === 'array' && propSchema.items) {
          this.generateArrayItemNodes(propSchema.items, propNode.id, level + 1, index);
        }
      }
    });
  }

  /**
   * Generate array item nodes
   */
  private generateArrayItemNodes(
    itemSchema: JSONSchema, 
    parentId: string, 
    level: number, 
    siblingIndex: number
  ): void {
    const itemNode = this.createPropertyNode(
      'items', 
      itemSchema, 
      level + 1, 
      siblingIndex, 
      false,
      true
    );
    this.nodes.push(itemNode);
    this.createEdge(parentId, itemNode.id, EdgeType.PROPERTY);

    // Handle nested array item properties
    if (itemSchema.type === 'object' && itemSchema.properties) {
      this.generatePropertyNodes(itemSchema, itemNode.id, level + 1, siblingIndex);
    }
  }

  /**
   * Create property node
   */
  private createPropertyNode(
    name: string, 
    schema: JSONSchema, 
    level: number, 
    siblingIndex: number, 
    isRequired: boolean,
    isArray: boolean = false
  ): Node<PropertyNodeData> {
    const nodeData: PropertyNodeData = {
      id: this.generateNodeId(),
      nodeType: NodeType.PROPERTY,
      label: name,
      schemaPath: `properties.${name}`,
      schema,
      propertyType: schema.type || 'any',
      format: schema.format,
      isRequired,
      isArray,
      description: schema.description,
      hasNestedProperties: !!schema.properties,
      validationRules: this.context.validationRules.get(`properties.${name}`) || []
    };

    return {
      id: nodeData.id,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, siblingIndex),
      data: nodeData
    };
  }

  /**
   * Create resolved reference node that inlines the definition
   */
  private createResolvedReferenceNode(
    name: string,
    originalSchema: JSONSchema,
    resolvedSchema: JSONSchema,
    level: number,
    siblingIndex: number,
    isRequired: boolean
  ): Node<PropertyNodeData> {
    const definitionName = originalSchema.$ref?.split('/').pop() || 'unknown';

    const nodeData: PropertyNodeData = {
      id: this.generateNodeId(),
      nodeType: NodeType.PROPERTY,
      label: name,
      schemaPath: `properties.${name}`,
      schema: resolvedSchema, // Use resolved schema instead of reference
      propertyType: resolvedSchema.type || 'object',
      isRequired,
      description: resolvedSchema.description || originalSchema.description,
      hasNestedProperties: !!(resolvedSchema.properties || resolvedSchema.enum),
      // Add reference information for display
      resolvedFrom: {
        refPath: originalSchema.$ref!,
        definitionName
      }
    };

    return {
      id: nodeData.id,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, siblingIndex),
      data: nodeData
    };
  }

  /**
   * Create reference node (fallback for unresolved references)
   */
  private createReferenceNode(
    name: string,
    schema: JSONSchema,
    level: number,
    siblingIndex: number,
    isRequired: boolean
  ): Node<ReferenceNodeData> {
    const nodeData: ReferenceNodeData = {
      id: this.generateNodeId(),
      nodeType: NodeType.REFERENCE,
      label: name,
      schemaPath: `properties.${name}`,
      schema,
      refPath: schema.$ref!,
      resolvedSchema: this.context.resolvedRefs.get(schema.$ref!),
      isRequired,
      description: schema.description
    };

    return {
      id: nodeData.id,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, siblingIndex),
      data: nodeData
    };
  }

  /**
   * Create enum node
   */
  private createEnumNode(
    baseName: string, 
    schema: JSONSchema, 
    level: number, 
    siblingIndex: number
  ): Node<EnumNodeData> {
    const nodeData: EnumNodeData = {
      id: this.generateNodeId(),
      nodeType: NodeType.ENUM,
      label: `${baseName} values`,
      schemaPath: `properties.${baseName}.enum`,
      schema,
      enumValues: schema.enum || [],
      enumNames: schema.enumNames,
      description: 'Allowed values'
    };

    return {
      id: nodeData.id,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, siblingIndex),
      data: nodeData
    };
  }

  /**
   * Generate definition nodes
   */
  private generateDefinitionNodes(): void {
    let definitionIndex = 0;
    
    this.context.definitions.forEach((definition, refPath) => {
      const definitionName = refPath.split('/').pop() || 'unknown';
      
      const nodeData: DefinitionNodeData = {
        id: this.generateNodeId(),
        nodeType: NodeType.DEFINITION,
        label: definitionName,
        schemaPath: refPath,
        schema: definition,
        definitionName,
        referencedBy: this.findReferencesToDefinition(refPath),
        description: definition.description
      };

      const definitionNode: Node<DefinitionNodeData> = {
        id: nodeData.id,
        type: 'schemaNode',
        position: {
          x: this.layout.definitionAreaOffset,
          y: definitionIndex * this.layout.nodeSpacing.vertical
        },
        data: nodeData
      };

      this.nodes.push(definitionNode);
      definitionIndex++;
    });
  }

  /**
   * Generate conditional nodes (placeholder for future implementation)
   */
  private generateConditionalNodes(): void {
    // This will be implemented when we add conditional logic support
    // For now, we'll create placeholder nodes for complex conditionals
    this.context.conditionals.forEach((conditional, id) => {
      const nodeData: ConditionalNodeData = {
        id: this.generateNodeId(),
        nodeType: NodeType.CONDITIONAL,
        label: `${conditional.type} condition`,
        schemaPath: conditional.parentPath,
        schema: conditional.condition || {},
        conditionalLogic: conditional,
        description: `Conditional logic: ${conditional.type}`
      };

      // Position conditionals in a separate area for now
      const conditionalNode: Node<ConditionalNodeData> = {
        id: nodeData.id,
        type: 'schemaNode',
        position: {
          x: this.layout.definitionAreaOffset + 300,
          y: Array.from(this.context.conditionals.keys()).indexOf(id) * this.layout.nodeSpacing.vertical
        },
        data: nodeData
      };

      this.nodes.push(conditionalNode);
    });
  }

  /**
   * Create edge between nodes
   */
  private createEdge(
    sourceId: string, 
    targetId: string, 
    edgeType: EdgeType, 
    label?: string,
    isConditional: boolean = false
  ): void {
    const edgeData: EdgeData = {
      edgeType,
      label,
      isConditional
    };

    const edge: Edge<EdgeData> = {
      id: this.generateEdgeId(),
      source: sourceId,
      target: targetId,
      type: 'smoothstep',
      animated: edgeType === EdgeType.CONDITIONAL,
      style: isConditional ? { strokeDasharray: '5,5' } : undefined,
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 15,
        height: 15
      },
      data: edgeData
    };

    this.edges.push(edge);
  }

  /**
   * Calculate node position with improved spacing and collision avoidance
   */
  private calculateNodePosition(level: number, siblingIndex: number): { x: number; y: number } {
    // Get current count of nodes at this level
    const currentCount = this.levelNodeCounts.get(level) || 0;
    this.levelNodeCounts.set(level, currentCount + 1);

    // Calculate base position
    const x = level * this.layout.nodeSpacing.horizontal;
    const y = currentCount * this.layout.nodeSpacing.vertical;

    // Check for collisions and adjust if necessary
    const position = this.findNonCollidingPosition(x, y, level);

    // Store the position
    const nodeId = `level-${level}-node-${currentCount}`;
    this.nodePositions.set(nodeId, position);

    return position;
  }

  /**
   * Find a position that doesn't collide with existing nodes
   */
  private findNonCollidingPosition(baseX: number, baseY: number, level: number): { x: number; y: number } {
    const minDistance = 80; // Minimum distance between nodes
    let x = baseX;
    let y = baseY;
    let attempts = 0;
    const maxAttempts = 50;

    while (attempts < maxAttempts) {
      let hasCollision = false;

      // Check against all existing positions
      for (const existingPos of this.nodePositions.values()) {
        const distance = Math.sqrt(
          Math.pow(x - existingPos.x, 2) + Math.pow(y - existingPos.y, 2)
        );

        if (distance < minDistance) {
          hasCollision = true;
          break;
        }
      }

      if (!hasCollision) {
        return { x, y };
      }

      // Try next position - move down first, then adjust horizontally if needed
      y += this.layout.nodeSpacing.vertical * 0.3;
      if (attempts % 10 === 9) {
        x += this.layout.nodeSpacing.horizontal * 0.2;
        y = baseY; // Reset Y and try new X
      }

      attempts++;
    }

    // Fallback to original position if no collision-free position found
    return { x: baseX, y: baseY };
  }

  /**
   * Calculate layout positions (placeholder for more sophisticated layout)
   */
  private calculateLayout(): void {
    // For now, we'll use the basic positioning
    // Future: Implement more sophisticated layout algorithms
  }

  /**
   * Find all references to a definition
   */
  private findReferencesToDefinition(refPath: string): string[] {
    // This would scan through all nodes to find references
    // For now, return empty array
    return [];
  }

  /**
   * Calculate maximum depth of the graph
   */
  private calculateMaxDepth(): number {
    return Math.max(...this.nodes.map(node => 
      Math.floor(node.position.x / this.layout.nodeSpacing.horizontal)
    ));
  }

  /**
   * Generate unique node ID
   */
  private generateNodeId(): string {
    return `node-${this.nodeIdCounter++}`;
  }

  /**
   * Generate unique edge ID
   */
  private generateEdgeId(): string {
    return `edge-${this.edgeIdCounter++}`;
  }

  /**
   * Reset generator state
   */
  private reset(): void {
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
    this.nodes = [];
    this.edges = [];
    this.levelNodeCounts.clear();
    this.nodePositions.clear();
  }
}
