import { Node, <PERSON>, <PERSON>erType } from '@xyflow/react';
import {
  JSONSchema,
  SchemaContext,
  NodeType,
  EdgeType,
  NodeData,
  EdgeData,
  SchemaGraph,
  PropertyNodeData,
  DefinitionNodeData,
  EnumNodeData,
  ReferenceNodeData,
  ConditionalNodeData,
  LayoutConfig
} from '../types';

/**
 * Graph generation engine that converts analyzed schema into React Flow nodes and edges
 */
export class GraphGenerator {
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;
  private nodes: Node<NodeData>[] = [];
  private edges: Edge<EdgeData>[] = [];
  private layout: LayoutConfig;
  private context!: SchemaContext; // Will be set in generate method
  private hierarchyTracker: Map<string, { level: number; children: string[]; parent?: string }> = new Map();
  private levelYOffsets: Map<number, number> = new Map(); // Track Y offset per level

  constructor(layout?: Partial<LayoutConfig>) {
    this.layout = {
      nodeSpacing: { horizontal: 400, vertical: 150 }, // Further increased spacing
      levelSpacing: 300,
      conditionalBranchOffset: 200,
      definitionAreaOffset: 700, // For definition nodes
      ...layout
    };
  }

  /**
   * Generate graph from analyzed schema context
   */
  public generateGraph(schema: JSONSchema, context: SchemaContext): SchemaGraph {
    this.reset();
    this.context = context;

    try {
      // Generate root node
      const rootNode = this.createRootNode(schema);
      this.nodes.push(rootNode);

      // Generate property nodes
      this.generatePropertyNodes(schema, rootNode.id, 0, 0);

      // Generate definition nodes with proper connections
      this.generateDefinitionNodes();

      // Handle root-level conditional logic
      this.handleConditionalLogic(schema, rootNode.id, 1, '');

      // Calculate final positions
      this.calculateLayout();

      return {
        nodes: this.nodes,
        edges: this.edges,
        layout: this.layout,
        metadata: {
          totalNodes: this.nodes.length,
          totalEdges: this.edges.length,
          maxDepth: this.calculateMaxDepth(),
          hasConditionals: context.conditionals.size > 0,
          hasDefinitions: context.definitions.size > 0
        }
      };
    } catch (error) {
      console.error('Graph generation failed:', error);
      return {
        nodes: [],
        edges: [],
        layout: this.layout,
        metadata: {
          totalNodes: 0,
          totalEdges: 0,
          maxDepth: 0,
          hasConditionals: false,
          hasDefinitions: false
        }
      };
    }
  }

  /**
   * Create root node
   */
  private createRootNode(schema: JSONSchema): Node<NodeData> {
    const nodeId = this.generateNodeId();
    const nodeData: PropertyNodeData = {
      id: nodeId,
      nodeType: NodeType.ROOT,
      label: schema.title || 'Root Schema',
      schemaPath: '',
      schema,
      propertyType: schema.type || 'object',
      description: schema.description,
      hasNestedProperties: !!schema.properties
    };

    // Register root node in hierarchy
    this.registerNodeInHierarchy(nodeId, 0);

    return {
      id: nodeId,
      type: 'schemaNode',
      position: { x: 0, y: 0 },
      data: nodeData
    };
  }

  /**
   * Generate property nodes recursively
   */
  private generatePropertyNodes(
    schema: JSONSchema,
    parentId: string,
    level: number,
    _siblingIndex: number
  ): void {
    if (!schema.properties) return;

    const requiredFields = schema.required || [];
    const propertyKeys = Object.keys(schema.properties);

    propertyKeys.forEach((propName, index) => {
      const propSchema = schema.properties![propName];
      const isRequired = requiredFields.includes(propName);
      
      // Handle $ref properties - create reference nodes that connect to definition nodes
      if (propSchema.$ref) {
        const refNode = this.createReferenceNode(propName, propSchema, level + 1, index, isRequired, parentId);
        this.nodes.push(refNode);
        this.createEdge(parentId, refNode.id, EdgeType.REFERENCE);
      } else {
        // Regular property
        const propNode = this.createPropertyNode(propName, propSchema, level + 1, index, isRequired, false, parentId);
        this.nodes.push(propNode);
        this.createEdge(parentId, propNode.id, EdgeType.PROPERTY);

        // Handle enum properties
        if (propSchema.enum) {
          const enumNode = this.createEnumNode(propName, propSchema, level + 2, index, propNode.id);
          this.nodes.push(enumNode);
          this.createEdge(propNode.id, enumNode.id, EdgeType.PROPERTY);
        }

        // Handle nested objects
        if (propSchema.type === 'object' && propSchema.properties) {
          this.generatePropertyNodes(propSchema, propNode.id, level + 1, index);
        }

        // Handle arrays
        if (propSchema.type === 'array' && propSchema.items) {
          this.generateArrayItemNodes(propSchema.items, propNode.id, level + 1, index);
        }

        // Handle conditional logic (allOf, anyOf, oneOf) inline
        this.handleConditionalLogic(propSchema, propNode.id, level + 1, `properties.${propName}`);
      }
    });
  }

  /**
   * Generate array item nodes
   */
  private generateArrayItemNodes(
    itemSchema: JSONSchema, 
    parentId: string, 
    level: number, 
    siblingIndex: number
  ): void {
    const itemNode = this.createPropertyNode(
      'items', 
      itemSchema, 
      level + 1, 
      siblingIndex, 
      false,
      true
    );
    this.nodes.push(itemNode);
    this.createEdge(parentId, itemNode.id, EdgeType.PROPERTY);

    // Handle nested array item properties
    if (itemSchema.type === 'object' && itemSchema.properties) {
      this.generatePropertyNodes(itemSchema, itemNode.id, level + 1, siblingIndex);
    }
  }

  /**
   * Create property node
   */
  private createPropertyNode(
    name: string,
    schema: JSONSchema,
    level: number,
    _siblingIndex: number,
    isRequired: boolean,
    isArray: boolean = false,
    parentId?: string
  ): Node<PropertyNodeData> {
    const nodeId = this.generateNodeId();
    const nodeData: PropertyNodeData = {
      id: nodeId,
      nodeType: NodeType.PROPERTY,
      label: schema.title || name, // Use title first, fallback to name/key
      schemaPath: `properties.${name}`,
      schema,
      propertyType: schema.type || 'any',
      format: schema.format,
      isRequired,
      isArray,
      description: schema.description,
      hasNestedProperties: !!schema.properties,
      validationRules: this.context.validationRules.get(`properties.${name}`) || []
    };

    // Register in hierarchy
    this.registerNodeInHierarchy(nodeId, level, parentId);

    return {
      id: nodeId,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, parentId),
      data: nodeData
    };
  }



  /**
   * Create reference node that will connect to definition nodes
   */
  private createReferenceNode(
    name: string,
    schema: JSONSchema,
    level: number,
    _siblingIndex: number,
    isRequired: boolean,
    parentId?: string
  ): Node<ReferenceNodeData> {
    const nodeId = this.generateNodeId();
    const nodeData: ReferenceNodeData = {
      id: nodeId,
      nodeType: NodeType.REFERENCE,
      label: schema.title || name, // Use title first, fallback to name
      schemaPath: `properties.${name}`,
      schema,
      refPath: schema.$ref!,
      resolvedSchema: this.context.resolvedRefs.get(schema.$ref!),
      isRequired,
      description: schema.description
    };

    // Register in hierarchy
    this.registerNodeInHierarchy(nodeId, level, parentId);

    return {
      id: nodeId,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, parentId),
      data: nodeData
    };
  }

  /**
   * Generate definition nodes and connect them to reference nodes
   */
  private generateDefinitionNodes(): void {
    const definitionLevel = 1; // Place definitions at level 1
    let definitionIndex = 0;

    this.context.definitions.forEach((definition, refPath) => {
      const definitionName = refPath.split('/').pop() || 'unknown';
      const nodeId = this.generateNodeId();

      const nodeData: DefinitionNodeData = {
        id: nodeId,
        nodeType: NodeType.DEFINITION,
        label: definition.title || definitionName, // Use title first, fallback to name
        schemaPath: refPath,
        schema: definition,
        definitionName,
        referencedBy: this.findReferencesToDefinition(refPath),
        description: definition.description
      };

      // Register in hierarchy
      this.registerNodeInHierarchy(nodeId, definitionLevel);

      const definitionNode: Node<DefinitionNodeData> = {
        id: nodeId,
        type: 'schemaNode',
        position: {
          x: this.layout.definitionAreaOffset,
          y: definitionIndex * this.layout.nodeSpacing.vertical
        },
        data: nodeData
      };

      this.nodes.push(definitionNode);

      // Connect all reference nodes to this definition
      this.connectReferencesToDefinition(refPath, nodeId);

      // Generate properties for the definition
      if (definition.properties) {
        this.generatePropertyNodes(definition, nodeId, definitionLevel + 1, 0);
      }

      definitionIndex++;
    });
  }

  /**
   * Find all references to a specific definition
   */
  private findReferencesToDefinition(_refPath: string): string[] {
    const references: string[] = [];

    // This would be populated during the analysis phase
    // For now, return empty array as references will be connected dynamically
    return references;
  }

  /**
   * Connect reference nodes to their corresponding definition node
   */
  private connectReferencesToDefinition(refPath: string, definitionNodeId: string): void {
    // Find all reference nodes that point to this definition
    this.nodes.forEach(node => {
      if (node.data.nodeType === NodeType.REFERENCE &&
          (node.data as ReferenceNodeData).refPath === refPath) {
        this.createEdge(node.id, definitionNodeId, EdgeType.REFERENCE);
      }
    });
  }

  /**
   * Create conditional node
   */
  private createConditionalNode(
    type: 'allOf' | 'anyOf' | 'oneOf',
    schemas: JSONSchema[],
    level: number,
    schemaPath: string,
    parentId?: string
  ): Node<ConditionalNodeData> {
    const nodeId = this.generateNodeId();
    const nodeData: ConditionalNodeData = {
      id: nodeId,
      nodeType: NodeType.CONDITIONAL,
      label: `${type} condition`,
      schemaPath,
      schema: { [type]: schemas },
      conditionalLogic: {
        id: `${type}-${schemaPath}`,
        type,
        branches: schemas.map((schema, index) => ({
          id: `${type}-${schemaPath}-${index}`,
          schema
        })),
        parentPath: schemaPath
      },
      description: `Conditional logic: ${type}`
    };

    // Register in hierarchy
    this.registerNodeInHierarchy(nodeId, level, parentId);

    return {
      id: nodeId,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, parentId),
      data: nodeData
    };
  }

  /**
   * Create enum node
   */
  private createEnumNode(
    baseName: string,
    schema: JSONSchema,
    level: number,
    _siblingIndex: number,
    parentId?: string
  ): Node<EnumNodeData> {
    const nodeId = this.generateNodeId();
    const nodeData: EnumNodeData = {
      id: nodeId,
      nodeType: NodeType.ENUM,
      label: `${baseName} values`,
      schemaPath: `properties.${baseName}.enum`,
      schema,
      enumValues: schema.enum || [],
      enumNames: schema.enumNames,
      description: 'Allowed values'
    };

    // Register in hierarchy
    this.registerNodeInHierarchy(nodeId, level, parentId);

    return {
      id: nodeId,
      type: 'schemaNode',
      position: this.calculateNodePosition(level, parentId),
      data: nodeData
    };
  }



  /**
   * Handle conditional logic inline with proper parent connections
   */
  private handleConditionalLogic(
    schema: JSONSchema,
    parentId: string,
    level: number,
    schemaPath: string
  ): void {
    // Handle allOf
    if (schema.allOf && schema.allOf.length > 0) {
      const allOfNode = this.createConditionalNode('allOf', schema.allOf, level, schemaPath, parentId);
      this.nodes.push(allOfNode);
      this.createEdge(parentId, allOfNode.id, EdgeType.CONDITIONAL);

      // Process each allOf branch
      schema.allOf.forEach((subSchema, index) => {
        if (subSchema.properties) {
          this.generatePropertyNodes(subSchema, allOfNode.id, level + 1, index);
        }
      });
    }

    // Handle anyOf
    if (schema.anyOf && schema.anyOf.length > 0) {
      const anyOfNode = this.createConditionalNode('anyOf', schema.anyOf, level, schemaPath, parentId);
      this.nodes.push(anyOfNode);
      this.createEdge(parentId, anyOfNode.id, EdgeType.CONDITIONAL);

      // Process each anyOf branch
      schema.anyOf.forEach((subSchema, index) => {
        if (subSchema.properties) {
          this.generatePropertyNodes(subSchema, anyOfNode.id, level + 1, index);
        }
      });
    }

    // Handle oneOf
    if (schema.oneOf && schema.oneOf.length > 0) {
      const oneOfNode = this.createConditionalNode('oneOf', schema.oneOf, level, schemaPath, parentId);
      this.nodes.push(oneOfNode);
      this.createEdge(parentId, oneOfNode.id, EdgeType.CONDITIONAL);

      // Process each oneOf branch
      schema.oneOf.forEach((subSchema, index) => {
        if (subSchema.properties) {
          this.generatePropertyNodes(subSchema, oneOfNode.id, level + 1, index);
        }
      });
    }
  }

  /**
   * Create edge between nodes
   */
  private createEdge(
    sourceId: string, 
    targetId: string, 
    edgeType: EdgeType, 
    label?: string,
    isConditional: boolean = false
  ): void {
    const edgeData: EdgeData = {
      edgeType,
      label,
      isConditional
    };

    const edge: Edge<EdgeData> = {
      id: this.generateEdgeId(),
      source: sourceId,
      target: targetId,
      type: 'smoothstep',
      animated: edgeType === EdgeType.CONDITIONAL,
      style: isConditional ? { strokeDasharray: '5,5' } : undefined,
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 15,
        height: 15
      },
      data: edgeData
    };

    this.edges.push(edge);
  }

  /**
   * Calculate hierarchical node position
   */
  private calculateNodePosition(level: number, _parentId?: string): { x: number; y: number } {
    const x = level * this.layout.nodeSpacing.horizontal;

    // Get current Y offset for this level
    const y = this.levelYOffsets.get(level) || 0;

    // Update Y offset for next node at this level
    this.levelYOffsets.set(level, y + this.layout.nodeSpacing.vertical);

    return { x, y };
  }

  /**
   * Register node in hierarchy tracker
   */
  private registerNodeInHierarchy(nodeId: string, level: number, parentId?: string): void {
    this.hierarchyTracker.set(nodeId, {
      level,
      children: [],
      parent: parentId
    });

    // Add to parent's children if parent exists
    if (parentId && this.hierarchyTracker.has(parentId)) {
      const parent = this.hierarchyTracker.get(parentId)!;
      parent.children.push(nodeId);
    }
  }

  /**
   * Calculate layout positions (placeholder for more sophisticated layout)
   */
  private calculateLayout(): void {
    // For now, we'll use the basic positioning
    // Future: Implement more sophisticated layout algorithms
  }



  /**
   * Calculate maximum depth of the graph
   */
  private calculateMaxDepth(): number {
    return Math.max(...this.nodes.map(node => 
      Math.floor(node.position.x / this.layout.nodeSpacing.horizontal)
    ));
  }

  /**
   * Generate unique node ID
   */
  private generateNodeId(): string {
    return `node-${this.nodeIdCounter++}`;
  }

  /**
   * Generate unique edge ID
   */
  private generateEdgeId(): string {
    return `edge-${this.edgeIdCounter++}`;
  }

  /**
   * Reset generator state
   */
  private reset(): void {
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
    this.nodes = [];
    this.edges = [];
    this.hierarchyTracker.clear();
    this.levelYOffsets.clear();
  }
}
