import { 
  JSONSchema, 
  SchemaContext, 
  ConditionalLogic, 
  ConditionalBranch, 
  ValidationRule,
  SchemaAnalysisResult 
} from '../types';

/**
 * Core schema analysis engine that parses and analyzes JSON schemas
 * Handles definitions, references, conditionals, and validation rules
 */
export class SchemaAnalyzer {
  private context: SchemaContext;
  private errors: string[] = [];
  private warnings: string[] = [];

  constructor() {
    this.context = {
      definitions: new Map(),
      resolvedRefs: new Map(),
      conditionals: new Map(),
      validationRules: new Map(),
      schemaPath: []
    };
  }

  /**
   * Main analysis method - analyzes the entire schema
   */
  public analyze(schema: JSONSchema): SchemaAnalysisResult {
    this.reset();
    
    try {
      // Step 1: Extract and index definitions
      this.extractDefinitions(schema);
      
      // Step 2: Analyze the main schema structure
      this.analyzeSchema(schema, []);
      
      // Step 3: Resolve all references
      this.resolveAllReferences();
      
      // Step 4: Extract conditional logic
      this.extractConditionalLogic(schema, []);
      
      // Step 5: Extract validation rules
      this.extractValidationRules(schema, []);

      return {
        context: this.context,
        graph: { nodes: [], edges: [], layout: this.getDefaultLayout(), metadata: this.getMetadata() },
        errors: this.errors,
        warnings: this.warnings
      };
    } catch (error) {
      this.errors.push(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        context: this.context,
        graph: { nodes: [], edges: [], layout: this.getDefaultLayout(), metadata: this.getMetadata() },
        errors: this.errors,
        warnings: this.warnings
      };
    }
  }

  /**
   * Extract and index all definitions from the schema
   */
  private extractDefinitions(schema: JSONSchema): void {
    if (schema.definitions) {
      Object.entries(schema.definitions).forEach(([name, definition]) => {
        const refPath = `#/definitions/${name}`;
        this.context.definitions.set(refPath, definition);
        
        // Recursively extract nested definitions
        if (definition.definitions) {
          this.extractDefinitions(definition);
        }
      });
    }
  }

  /**
   * Recursively analyze schema structure
   */
  private analyzeSchema(schema: JSONSchema, path: string[]): void {
    this.context.schemaPath = path;

    // Handle properties
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]) => {
        this.analyzeSchema(propSchema, [...path, propName]);
      });
    }

    // Handle array items
    if (schema.items) {
      this.analyzeSchema(schema.items, [...path, 'items']);
    }

    // Handle logical operators
    if (schema.allOf) {
      schema.allOf.forEach((subSchema, index) => {
        this.analyzeSchema(subSchema, [...path, 'allOf', index.toString()]);
      });
    }

    if (schema.anyOf) {
      schema.anyOf.forEach((subSchema, index) => {
        this.analyzeSchema(subSchema, [...path, 'anyOf', index.toString()]);
      });
    }

    if (schema.oneOf) {
      schema.oneOf.forEach((subSchema, index) => {
        this.analyzeSchema(subSchema, [...path, 'oneOf', index.toString()]);
      });
    }

    // Handle conditional logic
    if (schema.if && schema.then) {
      this.analyzeSchema(schema.if, [...path, 'if']);
      this.analyzeSchema(schema.then, [...path, 'then']);
      if (schema.else) {
        this.analyzeSchema(schema.else, [...path, 'else']);
      }
    }
  }

  /**
   * Resolve all $ref references
   */
  private resolveAllReferences(): void {
    this.context.definitions.forEach((definition, refPath) => {
      try {
        const resolved = this.resolveReference(definition);
        this.context.resolvedRefs.set(refPath, resolved);
      } catch (error) {
        this.errors.push(`Failed to resolve reference ${refPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });
  }

  /**
   * Resolve a single reference
   */
  private resolveReference(schema: JSONSchema): JSONSchema {
    if (schema.$ref) {
      const referenced = this.context.definitions.get(schema.$ref);
      if (!referenced) {
        throw new Error(`Reference not found: ${schema.$ref}`);
      }
      return this.resolveReference(referenced);
    }
    return schema;
  }

  /**
   * Extract conditional logic patterns
   */
  private extractConditionalLogic(schema: JSONSchema, path: string[]): void {
    const pathKey = path.join('.');

    // Handle if-then-else
    if (schema.if && schema.then) {
      const conditional: ConditionalLogic = {
        id: `conditional-${pathKey}`,
        type: 'if-then-else',
        condition: schema.if,
        branches: [
          {
            id: `then-${pathKey}`,
            condition: schema.if,
            schema: schema.then
          }
        ],
        parentPath: pathKey
      };

      if (schema.else) {
        conditional.branches.push({
          id: `else-${pathKey}`,
          schema: schema.else,
          isDefault: true
        });
      }

      this.context.conditionals.set(conditional.id, conditional);
    }

    // Handle allOf
    if (schema.allOf) {
      const conditional: ConditionalLogic = {
        id: `allOf-${pathKey}`,
        type: 'allOf',
        branches: schema.allOf.map((subSchema, index) => ({
          id: `allOf-${pathKey}-${index}`,
          schema: subSchema
        })),
        parentPath: pathKey
      };
      this.context.conditionals.set(conditional.id, conditional);
    }

    // Handle anyOf
    if (schema.anyOf) {
      const conditional: ConditionalLogic = {
        id: `anyOf-${pathKey}`,
        type: 'anyOf',
        branches: schema.anyOf.map((subSchema, index) => ({
          id: `anyOf-${pathKey}-${index}`,
          schema: subSchema
        })),
        parentPath: pathKey
      };
      this.context.conditionals.set(conditional.id, conditional);
    }

    // Handle oneOf
    if (schema.oneOf) {
      const conditional: ConditionalLogic = {
        id: `oneOf-${pathKey}`,
        type: 'oneOf',
        branches: schema.oneOf.map((subSchema, index) => ({
          id: `oneOf-${pathKey}-${index}`,
          schema: subSchema
        })),
        parentPath: pathKey
      };
      this.context.conditionals.set(conditional.id, conditional);
    }

    // Recursively process nested schemas
    this.recursivelyExtractConditionals(schema, path);
  }

  /**
   * Recursively extract conditionals from nested schemas
   */
  private recursivelyExtractConditionals(schema: JSONSchema, path: string[]): void {
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]) => {
        this.extractConditionalLogic(propSchema, [...path, propName]);
      });
    }

    if (schema.items) {
      this.extractConditionalLogic(schema.items, [...path, 'items']);
    }
  }

  /**
   * Extract validation rules from schema
   */
  private extractValidationRules(schema: JSONSchema, path: string[]): void {
    const pathKey = path.join('.');
    const rules: ValidationRule[] = [];

    // Required validation
    if (schema.required && schema.required.length > 0) {
      rules.push({
        type: 'required',
        value: schema.required,
        message: 'This field is required'
      });
    }

    // Format validation
    if (schema.format) {
      rules.push({
        type: 'format',
        value: schema.format,
        message: `Must be in ${schema.format} format`
      });
    }

    // Pattern validation
    if (schema.pattern) {
      rules.push({
        type: 'pattern',
        value: schema.pattern,
        message: 'Must match the required pattern'
      });
    }

    // Range validation
    if (schema.minimum !== undefined || schema.maximum !== undefined) {
      rules.push({
        type: 'range',
        value: { min: schema.minimum, max: schema.maximum },
        message: 'Value must be within the specified range'
      });
    }

    // Length validation
    if (schema.minLength !== undefined || schema.maxLength !== undefined) {
      rules.push({
        type: 'length',
        value: { min: schema.minLength, max: schema.maxLength },
        message: 'Length must be within the specified range'
      });
    }

    // Items validation
    if (schema.minItems !== undefined || schema.maxItems !== undefined) {
      rules.push({
        type: 'items',
        value: { min: schema.minItems, max: schema.maxItems },
        message: 'Number of items must be within the specified range'
      });
    }

    if (rules.length > 0) {
      this.context.validationRules.set(pathKey, rules);
    }

    // Recursively process nested schemas
    this.recursivelyExtractValidations(schema, path);
  }

  /**
   * Recursively extract validations from nested schemas
   */
  private recursivelyExtractValidations(schema: JSONSchema, path: string[]): void {
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]) => {
        this.extractValidationRules(propSchema, [...path, propName]);
      });
    }

    if (schema.items) {
      this.extractValidationRules(schema.items, [...path, 'items']);
    }
  }

  /**
   * Reset analyzer state
   */
  private reset(): void {
    this.context = {
      definitions: new Map(),
      resolvedRefs: new Map(),
      conditionals: new Map(),
      validationRules: new Map(),
      schemaPath: []
    };
    this.errors = [];
    this.warnings = [];
  }

  /**
   * Get default layout configuration
   */
  private getDefaultLayout() {
    return {
      nodeSpacing: { horizontal: 300, vertical: 100 },
      levelSpacing: 200,
      conditionalBranchOffset: 150,
      definitionAreaOffset: 500
    };
  }

  /**
   * Get metadata about the analysis
   */
  private getMetadata() {
    return {
      totalNodes: 0,
      totalEdges: 0,
      maxDepth: 0,
      hasConditionals: this.context.conditionals.size > 0,
      hasDefinitions: this.context.definitions.size > 0
    };
  }
}
