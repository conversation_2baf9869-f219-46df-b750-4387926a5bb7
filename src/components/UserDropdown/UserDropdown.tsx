import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import plaseholder from "@/assets/plaseholder.svg"
import { LogOut, Settings } from "lucide-react"
import { useTranslation } from "react-i18next"
import { useAuth } from "react-oidc-context"


export const UserDropdown = () => {
    const { t, i18n } = useTranslation()
    const { removeUser } = useAuth()
    const logout = async () => {
        await removeUser()
        location.href = '/'
    }
    return (
        <DropdownMenu >
            <DropdownMenuTrigger asChild >
                <div className="bg-[#F2F2F5] rounded-full cursor-pointer">
                    <img
                        src={plaseholder}
                        alt="placeholder"
                        className="object-contain w-10 h-10"
                    />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center" className={`${i18n.language === "ar" ? "rtl" : "ltr"} mx-[1rem]`}>
                <DropdownMenuItem><Settings className="h-4 w-4" /> <span>{t('settings')}</span></DropdownMenuItem>
                <DropdownMenuItem onClick={logout}><LogOut className="h-4 w-4" /> <span>{t('signOut')}</span></DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}