import { Button } from "@/components/ui/button";
import uploadlogo from "@/assets/upload.svg";
import linklogo from "@/assets/link.svg";
import marketlogo from "@/assets/market.svg";
import createWorkSpaceLogo from "@/assets/createWorkSpace.svg";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { CreateWorkspaceModal } from "@/components/CreateWorkspaceModal/CreateWorkspaceModal";
import { showToast } from "@/shared/utils/toastConfig";
import { useMutation } from "@apollo/client";
import { CREATE_EMPTY_WORKSPACE } from "@/shared/graphQl/mutations/dataset";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { Routes } from "@/shared/utils/routes";
import { setSelectedMainLayer, setSelectedTableLayer } from "@/shared/store/slices/mapSlice";
import { useDispatch } from "react-redux";

interface WorkspaceActionsProps {
    onUploadClick: () => void;
    onDesignLayer: () => void;
    orgId?: number;
}

export const WorkspaceActions: React.FC<WorkspaceActionsProps> = ({ onUploadClick, onDesignLayer, orgId }) => {
    const { t, i18n } = useTranslation();
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isCreatingWorkspace, setIsCreatingWorkspace] = useState(false);
    const { navigateWithOrg } = useOrganizationManager();
    const dispatch = useDispatch();

    const [createEmptyWorkspace] = useMutation(CREATE_EMPTY_WORKSPACE);

    const handleCreateWorkspace = async (workspaceName: string, description: string) => {
        if (!orgId) return;

        setIsCreatingWorkspace(true);

        try {
            const { data } = await createEmptyWorkspace({
                variables: {
                    dataInput: {
                        orgId,
                        name: workspaceName,
                        description
                    }
                }
            });

            if (data?.createEmptyWorkspace?.workspace) {
                showToast.success(t('alerts.success.workspaceCreated'));
                dispatch(setSelectedMainLayer(null));
                dispatch(setSelectedTableLayer(null));
                setIsCreateModalOpen(false);
                localStorage.setItem('currentWorkspaceTitle', data.createEmptyWorkspace.workspace.name);
                navigateWithOrg(`/${Routes.map}/${data.createEmptyWorkspace.workspace.id}?fromCreation=true&type=CREATE_EMPTY`);
            }
        } catch (error) {
            console.error('Error creating workspace:', error);
            showToast.error(t('alerts.error.workspaceCreateError'));
        } finally {
            setIsCreatingWorkspace(false);
        }
    };

    const workspaceActions = [
        { icon: createWorkSpaceLogo, label: t('createWorkspace'), text: t('createworkspaceDesc'), onClick: () => setIsCreateModalOpen(true) },
        { icon: uploadlogo, label: t('uploadFile'), text: t('uploadFileDescription'), onClick: onUploadClick },
        { icon: marketlogo, label: t('designLayer'), text: t('designLayerDescription'), onClick: onDesignLayer },
        { icon: linklogo, label: t('connectDatabase'), text: t('connectDatabaseDescription') },
    ];

    return (
        <>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                {workspaceActions.map((item, index) => (
                    <Button
                        key={index}
                        variant="outline"
                        className="flex py-3 px-3 h-auto items-center justify-start gap-4 rounded-lg"
                        onClick={item.onClick}
                        disabled={!orgId || !item.onClick}
                    >
                        <div className="flex-shrink-0">
                            <img src={item.icon} alt={item.label} className="w-18 h-18" />
                        </div>
                        <div className={`flex flex-col gap-2 ${i18n.dir() === 'rtl' ? 'text-right' : 'text-left'}`}>
                            <span className="text-base font-semibold text-gray-800">{item.label}</span>
                            <p className="text-sm text-gray-600 whitespace-normal break-words leading-relaxed">{item.text}</p>
                        </div>
                    </Button>
                ))}
            </div>

            <CreateWorkspaceModal
                isOpen={isCreateModalOpen}
                onClose={() => setIsCreateModalOpen(false)}
                onSubmit={handleCreateWorkspace}
                isLoading={isCreatingWorkspace}
            />
        </>
    );
};