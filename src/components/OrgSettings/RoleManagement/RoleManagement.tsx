import { But<PERSON> } from "@/components/ui/button"
import { Plus, Loader2 } from "lucide-react"
import { useTranslation } from "react-i18next"
import { RoleCard } from "./RoleCard/RoleCard"
import { useEffect } from "react"
import { RoleModal } from "./RoleModal/RoleModal"
import { useDispatch, useSelector } from "react-redux"
import { AppDispatch, RootState } from "@/shared/store/store"
import {
    fetchRoles,
    setRoleModalOpen,
    setRoleModalMode,
    setCurrentRole,
} from "@/shared/store/slices/organizationSettingsSlice"
import { Role } from "@/shared/types/organization"

export const RoleManagement = () => {
    const { t } = useTranslation()
    const dispatch = useDispatch<AppDispatch>()

    const {
        orgId,
        roles,
        loadingRoles,
        isRoleModalOpen,
        roleModalMode,
        currentRole
    } = useSelector((state: RootState) => state.orgSettings)

    useEffect(() => {
        if (orgId) {
            dispatch(fetchRoles(orgId))
        }
    }, [orgId, dispatch])

    const handleCreateRole = () => {
        dispatch(setRoleModalMode("create"))
        dispatch(setCurrentRole(null))
        dispatch(setRoleModalOpen(true))
    }

    const handleEditRole = (role: Role) => {
        dispatch(setRoleModalMode("edit"))
        dispatch(setCurrentRole(role))
        dispatch(setRoleModalOpen(true))
    }

    const handleCloseModal = (shouldRefetch = false) => {
        dispatch(setRoleModalOpen(false))
        if (shouldRefetch && orgId) {
            dispatch(fetchRoles(orgId))
        }
    }

    return (
        <div className="space-y-6">
            {/* Header section */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">{t("roleManagement")}</h2>
                    <p className="text-muted-foreground">{t("roleManagementDescription")}</p>
                </div>
                <Button
                    className="gap-2 bg-[#5E58EE] hover:bg-[#5E58EE] rounded-full"
                    onClick={handleCreateRole}
                >
                    <span>{t("newRole")}</span>
                    <Plus size={16} />
                </Button>
            </div>

            {/* Roles list */}
            <div className="space-y-4">
                {loadingRoles ? (
                    <div className="flex items-center justify-center h-64">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    </div>
                ) : roles.length === 0 ? (
                    <div className="text-center py-8 border rounded-lg bg-gray-50">
                        {t("noRolesFound")}
                    </div>
                ) : (
                    roles.map((role) => (
                        <RoleCard
                            key={role.id}
                            role={role}
                            onEdit={() => handleEditRole(role)}
                            onDelete={() => dispatch(fetchRoles(orgId as string))}
                        />
                    ))
                )}
            </div>

            {/* Role creation/editing modal */}
            <RoleModal
                isOpen={isRoleModalOpen}
                onClose={handleCloseModal}
                mode={roleModalMode}
                role={currentRole}
            />
        </div>
    )
}
