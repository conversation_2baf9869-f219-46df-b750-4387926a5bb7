import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { X, Loader2 } from "lucide-react"
import { useTranslation } from "react-i18next"
import { useEffect, useState } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useDispatch, useSelector } from "react-redux"
import { AppDispatch, RootState } from "@/shared/store"
import { createOrUpdateRole } from "@/shared/store/slices/organizationSettingsSlice"
import { showToast } from "@/shared/utils/toastConfig"
import { useQuery } from "@apollo/client"
import { GET_PERMISSIONS_BY_ORG_ID } from "@/shared/graphQl/queries/organization"
import { PermissionGroup, RoleModalProps } from "@/shared/types/organization"
import { PermissionsSection } from "@/shared/components/PermissionsSection"

export const RoleModal = ({ isOpen, onClose, mode, role }: RoleModalProps) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const dispatch = useDispatch<AppDispatch>()

    const { orgId, submitting } = useSelector((state: RootState) => state.orgSettings)
    const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>({})
    const [roleName, setRoleName] = useState("")
    const [roleDescription, setRoleDescription] = useState("")
    const [permissionGroups, setPermissionGroups] = useState<any[]>([])
    const [formErrors, setFormErrors] = useState<{ name?: string }>({})

    // Fetch permissions directly with Apollo Client
    const { data: permissionsData, loading: loadingPermissions } = useQuery(GET_PERMISSIONS_BY_ORG_ID, {
        variables: { orgId: Number(orgId) },
        skip: !isOpen || !orgId,
        fetchPolicy: "network-only"
    });

    // Process permissions data when it's loaded
    useEffect(() => {
        if (permissionsData?.permissions) {
            const groups = processPermissionsData(permissionsData.permissions);
            setPermissionGroups(groups);

            // Initialize expanded sections
            const sections: Record<string, boolean> = {}
            groups.forEach((group: any) => {
                sections[group.id] = false
            })
        }
    }, [permissionsData])

    // Initialize form when modal opens or role changes
    useEffect(() => {
        if (role && mode === "edit") {
            setRoleName(role.title)
            setRoleDescription(role.description || "")

            const permissions: Record<string, boolean> = {}
            role.permissions.forEach((id: string) => {
                permissions[id] = true
            })
            setSelectedPermissions(permissions)
        } else {
            setRoleName("")
            setRoleDescription("")
            setSelectedPermissions({})
        }
        setFormErrors({})
    }, [role, mode, isOpen])

    // Helper functions for processing permissions
    const processPermissionsData = (permissions: PermissionGroup[]) => {
        return permissions.map((group: PermissionGroup) => {
            // Create a translated model name
            const modelTranslations: Record<string, string> = {
                role: t("roleManagement"),
                user: t("userManagement"),
                workspace: t("workspaceManagement"),
            }

            // Create a translated permission name
            const permissionTranslations: Record<string, string> = {
                add: t("add"),
                change: t("edit"),
                delete: t("delete"),
                view: t("view"),
            }

            // Process permissions to add translated names
            const processedPermissions = group.permissions.map((perm) => {
                // Extract action from codename (e.g., "add_role" -> "add")
                const [action, model] = perm.codename.split('_')
                let translationKey = model;
                if (model === 'workspace') {
                    translationKey = 'workspaceTitle';
                }
                return {
                    ...perm,
                    translated: `${permissionTranslations[action] || action} ${t(translationKey)}`
                }
            })

            return {
                id: group.model.toLowerCase(),
                title: modelTranslations[group.model.toLowerCase()] || group.model,
                permissions: processedPermissions
            }
        });
    }

    const validateForm = () => {
        const errors: { name?: string } = {};

        if (!roleName.trim()) {
            errors.name = t("roleNameRequired");
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    }

    const handleSubmit = () => {
        if (!validateForm() || !orgId) {
            return;
        }

        // Collect selected permission IDs
        const permissionIds = Object.entries(selectedPermissions)
            .filter(([, isSelected]) => isSelected)
            .map(([id]) => id)

        dispatch(createOrUpdateRole({
            orgId,
            roleId: String(role?.id),
            title: roleName,
            description: roleDescription,
            permissions: permissionIds,
            isEdit: mode === "edit"
        })).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                showToast.success(mode === "create" ? t("roleCreatedSuccess") : t("roleUpdatedSuccess"))
                onClose(true)
            }
        })
    }

    return (
        <Dialog open={isOpen} onOpenChange={() => onClose()}>
            <DialogContent className={`sm:max-w-[500px] [&>button]:hidden ${direction}`}>
                <DialogHeader>
                    <div className="flex items-center justify-between">
                        <DialogTitle className="text-xl">
                            {mode === "create" ? t("createNewRole") : t("editRole")}
                        </DialogTitle>
                        <Button variant="ghost" size="icon" onClick={() => onClose()} className="h-8 w-8">
                            <X size={18} />
                        </Button>
                    </div>
                    <DialogDescription className={direction}>
                        {mode === "create"
                            ? t("createRoleDescription")
                            : t("editRoleDescription")}
                    </DialogDescription>
                </DialogHeader>

                <ScrollArea className="max-h-[60vh] overflow-auto px-2">
                    <div className="space-y-6 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="role-name" className={`block ${direction}`}>
                                {t("roleName")} <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                dir={direction}
                                id="role-name"
                                placeholder={t("roleNamePlaceholder")}
                                className={`w-full ${formErrors.name ? 'border-red-500' : ''} no-border-focus`}
                                value={roleName}
                                onChange={(e) => setRoleName(e.target.value)}
                            />
                            {formErrors.name && (
                                <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="role-description" className={`block ${direction}`}>
                                {t("descrption")}
                            </Label>
                            <Textarea
                                dir={direction}
                                id="role-description"
                                placeholder={t("roleDescriptionPlaceholder")}
                                className="w-full no-border-focus"
                                value={roleDescription}
                                onChange={(e) => setRoleDescription(e.target.value)}
                            />
                        </div>

                        <PermissionsSection
                            groups={permissionGroups}
                            selectedPermissions={selectedPermissions}
                            onPermissionChange={setSelectedPermissions}
                            loading={loadingPermissions}
                        />
                    </div>
                </ScrollArea>

                <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" onClick={() => onClose()} className="rounded-full" disabled={submitting}>
                        {t("cancel")}
                    </Button>
                    <Button
                        className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-full"
                        onClick={handleSubmit}
                        disabled={loadingPermissions || submitting}
                    >
                        {submitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                        {mode === "create" ? t("createRole") : t("saveChanges")}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
