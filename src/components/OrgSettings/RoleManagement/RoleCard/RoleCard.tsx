import { <PERSON><PERSON> } from "@/components/ui/button"
import { Shield, Trash, Loader2 } from "lucide-react"
import { useTranslation } from "react-i18next"
import { useState } from "react"
import { useMutation } from "@apollo/client"
import { DELETE_ROLE } from "@/shared/graphQl/mutations/organization"
import { useSearchParams } from "react-router-dom"
import { DeleteAlert } from "./DeleteAlert"
import { showToast } from "@/shared/utils/toastConfig"
import { RoleCardProps } from "@/shared/types/organization"

export const RoleCard = ({ role, onEdit, onDelete }: RoleCardProps) => {
    const { t } = useTranslation()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

    const [deleteRole, { loading: isDeleting }] = useMutation(DELETE_ROLE, {
        onCompleted: () => {
            showToast.success(t("roleDeletedSuccess"))
            setIsDeleteDialogOpen(false)
            onDelete()
        },
        onError: (error) => {
            console.error("Error deleting role:", error)
            setIsDeleteDialogOpen(false)
        }
    })

    const handleDelete = () => {
        deleteRole({
            variables: {
                dataInput: {
                    orgId: Number(orgId),
                    roleId: Number(role.id)
                }
            }
        })
    }

    return (
        <>
            <div className="flex items-center justify-between rounded-lg border p-4 bg-white">
                <div className="flex items-center gap-4">
                    <Shield size={18} />
                    <div>
                        <div className="flex items-center gap-2">
                            <h3 className="font-medium">{role.title}</h3>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-md">
                                {t("usersCount", { count: role.users || role.usersCount || 0 })}
                            </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{role.description}</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button variant="ghost" size="sm" onClick={onEdit} className="rounded-full">
                        {t("edit")}
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 border-red-200 rounded-full"
                        onClick={() => setIsDeleteDialogOpen(true)}
                        disabled={isDeleting}
                    >
                        {isDeleting ? <Loader2 size={16} className="animate-spin" /> : <Trash size={16} />}
                    </Button>
                </div>
            </div>

            <DeleteAlert
                isOpen={isDeleteDialogOpen}
                onClose={() => setIsDeleteDialogOpen(false)}
                onConfirm={handleDelete}
                title={t("deleteRole")}
                description={t("deleteRoleConfirmation", { role: role.title })}
                isLoading={isDeleting}
            />
        </>
    )
}