import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { DeleteAlertProps } from "@/shared/types/organization";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next"

export const DeleteAlert: React.FC<DeleteAlertProps> = ({
    isOpen,
    onClose,
    onConfirm,
    title,
    description,
    isLoading = false,
}) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()

    return (
        <AlertDialog open={isOpen} onOpenChange={onClose}>
            <AlertDialogContent className={`${direction}`}>
                <AlertDialogHeader className={`${direction}`}>
                    <AlertDialogTitle>{title}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className={`gap-2 ${direction}`}>
                    <AlertDialogCancel onClick={onClose}>
                        {t('common.cancel')}
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={onConfirm}
                        className="bg-red-500 hover:bg-red-600"
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : null}
                        {t('common.delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
};