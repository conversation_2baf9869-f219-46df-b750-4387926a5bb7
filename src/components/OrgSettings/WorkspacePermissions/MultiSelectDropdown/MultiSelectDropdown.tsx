import { useState, useRef, useEffect } from 'react';
import { ChevronDown } from "lucide-react";
import { useTranslation } from "react-i18next";
import { CheckboxItem } from './CheckboxItem';
import { SearchInput } from '@/shared/components/SearchInput';
import { DirectionalScrollArea } from '@/components/Map/components/LayerSidebar/DirectionalScrollArea';

interface MultiSelectDropdownProps {
    options: { value: string; label: string }[];
    selectedValues: string[];
    onSelect: (selectedValues: string[]) => void;
    placeholder?: string;
    showSelectAll?: boolean;
    selectAllLabel?: string;
    showSearch?: boolean;
    className?: string;
}

export const MultiSelectDropdown = ({
    options,
    selectedValues,
    onSelect,
    placeholder = "Select...",
    showSelectAll = false,
    selectAllLabel = "Select All",
    showSearch = false,
    className = ""
}: MultiSelectDropdownProps) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Handle click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleCheckboxChange = (value: string, checked: boolean) => {
        // Handle "Select All" option
        if (value === "select-all") {
            if (checked) {
                // Select all options
                const allValues = options.map(option => option.value);
                onSelect(allValues);
            } else {
                // Deselect all
                onSelect([]);
            }
            return;
        }

        // Handle regular option selection
        const updatedValues = checked
            ? [...selectedValues, value]
            : selectedValues.filter((v) => v !== value);
        onSelect(updatedValues);
    };

    // Check if all options are selected
    const allSelected = options.length > 0 && selectedValues.length === options.length;

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    // Filter options based on search term
    const filteredOptions = options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort and group options: selected first, then unselected
    const selectedOptions = filteredOptions.filter(option =>
        selectedValues.includes(option.value)
    );
    const unselectedOptions = filteredOptions.filter(option =>
        !selectedValues.includes(option.value)
    );

    const selectedCount = selectedValues.length;

    return (
        <div className={`relative w-full`} ref={dropdownRef}>
            {/* Trigger Button */}
            <button
                onClick={toggleDropdown}
                className={`min-w-[12rem] w-full px-4 py-[6px] bg-white border border-slate-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-between no-border-focus ${className}`}
                style={{ textAlign: direction === 'rtl' ? 'right' : 'left' }}
            >
                <ChevronDown
                    className={`w-5 h-5 text-slate-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''
                        } ${direction === 'rtl' ? 'order-2' : 'order-1'}`}
                />
                <span className={`text-slate-700 font-medium ${direction === 'rtl' ? 'order-1' : 'order-2'}`}>
                    {selectedCount > 0
                        ? `${selectedCount} ${t("selected")}`
                        : placeholder}
                </span>
            </button>

            {/* Dropdown Menu */}
            {isOpen && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-slate-200 rounded-lg shadow-lg z-50 overflow-hidden min-w-full">
                    {/* Search Input */}
                    {showSearch && (
                        <div className="p-3 border-b border-slate-100">
                            <SearchInput
                                value={searchTerm}
                                onChange={setSearchTerm}
                            />
                        </div>
                    )}

                    <DirectionalScrollArea className="h-60">
                        {filteredOptions.length > 0 ? (
                            <>
                                {/* Select All Item */}
                                {showSelectAll && (
                                    <div className="border-b border-slate-100">
                                        <CheckboxItem
                                            label={selectAllLabel}
                                            checked={allSelected}
                                            onChange={() => handleCheckboxChange("select-all", !allSelected)}
                                        />
                                    </div>
                                )}

                                {/* Selected Items */}
                                {selectedOptions.map((option) => (
                                    <CheckboxItem
                                        key={option.value}
                                        label={option.label}
                                        checked={true}
                                        onChange={() => handleCheckboxChange(option.value, false)}
                                    />
                                ))}

                                {/* Separator if there are both selected and unselected items */}
                                {selectedOptions.length > 0 && unselectedOptions.length > 0 && (
                                    <div className="border-t border-slate-200 my-1"></div>
                                )}

                                {/* Unselected Items */}
                                {unselectedOptions.map((option) => (
                                    <CheckboxItem
                                        key={option.value}
                                        label={option.label}
                                        checked={false}
                                        onChange={() => handleCheckboxChange(option.value, true)}
                                    />
                                ))}
                            </>
                        ) : (
                            <div className="px-4 py-3 text-slate-500 text-center text-sm">
                                {searchTerm ? (t("noSearchResults") || "No results found") : (t("noOptions") || "No options available")}
                            </div>
                        )}
                    </DirectionalScrollArea>
                </div>
            )
            }
        </div >
    );
};