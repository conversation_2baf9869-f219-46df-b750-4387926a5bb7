import { useTranslation } from "react-i18next";

interface CheckboxItemProps {
    label: string;
    checked: boolean;
    onChange: () => void;
}

export const CheckboxItem: React.FC<CheckboxItemProps> = ({ label, checked, onChange }) => {
    const { i18n } = useTranslation();
    const direction = i18n.dir();
    return (
        <label dir={direction} className="flex items-center justify-between px-4 py-3 hover:bg-slate-50 cursor-pointer transition-colors duration-150 border-b border-slate-50 last:border-b-0 max-w-full">
            <div className="relative flex-shrink-0">
                <input
                    type="checkbox"
                    checked={checked}
                    onChange={onChange}
                    className="sr-only"
                />
                <div
                    className={`w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center text-white ${checked
                        ? 'border-[#5E58EE] bg-[#5E58EE]'
                        : 'hover:bg-transparent'
                        }`}
                >
                    {checked && (
                        <svg
                            className="w-3 h-3 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={3}
                                d="M5 13l4 4L19 7"
                            />
                        </svg>
                    )}
                </div>
            </div>
            <span className="text-slate-700 font-medium flex-1 mr-3 break-all">
                {label}
            </span>
        </label>
    );
};