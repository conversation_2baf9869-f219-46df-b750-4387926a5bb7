import { useState, useEffect } from "react";
import { Building, UserPlus, Mail, Trash, ChevronRight, ChevronLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { WorkspaceItem } from "./WorkspaceItem/WorkspaceItem";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/shared/store";
import { fetchWorkspaces } from "@/shared/store/slices/workspaceSlice";
import { useDebounce } from "@/shared/hooks/useDebounce";
import { showToast } from "@/shared/utils/toastConfig";
import { AddWorkspaceUserDialog } from "./AddWorkspaceUserDialog/AddWorkspaceUserDialog";
import { Badge } from "@/components/ui/badge";
import { MultiSelectDropdown } from "./MultiSelectDropdown/MultiSelectDropdown";
import {
  fetchWorkspaceUsers,
  fetchPermissions,
  setSelectedWorkspace,
  setAddWorkspaceUserDialogOpen,
  updateSelectedPermissions,
  updatePendingPermissionChanges,
  assignWorkspacePermissions
} from "@/shared/store/slices/organizationSettingsSlice";
import { mapActiveStatusToEnum, renderStatusBadge } from "@/shared/utils/statusBadges";
import { SearchInput } from "@/shared/components/SearchInput";

const WORKSPACES_PER_PAGE = 10;

export const WorkspacePermissions = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();

  const {
    orgId,
    selectedWorkspace,
    workspaceMembers,
    workspacePermissions,
    selectedPermissions,
    pendingPermissionChanges,
    isAddWorkspaceUserDialogOpen,
    loadingWorkspaceUsers,
    submitting
  } = useSelector((state: RootState) => state.orgSettings);

  const { workspaces, totalCount, loading } = useSelector((state: RootState) => state.workspace);

  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch workspaces when component mounts or search/page changes
  useEffect(() => {
    if (orgId) {
      dispatch(
        fetchWorkspaces({
          searchTerm: debouncedSearchTerm,
          offset: (currentPage - 1) * WORKSPACES_PER_PAGE,
          limit: WORKSPACES_PER_PAGE,
          orderBy: "-created",
          orgId: Number(orgId),
        })
      );
    }
  }, [debouncedSearchTerm, currentPage, orgId, dispatch]);

  // Fetch workspace permissions
  useEffect(() => {
    if (orgId) {
      dispatch(fetchPermissions(orgId));
    }
  }, [orgId, dispatch]);

  // Fetch workspace users when workspace is selected
  useEffect(() => {
    if (orgId && selectedWorkspace) {
      dispatch(fetchWorkspaceUsers({
        orgId,
        workspaceId: selectedWorkspace.id
      }));
    }
  }, [orgId, selectedWorkspace, dispatch]);

  // Process debounced permission changes
  useEffect(() => {
    const processPermissionChanges = async () => {
      const userIds = Object.keys(pendingPermissionChanges);

      if (userIds.length === 0 || !selectedWorkspace || !orgId) return;

      for (const userId of userIds) {
        const permissionIds = pendingPermissionChanges[userId];

        dispatch(assignWorkspacePermissions({
          orgId,
          workspaceId: selectedWorkspace.id,
          userIds: [userId],
          permissionIds: permissionIds || []
        }));
      }
    };

    if (Object.keys(pendingPermissionChanges).length > 0) {
      processPermissionChanges();
    }
  }, [pendingPermissionChanges, orgId, selectedWorkspace, dispatch]);

  // Reset page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  const handleWorkspaceSelect = (workspace: any) => {
    dispatch(setSelectedWorkspace(workspace));
  };

  const handleOpenAddUserDialog = () => {
    if (!selectedWorkspace) {
      showToast.warning(t("pleaseSelectWorkspace"));
      return;
    }
    dispatch(setAddWorkspaceUserDialogOpen(true));
  };

  const handleAddUsers = async (data: { userIds: string[]; permissionIds: string[] }) => {
    if (!selectedWorkspace || !orgId) {
      showToast.error(t("noWorkspaceSelected"));
      return;
    }

    dispatch(assignWorkspacePermissions({
      orgId,
      workspaceId: selectedWorkspace.id,
      userIds: data.userIds,
      permissionIds: data.permissionIds
    })).then((result) => {
      if (result.meta.requestStatus === 'fulfilled') {
        showToast.success(t("usersAddedSuccessfully"));
      }
    });
  };

  const handleRemoveUser = async (userId: string) => {
    if (!selectedWorkspace || !orgId) return;

    dispatch(assignWorkspacePermissions({
      orgId,
      workspaceId: selectedWorkspace.id,
      userIds: [userId],
      permissionIds: [] // Empty array to remove all permissions
    })).then((result) => {
      if (result.meta.requestStatus === 'fulfilled') {
        showToast.success(t("userRemovedSuccessfully"));
      }
    });
  };

  const handlePermissionChange = (userId: string, selectedValues: string[]) => {
    // Update the UI immediately
    dispatch(updateSelectedPermissions({ userId, permissions: selectedValues }));

    // Queue the change for debounced processing
    dispatch(updatePendingPermissionChanges({ userId, permissions: selectedValues }));
  };

  // Format permissions for the dropdown
  const permissionOptions = workspacePermissions?.map((permission: any) => ({
    value: permission.id,
    label: permission.title,
  })) || [];

  const totalPages = Math.ceil(totalCount / WORKSPACES_PER_PAGE);

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1);
      dispatch(setSelectedWorkspace(null));
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
      dispatch(setSelectedWorkspace(null));
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">{t("workspacePermissions")}</h2>
        <p className="text-muted-foreground">{t("workspacePermissionsDescription")}</p>
      </div>

      <div className="flex gap-6">
        <div className="w-1/4 bg-white border rounded-lg min-h-[400px]">
          <div className="p-4 border-b">
            <h3 className="font-bold text-lg mb-2">{t("workspaces")}</h3>
            <SearchInput
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder={t("search")}
              className="text-sm h-9"
            />
          </div>
          <div className="p-3 max-h-[500px] overflow-y-auto">
            {loading && workspaces.length === 0 ? (
              <div className="text-center py-4 text-gray-500">{t("loading")}</div>
            ) : workspaces.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {debouncedSearchTerm ? t("noSearchResults") : t("noWorkspaces")}
              </div>
            ) : (
              <>
                {workspaces.map((workspace: any) => (
                  <WorkspaceItem
                    key={workspace.id}
                    workspace={workspace}
                    isActive={selectedWorkspace?.id === workspace.id}
                    onClick={() => handleWorkspaceSelect(workspace)}
                  />
                ))}
                {totalPages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <Button
                      variant="outline"
                      onClick={handlePrevPage}
                      disabled={currentPage === 1 || loading}
                    >
                      <ChevronRight size={16} />
                    </Button>
                    <span>{t("page")} {currentPage} {t("of")} {totalPages}</span>
                    <Button
                      variant="outline"
                      onClick={handleNextPage}
                      disabled={currentPage === totalPages || loading}
                    >
                      <ChevronLeft size={16} />
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {selectedWorkspace ? (
          <div className="w-3/4 bg-white border rounded-lg p-6">
            <div className="border-b pb-4">
              <h3 className="text-lg font-semibold mb-1">{selectedWorkspace.name}</h3>
              <p className="text-sm text-gray-500">{t("manageWorkspaceUsers")}</p>
            </div>
            <div className="py-4 flex justify-between items-center border-b">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">
                  {t("users")} ({workspaceMembers?.length || 0})
                </h4>
              </div>
              <Button
                className="gap-2 bg-[#5E58EE] hover:bg-[#5E58EE] rounded-full"
                onClick={handleOpenAddUserDialog}
              >
                <UserPlus size={16} />
                <span>{t("addUser")}</span>
              </Button>
            </div>
            <div className="space-y-4 mt-4">
              {loadingWorkspaceUsers ? (
                <div className="text-center py-8 text-gray-500">{t("loading")}</div>
              ) : workspaceMembers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">{t("noUsersInWorkspace")}</div>
              ) : (
                workspaceMembers.map((member: any) => {
                  const status = mapActiveStatusToEnum(member?.activeStatus as any);
                  const isUpdatingPermissions = Object.keys(pendingPermissionChanges).includes(String(member.id));
                  const fullName = `${member.firstName || ''} ${member.lastName || ''}`.trim() || `User ${member.id}`;

                  return (
                    <div key={member.id} className="flex flex-col py-3 border-b">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{fullName}</span>
                              <span className="text-gray-600 bg-gray-100 px-3 py-1 rounded text-sm">
                                {member?.role?.title}
                              </span>
                              {renderStatusBadge(status, t)}
                            </div>
                            <div className="flex items-center text-sm text-gray-500 gap-1">
                              <Mail size={14} />
                              <span>{member.email || `user${member.id}@example.com`}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-3">
                          {/* MultiSelectDropdown for permissions */}
                          <div className="relative">
                            {isUpdatingPermissions && (
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                                <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
                              </div>
                            )}
                            <MultiSelectDropdown
                              options={permissionOptions}
                              selectedValues={selectedPermissions[member.id] || []}
                              onSelect={(selectedValues) =>
                                handlePermissionChange(member.id, selectedValues)
                              }
                              placeholder={t("selectPermissions")}
                              className="min-w-[12rem]"
                            />
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-500 border-red-200"
                            onClick={() => handleRemoveUser(member.id)}
                            disabled={submitting}
                          >
                            <Trash size={16} />
                            <span className="ml-2">{t("remove")}</span>
                          </Button>
                        </div>
                      </div>

                      {/* Display selected permissions */}
                      <div className="mt-3">
                        <h5 className="text-sm font-medium mb-2">{t("permissions")}:</h5>
                        <div className="flex flex-wrap gap-2">
                          {selectedPermissions[member.id]?.length > 0 ? (
                            selectedPermissions[member.id].map((permissionId) => {
                              const permission = permissionOptions.find(
                                (p: any) => p.value === permissionId
                              );
                              return (
                                <Badge
                                  key={permissionId}
                                  className="bg-blue-100 text-blue-800 hover:bg-blue-200"
                                >
                                  {permission?.label || permissionId}
                                </Badge>
                              );
                            })
                          ) : (
                            <span className="text-sm text-gray-500">
                              {t("noPermissions")}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })
              )}
            </div>
          </div>
        ) : (
          <div className="w-3/4 bg-white border rounded-lg p-8 flex items-center justify-center flex-col h-fit">
            <Building size={64} className="text-gray-300 mb-4" />
            <h3 className="text-xl font-medium mb-1">{t("selectWorkspacePrompt")}</h3>
            <p className="text-gray-500 text-sm">{t("selectWorkspaceDescription")}</p>
          </div>
        )}
      </div>

      {/* Add Workspace User Dialog */}
      <AddWorkspaceUserDialog
        isOpen={isAddWorkspaceUserDialogOpen}
        onAddUsers={handleAddUsers}
        workspace={selectedWorkspace as any}
      />
    </div>
  );
};