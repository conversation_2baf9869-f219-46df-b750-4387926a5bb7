import { cn } from "@/lib/utils"
import { WorkspaceItemProps } from "@/shared/types/organization"

export const WorkspaceItem = ({ workspace, isActive, onClick }: WorkspaceItemProps) => {
    return (
        <button
            key={workspace.id}
            onClick={onClick}
            className={cn(
                "w-full px-4 py-2 text-sm cursor-pointer rounded-full text-center box-border",
                isActive ? "bg-[#6366F1] text-white hover:bg-[#6366F1]" : "",
            )}
        >
            {workspace.name}
        </button>
    )
}