import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Loader2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useTranslation } from "react-i18next";
import { useQuery } from "@apollo/client";
import { WORKSPACE_USERS_QUERY } from "@/shared/graphQl/queries/organization";
import { showToast } from "@/shared/utils/toastConfig";
import { cn } from "@/lib/utils";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/shared/store";
import { setAddWorkspaceUserDialogOpen } from "@/shared/store/slices/organizationSettingsSlice";
import { AddWorkspaceUserDialogProps, User } from "@/shared/types/organization";
import { SearchInput } from "@/shared/components/SearchInput";
import { PermissionsSection } from "@/shared/components/PermissionsSection";

export const AddWorkspaceUserDialog = ({
    isOpen,
    onAddUsers,
    workspace,
}: AddWorkspaceUserDialogProps) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch<AppDispatch>();

    const { orgId, workspacePermissions, submitting } = useSelector((state: RootState) => state.orgSettings);

    const [searchQuery, setSearchQuery] = useState("");
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>({});

    // Fetch all users in the organization
    const { data: usersData, loading: usersLoading } = useQuery(
        WORKSPACE_USERS_QUERY,
        {
            variables: { orgId, workspaceId: workspace?.id, excludeIndividuals: true },
            skip: !isOpen || !orgId,
            fetchPolicy: "network-only",
        }
    );

    // Reset selections when dialog opens/closes
    useEffect(() => {
        if (!isOpen) {
            setSelectedUsers([]);
            setSearchQuery("");
            setSelectedPermissions({});
        }
    }, [isOpen]);

    const users = usersData?.users?.data || [];

    // Filter users based on search query
    const filteredUsers = users
        .filter((user: User) => {
            const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
            const email = user.email.toLowerCase();
            const search = searchQuery.toLowerCase();

            return fullName.includes(search) || email.includes(search);
        })
        .map((user: User) => ({
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            email: user.email,
            role: user.role?.title || "",
        }));

    const handleToggleUser = (userId: string) => {
        setSelectedUsers((prev) => {
            if (prev.includes(userId)) {
                return prev.filter((id) => id !== userId);
            } else {
                return [...prev, userId];
            }
        });
    };

    const handleCloseAddUserModal = () => {
        dispatch(setAddWorkspaceUserDialogOpen(false));
    };

    const handleAddUsers = () => {
        try {
            if (selectedUsers.length === 0) {
                showToast.warning(t("pleaseSelectAtLeastOneUser"));
                return;
            }

            // Collect selected permission IDs
            const selectedPermissionIds = Object.keys(selectedPermissions).filter(
                (permissionId) => selectedPermissions[permissionId]
            );

            // Format the data for the backend
            const dataToSend = {
                userIds: selectedUsers,
                permissionIds: selectedPermissionIds,
            };
            onAddUsers(dataToSend);
        } catch (error) {
            console.error("Error adding users:", error);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleCloseAddUserModal}>
            <DialogContent className={`sm:max-w-[500px] [&>button]:hidden ${direction}`} >
                <DialogHeader dir={direction}>
                    <div className="flex items-center justify-between" >
                        <DialogTitle className="text-xl" >{t("selectFromExistingUsers")}</DialogTitle>
                        <Button variant="ghost" size="icon" onClick={handleCloseAddUserModal} className="h-8 w-8">
                            <X size={18} />
                        </Button>
                    </div>
                    <DialogDescription className={direction}>
                        {t("selectUsersToAddToWorkspace", { workspaceName: workspace?.name ? workspace?.name : "" })}
                    </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                    <SearchInput
                        value={searchQuery}
                        onChange={setSearchQuery}
                        placeholder={t("searchUsers")}
                        className="mb-4"
                    />

                    <div className="space-y-4 max-h-[200px] overflow-y-auto">
                        {usersLoading ? (
                            <div className="text-center py-4 flex items-center justify-center">
                                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                                <span>{t("loading")}</span>
                            </div>
                        ) : filteredUsers.length > 0 ? (
                            filteredUsers.map((user: any) => (
                                <div key={user.id} className="flex items-center justify-between py-2">
                                    <div className="flex items-center gap-3">
                                        <Checkbox
                                            id={`user-${user.id}`}
                                            checked={selectedUsers.includes(user.id)}
                                            onCheckedChange={() => handleToggleUser(user.id)}
                                        />
                                        <div>
                                            <Label htmlFor={`user-${user.id}`} className="font-medium cursor-pointer">
                                                {user.name}
                                            </Label>
                                            <div className="text-sm text-gray-500">{user.email}</div>
                                            {user.role && (
                                                <div className="text-xs text-gray-400">{user.role}</div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                {searchQuery ? t("noUsersMatchSearch") : t("noUsersAvailableToAdd")}
                            </div>
                        )}
                    </div>

                    {selectedUsers.length > 0 && (
                        <>
                            <Separator className="my-4" />
                            <PermissionsSection
                                groups={[{
                                    id: "workspace",
                                    title: t("workspacePermissions"),
                                    permissions: workspacePermissions
                                }]}
                                selectedPermissions={selectedPermissions}
                                onPermissionChange={setSelectedPermissions}
                            />
                        </>
                    )}
                </div>

                <div className="flex justify-between mt-4">
                    <Button
                        className={cn(
                            "bg-[#6366F1] hover:bg-[#5355d1]",
                            selectedUsers.length === 0 && "opacity-50 cursor-not-allowed"
                        )}
                        disabled={selectedUsers.length === 0 || submitting}
                        onClick={handleAddUsers}
                    >
                        {submitting ? (
                            <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                {t("adding")}
                            </>
                        ) : (
                            `${t("add")} (${selectedUsers.length})`
                        )}
                    </Button>
                    <Button variant="outline" onClick={handleCloseAddUserModal}>
                        {t("cancel")}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
