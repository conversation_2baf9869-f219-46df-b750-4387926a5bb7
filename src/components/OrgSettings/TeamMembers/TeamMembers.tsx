import { Button } from "@/components/ui/button"
import { UserPlus, ChevronLeft, ChevronRight, Loader2, X } from "lucide-react"
import { MemberCard } from "./MemberCard/MemberCard"
import { useTranslation } from "react-i18next"
import { useEffect } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AddUserForm } from "./AddUserForm/AddUserForm"
import { useDispatch, useSelector } from "react-redux"
import { AppDispatch, RootState } from "@/shared/store/store"
import {
    fetchUsers,
    setUserSearchTerm,
    setUserCurrentPage,
    setAddUserDialogOpen,
    fetchRoles
} from "@/shared/store/slices/organizationSettingsSlice"
import { mapActiveStatusToEnum } from "@/shared/utils/statusBadges"
import { SearchInput } from "@/shared/components/SearchInput"

export const TeamMembers = () => {
    const { t, i18n } = useTranslation()
    const direction = i18n.dir();
    const dispatch = useDispatch<AppDispatch>()

    const {
        orgId,
        users,
        totalUsers,
        userSearchTerm,
        userCurrentPage,
        usersPerPage,
        loadingUsers,
        isAddUserDialogOpen,
        roles
    } = useSelector((state: RootState) => state.orgSettings)

    // Fetch users when component mounts or when search/page changes
    useEffect(() => {
        if (orgId) {
            dispatch(fetchUsers({
                orgId,
                page: userCurrentPage,
                limit: usersPerPage,
                searchTerm: userSearchTerm
            }))
            dispatch(fetchRoles(orgId))
        }
    }, [orgId, userCurrentPage, userSearchTerm, usersPerPage])

    const handleNextPage = () => {
        const totalPages = Math.ceil(totalUsers / usersPerPage)
        if (userCurrentPage < totalPages) {
            dispatch(setUserCurrentPage(userCurrentPage + 1))
        }
    }

    const handlePrevPage = () => {
        if (userCurrentPage > 1) {
            dispatch(setUserCurrentPage(userCurrentPage - 1))
        }
    }

    const handleOpenAddUserDialog = () => {
        dispatch(setAddUserDialogOpen(true))
    }

    const handleCloseAddUserDialog = () => {
        dispatch(setAddUserDialogOpen(false))
    }

    const totalPages = Math.ceil(totalUsers / usersPerPage)


    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">{t("teamMembers")}</h2>
                    <div className="flex items-center gap-2">
                        <p className="text-muted-foreground">{t("teamMembersDescription")}</p>
                        <span className="bg-[#6366F1]/10 text-[#6366F1] px-2 rounded-full text-sm">
                            {t("totalUsers", { count: totalUsers })}
                        </span>
                    </div>
                </div>
                <Dialog open={isAddUserDialogOpen} onOpenChange={(open) => {
                    if (open) {
                        dispatch(setAddUserDialogOpen(true));
                    } else {
                        dispatch(setAddUserDialogOpen(false));
                    }
                }}>
                    <DialogTrigger asChild dir={direction}>
                        <Button className="gap-2 bg-[#5E58EE] hover:bg-[#5E58EE] rounded-full" onClick={handleOpenAddUserDialog}>
                            <UserPlus size={16} />
                            <span>{t("inviteUser")}</span>
                        </Button>
                    </DialogTrigger>
                    <DialogContent className={` [&>button]:hidden ${direction}`}>
                        <DialogHeader>
                            <div className="flex items-center justify-between">
                                <DialogTitle>{t("inviteUser")}</DialogTitle>
                                <Button variant="ghost" size="icon" onClick={handleCloseAddUserDialog} className="h-8 w-8">
                                    <X size={18} />
                                </Button>
                            </div>
                        </DialogHeader>
                        <AddUserForm />
                    </DialogContent>
                </Dialog>
            </div>

            <SearchInput
                value={userSearchTerm}
                onChange={(value) => dispatch(setUserSearchTerm(value))}
                placeholder={t("searchTeamMembers")}
            />
            {loadingUsers && !users.length ? (
                <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                </div>
            ) : (
                <>
                    <div className="space-y-4">
                        {users.length > 0 ? (
                            users.map((user: any) => (
                                <MemberCard
                                    key={user.id}
                                    userId={user.id}
                                    name={`${user.firstName} ${user.lastName}`}
                                    email={user.email}
                                    roleId={user.role.id}
                                    roleTitle={user.role.title}
                                    phone={user.phone}
                                    status={mapActiveStatusToEnum(user.activeStatus)}
                                    roles={roles}
                                />
                            ))
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                {userSearchTerm ? t("noSearchResults") : t("noUsers")}
                            </div>
                        )}
                    </div>

                    {totalPages > 1 && (
                        <div className="flex items-center justify-between pt-4 border-t">
                            <div className="text-sm text-gray-500">
                                {t("showing")} {Math.min((userCurrentPage - 1) * usersPerPage + 1, totalUsers)}-
                                {Math.min(userCurrentPage * usersPerPage, totalUsers)} {t("of")} {totalUsers} {t("users")}
                            </div>
                            <div className={`flex items-center space-x-2 ${direction}`}>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handlePrevPage}
                                    disabled={userCurrentPage === 1 || loadingUsers}
                                >
                                    <ChevronRight size={16} />
                                </Button>
                                <div className="text-sm">
                                    {t("page")} {userCurrentPage} {t("of")} {totalPages}
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleNextPage}
                                    disabled={userCurrentPage === totalPages || loadingUsers}
                                >
                                    <ChevronLeft size={16} />
                                </Button>
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    )
}

