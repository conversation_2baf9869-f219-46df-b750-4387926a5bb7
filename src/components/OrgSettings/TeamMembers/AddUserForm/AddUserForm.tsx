import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useTranslation } from "react-i18next"
import { useState } from "react"
import { showToast } from "@/shared/utils/toastConfig"
import { useDispatch, useSelector } from "react-redux"
import { AppDispatch, RootState } from "@/shared/store/store"
import { addUser, setAddUserDialogOpen } from "@/shared/store/slices/organizationSettingsSlice"

export const AddUserForm = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const dispatch = useDispatch<AppDispatch>()

    const { orgId, roles, submitting } = useSelector((state: RootState) => state.orgSettings)

    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        roleId: roles[0]?.id || ""
    })

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()

        if (!orgId) {
            showToast.error(t("organizationNotSelected"))
            return
        }

        dispatch(addUser({
            orgId,
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            phone: formData.phone,
            roleId: String(formData.roleId)
        })).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                showToast.success(t("userInvitedSuccessfully"))
                dispatch(setAddUserDialogOpen(false))
            }
        })
    }
    const handleCancel = () => {
        dispatch(setAddUserDialogOpen(false))
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-4" dir={direction}>
            <div className={`grid grid-cols-2 gap-4 ${direction}`}>
                <div className="space-y-2">
                    <Label htmlFor="firstName">{t("firstName")}</Label>
                    <Input
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleChange}
                        required
                        className="no-border-focus"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="lastName">{t("lastName")}</Label>
                    <Input
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleChange}
                        required
                        className="no-border-focus"
                    />
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="email">{t("email")}</Label>
                <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="no-border-focus"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="phone">{t("phone")}</Label>
                <div className={`flex ltr`}>
                    <div className="flex items-center px-3 border rounded-l-md bg-gray-50">
                        <span className="text-sm text-gray-500">+966</span>
                    </div>
                    <Input
                        id="phone"
                        type="tel"
                        name="phone"
                        className="rounded-l-none no-border-focus"
                        placeholder="5XXXXXXXX"
                        value={formData.phone.replace(/^\+966/, '')}
                        onChange={(e) => setFormData({
                            ...formData,
                            phone: `+966${e.target.value.replace(/\D/g, '').slice(0, 9)}`
                        })}
                    />
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="roleId">{t("role")}</Label>
                <select
                    id="roleId"
                    name="roleId"
                    className="w-full border rounded-md p-3 bg-white text-sm focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    value={formData.roleId}
                    onChange={handleChange}
                    required
                >
                    {roles.map(role => (
                        <option key={role.id} value={role.id}>
                            {role.title}
                        </option>
                    ))}
                </select>
            </div>

            <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={handleCancel}>
                    {t("cancel")}
                </Button>
                <Button
                    type="submit"
                    className="bg-[#5E58EE] hover:bg-[#5E58EE]"
                    disabled={submitting}
                >
                    {submitting ? t("sending") : t("sendInvitation")}
                </Button>
            </div>
        </form>
    )
}
