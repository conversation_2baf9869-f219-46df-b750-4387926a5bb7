import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Mail, MoreVertical, Phone } from "lucide-react"
import { useTranslation } from "react-i18next"
import { useState } from "react"
import { showToast } from "@/shared/utils/toastConfig"
import { UserStatus } from "@/shared/types/userStatus"
import { DeleteAlert } from "../../RoleManagement/RoleCard/DeleteAlert"
import { renderStatusBadge } from "@/shared/utils/statusBadges"
import { useDispatch, useSelector } from "react-redux"
import { AppDispatch, RootState } from "@/shared/store/store"
import { changeUserRole, changeUserStatus } from "@/shared/store/slices/organizationSettingsSlice"
import { MemberCardProps } from "@/shared/types/organization"

export const MemberCard = ({
    userId,
    name,
    email,
    phone,
    roleId,
    status,
    roles
}: MemberCardProps) => {
    const { t } = useTranslation()
    const dispatch = useDispatch<AppDispatch>()
    const [selectedRoleId, setSelectedRoleId] = useState(roleId)
    const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false)
    const [dropdownOpen, setDropdownOpen] = useState(false)

    const { orgId, submitting } = useSelector((state: RootState) => state.orgSettings)

    const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newRoleId = e.target.value
        setSelectedRoleId(newRoleId)

        if (orgId) {
            dispatch(changeUserRole({
                orgId,
                userId,
                roleId: newRoleId
            })).then((result) => {
                if (result.meta.requestStatus === 'fulfilled') {
                    showToast.success(t("userRoleUpdatedSuccessfully"))
                }
            })
        }
    }

    const handleRemoveUser = () => {
        if (orgId) {
            dispatch(changeUserStatus({
                orgId,
                userId,
                activeStatus: UserStatus.DELETED
            })).then((result) => {
                if (result.meta.requestStatus === 'fulfilled') {
                    showToast.success(t("userRemovedSuccessfully"))
                    setDropdownOpen(false)
                    setIsRemoveDialogOpen(false)
                }
            })
        }
    }

    const handleActivateUser = () => {
        if (orgId) {
            dispatch(changeUserStatus({
                orgId,
                userId,
                activeStatus: UserStatus.ACTIVE
            })).then((result) => {
                if (result.meta.requestStatus === 'fulfilled') {
                    showToast.success(t("userActivatedSuccessfully"))
                    setDropdownOpen(false)
                }
            })
        }
    }

    const handleDeactivateUser = () => {
        if (orgId) {
            dispatch(changeUserStatus({
                orgId,
                userId,
                activeStatus: UserStatus.INACTIVE
            })).then((result) => {
                if (result.meta.requestStatus === 'fulfilled') {
                    showToast.success(t("userDeactivatedSuccessfully"))
                    setDropdownOpen(false)
                }
            })
        }
    }

    return (
        <>
            <div className="flex items-center justify-between rounded-lg border p-4 bg-white">
                <div className="flex items-center gap-4">
                    <div>
                        <h3 className="font-medium">{name}</h3>
                        <div className="flex flex-col gap-1">
                            <div className="flex items-center text-sm text-gray-500 gap-1">
                                <Mail size={14} />
                                <span>{email}</span>
                            </div>
                            {phone && (
                                <div className="flex items-center text-sm text-gray-500 gap-1">
                                    <Phone size={14} />
                                    <span>{phone}</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-4">
                    {renderStatusBadge(status, t)}

                    <div className="flex flex-col">
                        <select
                            className="border rounded-md px-3 py-1.5 bg-white text-sm focus:ring-2 focus:ring-[#6366F1]"
                            value={selectedRoleId}
                            onChange={handleRoleChange}
                            disabled={submitting || status !== UserStatus.ACTIVE}
                        >
                            {roles.map(role => (
                                <option key={role.id} value={role.id}>
                                    {role.title}
                                </option>
                            ))}
                        </select>
                    </div>

                    <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                        <DropdownMenuTrigger className="no-border-focus" asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreVertical size={16} />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {status === UserStatus.INACTIVE && (
                                <DropdownMenuItem onClick={handleActivateUser}>
                                    {t("activateAccount")}
                                </DropdownMenuItem>
                            )}

                            {status === UserStatus.ACTIVE && (
                                <DropdownMenuItem onClick={handleDeactivateUser}>
                                    {t("deactivateAccount")}
                                </DropdownMenuItem>
                            )}

                            <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => {
                                    setDropdownOpen(false)
                                    setIsRemoveDialogOpen(true)
                                }}
                            >
                                {t("removeUser")}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            <DeleteAlert
                isOpen={isRemoveDialogOpen}
                onClose={() => setIsRemoveDialogOpen(false)}
                onConfirm={handleRemoveUser}
                title={t("removeUser")}
                description={t("removeUserConfirmation", { name })}
                isLoading={submitting}
            />
        </>
    )
}