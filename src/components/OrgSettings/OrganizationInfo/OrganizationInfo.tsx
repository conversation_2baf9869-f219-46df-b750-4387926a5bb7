import { useTranslation } from "react-i18next"
import { User } from "lucide-react"
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager"

export const OrganizationInfo = () => {
    const { t } = useTranslation()
    const { selectedOrg, loading } = useOrganizationManager()

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-bold">{t("organizationInfo")}</h2>
                <p className="text-muted-foreground">{t("manageBasicOrgInfo")}</p>
            </div>

            <div className="space-y-8 rounded-lg  p-6 bg-white border-t">
                <div className="flex justify-between items-center max-w-[500px]">
                    <div>
                        <h3 className="text-lg font-medium">{t("organizationName")}</h3>
                        <p className="text-gray-700 text-xl mt-2">{loading ? t("loading") : selectedOrg?.settings?.name}</p>
                    </div>
                    {/* <Button variant="outline" className="rounded-full">{t("edit")}</Button> */}
                </div>

                <div className="pt-6">
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="text-lg font-medium">{t("users")}</h3>
                            <div className="flex items-center mt-2 text-gray-700">
                                <User size={16} />
                                <span className="text-xl mx-2">{t("totalUsers", { count: selectedOrg?.usersCount })}</span>
                                <span className="text-green-500 px-4 py-1 bg-green-100 rounded-full text-xs me-2">
                                    {t("activeUsers", { count: selectedOrg?.usersCount })}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}