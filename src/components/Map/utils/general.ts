import GeoJSON from 'ol/format/GeoJSON'

export const getPolygonExtent = (geojsonObject: any) => {
  const geoJsonFeature = new GeoJSON().readFeature(geojsonObject, {
    dataProjection: 'EPSG:4326',
    featureProjection: 'EPSG:3857',
  }) as any
  const extent = geoJsonFeature.getGeometry().getExtent()
  return extent
}

export function extractFilename(url: string) {
  try {
    const urlObj = new URL(url, 'http://example.com')
    const pathname = urlObj.pathname
    const lastSegment = pathname.split('/').pop()
    return lastSegment ? lastSegment.split('#')[0].split('?')[0] : ''
  } catch {
    // If URL constructor fails, handle it as a simple path
    const lastSegment = url.split('/').pop()
    return lastSegment ? lastSegment.split('#')[0].split('?')[0] : ''
  }
}
