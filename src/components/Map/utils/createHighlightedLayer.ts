import { createVectorLayer } from '../core/layers/createVectorLayer'
import VectorSource from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { MAP_STYLES } from '../constants/styles'

const isEPSG4326Coordinates = (coordinates: number[]) => {
  return Math.abs(coordinates[0]) <= 180 && Math.abs(coordinates[1]) <= 90
}

const getFirstCoordinate = (geometry: any): number[] => {
  const type = geometry.type.toLowerCase()

  if (type === 'point') {
    return geometry.coordinates
  } else if (type === 'polygon') {
    return geometry.coordinates[0][0]
  } else if (type === 'multipolygon') {
    return geometry.coordinates[0][0][0]
  } else if (type === 'linestring') {
    return geometry.coordinates[0]
  } else if (type === 'multilinestring') {
    return geometry.coordinates[0][0]
  }

  return geometry.coordinates[0]
}

const detectProjection = (geojsonObject: any): string => {
  const geometries = geojsonObject.geometry.geometries || [
    geojsonObject.geometry,
  ]
  const firstCoord = getFirstCoordinate(geometries[0])
  return isEPSG4326Coordinates(firstCoord) ? 'EPSG:4326' : 'EPSG:3857'
}
export const WMS_HIGHLIGHTED_FEATURE_LAYER = 'wms-highlighted-feature'

export const createHighlightedVectorLayer = ({
  geojsonObject,
}: {
  geojsonObject: any
}) => {
  const sourceProjection = detectProjection(geojsonObject)

  const vectorSource = new VectorSource({
    features: new GeoJSON({
      dataProjection: sourceProjection,
      featureProjection: 'EPSG:3857',
    }).readFeatures(geojsonObject),
  })

  const highlightedFeatureLayer = createVectorLayer(vectorSource, {
    properties: {
      name: WMS_HIGHLIGHTED_FEATURE_LAYER,
      type: 'highlight',
    },
    zIndex: 1000,
  })

  highlightedFeatureLayer.setStyle(MAP_STYLES.highlight)
  return highlightedFeatureLayer
}
