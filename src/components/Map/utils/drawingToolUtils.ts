export function createCircleAsPolygon(
  center: [number, number],
  radius: number,
  numPoints: number
) {
  const coordinates = []
  const angleStep = (2 * Math.PI) / numPoints

  for (let i = 0; i < numPoints; i++) {
    const angle = i * angleStep
    const x = center[0] + radius * Math.cos(angle)
    const y = center[1] + radius * Math.sin(angle)
    coordinates.push([x, y])
  }

  coordinates.push(coordinates[0])

  return coordinates
}
