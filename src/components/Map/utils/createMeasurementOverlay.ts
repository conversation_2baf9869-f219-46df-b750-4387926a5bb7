import { Overlay } from 'ol'
import { createOverlay } from './createOverlay'

export function createMeasurementOverlay(
  coordinate: number[],
  onDelete?: () => void
): Overlay {
  const container = document.createElement('div')
  container.className = 'ol-tooltip ol-tooltip-measure'

  const measureText = document.createElement('span')
  container.appendChild(measureText)

  const deleteButton = document.createElement('div')
  deleteButton.className = 'delete-measure'
  deleteButton.innerHTML = '×'
  deleteButton.addEventListener('click', (e) => {
    e.stopPropagation()
    onDelete?.()
  })
  container.appendChild(deleteButton)

  const overlay = createOverlay(container, coordinate, {
    offset: [0, -15],
    positioning: 'bottom-center',
    autoPan: false,
  })

  return overlay
}

export function updateMeasurementContent(
  overlay: Overlay,
  distance: number
): void {
  const element = overlay.getElement()
  if (!element) return

  const measureText = element.querySelector('span')
  if (!measureText) return

  const formattedDistance =
    distance > 1000
      ? `${(distance / 1000).toFixed(2)} km`
      : `${Math.round(distance)} m`

  measureText.innerHTML = formattedDistance
}
