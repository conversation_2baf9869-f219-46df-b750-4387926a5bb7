/**
 * Extracts opacity value from a layer's SLD (Styled Layer Descriptor)
 * @param layer - The layer object containing slds array
 * @returns The opacity value from the matching SLD, or 1 as default
 */
export function extractOpacityFromLayer(layer: any): number {
  // Find the SLD that matches the layer key
  const matchingSld = layer.slds?.find(
    (sld: any) => sld.title === layer.layerKey
  )

  // Extract opacity from the matching SLD's featureStyle
  const opacity = matchingSld?.featureStyle?.opacity

  // Return the opacity or default to 1 if not found
  return typeof opacity === 'number' ? opacity : 1
}
