/**
 * Checks if all WMS layers are loaded
 * @param mapManager The map manager instance
 * @returns Promise that resolves when all layers are loaded
 */
export const waitForLayersToLoad = async (mapManager: any): Promise<void> => {
  // Get all WMS layers
  const wmsLayers = Object.values(mapManager.layers).filter(
    (layer: any) => layer.get('type') === 'wms'
  )

  if (wmsLayers.length === 0) {
    return Promise.resolve()
  }

  // Wait for a short time to allow layers to load
  return new Promise((resolve) => {
    // Force a render
    mapManager.map.renderSync()

    // Wait a bit more to ensure everything is rendered
    setTimeout(resolve, 500)
  })
}

/**
 * Retrieves the last saved thumbnail from localStorage
 * @param storageKey The key used to store the thumbnail
 * @returns The last saved thumbnail or null if none exists
 */
export const getLastSavedThumbnail = (
  storageKey: string = 'mapThumbnail'
): string | null => {
  return localStorage.getItem(storageKey)
}

/**
 * Retrieves the last saved view state from localStorage
 * @param storageKey The key used to store the view state
 * @returns The last saved view state or null if none exists
 */
export const getLastSavedViewState = (
  storageKey: string = 'mapThumbnail'
): any | null => {
  const viewStateStr = localStorage.getItem(`${storageKey}_viewState`)
  if (viewStateStr) {
    try {
      return JSON.parse(viewStateStr)
    } catch (error) {
      console.error('Failed to parse view state from localStorage:', error)
      return null
    }
  }
  return null
}

/**
 * Clears thumbnail and view state from localStorage
 * @param storageKey The key used to store the data
 */
export const clearSavedThumbnailData = (
  storageKey: string = 'mapThumbnail'
): void => {
  localStorage.removeItem(storageKey)
  localStorage.removeItem(`${storageKey}_viewState`)
}

/**
 * Checks if there's a saved thumbnail in localStorage
 * @param storageKey The key used to store the thumbnail
 * @returns Boolean indicating if thumbnail exists
 */
export const hasSavedThumbnail = (
  storageKey: string = 'mapThumbnail'
): boolean => {
  return localStorage.getItem(storageKey) !== null
}
