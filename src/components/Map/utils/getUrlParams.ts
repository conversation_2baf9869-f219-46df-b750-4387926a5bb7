export function getUrlParams(
  url: string = window.location.href,
  pathTemplate: string
): {
  queryParams: Record<string, string>
  pathParams: Record<string, string>
} {
  const urlObj = new URL(url)

  // Extract query parameters
  const queryParams: Record<string, string> = {}
  urlObj.searchParams.forEach((value, key) => {
    queryParams[key] = value
  })

  // Extract path parameters if pathTemplate is provided
  const pathParams: Record<string, string> = {}
  if (pathTemplate) {
    const pathRegex = new RegExp(
      '^' + pathTemplate.replace(/:\w+/g, '([^/]+)') + '$'
    )
    const matches = urlObj.pathname.match(pathRegex)

    if (matches) {
      const paramNames = (pathTemplate.match(/:\w+/g) || []).map((param) =>
        param.slice(1)
      )
      paramNames.forEach((param, index) => {
        pathParams[param] = matches[index + 1]
      })
    }
  }

  return { queryParams, pathParams }
}
