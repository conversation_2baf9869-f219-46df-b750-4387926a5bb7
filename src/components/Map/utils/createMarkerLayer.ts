import { Feature } from 'ol'
import { Point } from 'ol/geom'
import VectorSource from 'ol/source/Vector'
import { Style, Icon } from 'ol/style'
import { createVectorLayer } from '../core/layers/createVectorLayer'
import markerIcon from '@/assets/marker.png'

export const SEARCH_MARKER_LAYER = 'search-marker-layer'

export const createMarkerLayer = ({ coordinate }: { coordinate: number[] }) => {
  const markerFeature = new Feature({
    geometry: new Point(coordinate),
  })

  const markerStyle = new Style({
    image: new Icon({
      src: markerIcon,
      anchor: [0.5, 1],
      scale: 0.3,
    }),
  })

  const vectorSource = new VectorSource({
    features: [markerFeature],
  }) as VectorSource<Feature<any>>

  const markerLayer = createVectorLayer(vectorSource, {
    style: markerStyle,
    properties: {
      name: SEARCH_MARKER_LAYER,
      type: 'search',
    },
    zIndex: 100,
  })

  return markerLayer
}
