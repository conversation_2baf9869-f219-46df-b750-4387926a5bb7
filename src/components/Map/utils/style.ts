import { Style } from 'ol/style'
import { MAP_STYLES } from '../constants/styles'
import { GeometryType } from '../types/map.types'

export const getStyleForGeometry = (type: GeometryType): Style => {
  switch (type) {
    case 'Point':
      return MAP_STYLES.point
    case 'LineString':
      return MAP_STYLES.line
    case 'Polygon':
      return MAP_STYLES.polygon
    case 'Circle':
      return MAP_STYLES.circle
    case 'Rectangle':
      return MAP_STYLES.rectangle
    default:
      return new Style()
  }
}
