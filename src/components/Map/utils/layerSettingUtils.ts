/**
 * Extracts properties that have type "number" or ["number", ...] from a JSON schema
 * @param {Object} schema - The JSON schema to analyze
 * @return {string[]} - Array of property names that have numeric types
 */
export function extractNumberProperties(schema: any): string[] {
  if (!schema || !schema.properties) {
    return []
  }

  // Use a Set to automatically handle duplicates
  const numberProperties = new Set<string>()

  // Iterate through all properties in the schema
  for (const [propName, propDef] of Object.entries(schema.properties)) {
    // Check if property type is "number" or includes "number" in an array of types
    if (
      (propDef as any).type === 'number' ||
      (propDef as any).type === 'integer' ||
      (Array.isArray((propDef as any).type) &&
        ((propDef as any).type.includes('number') ||
          (propDef as any).type.includes('integer')))
    ) {
      numberProperties.add(propName)
    }

    // Also check in definitions if there's a reference
    if ((propDef as any).$ref && schema.definitions) {
      const refPath = (propDef as any).$ref.split('/')
      const defName = refPath[refPath.length - 1]

      if (schema.definitions[defName]) {
        const def = schema.definitions[defName]
        if (
          def.type === 'number' ||
          def.type === 'integer' ||
          (Array.isArray(def.type) &&
            (def.type.includes('number') || def.type.includes('integer')))
        ) {
          numberProperties.add(propName)
        }
      }
    }
  }

  // Convert Set back to array before returning
  return Array.from(numberProperties)
}

// Define color ranges based on the selected gradient - only first and last color, without hash
export const getColorRangeFromGradient = (gradient: string) => {
  const gradientMap: Record<string, string[]> = {
    'yellow-pink': ['F9D423', 'FF4E50'],
    'peach-purple': ['F9D423', 'A241E9'],
    'lime-blue': ['C5F634', '1D98F2'],
    'yellow-teal': ['F9D423', '02AABD'],
  }

  return gradientMap[gradient] || gradientMap['yellow-pink']
}

// Helper function to determine gradient name from color range
export const getGradientFromColorRange = (colorRange: string[]): string => {
  if (!colorRange || colorRange.length < 2) return 'yellow-pink'

  const startColor = colorRange[0]?.toLowerCase()
  const endColor = colorRange[1]?.toLowerCase()

  // Map color pairs to gradient names based on the gradientMap
  if (startColor === 'f9d423' && endColor === 'ff4e50') return 'yellow-pink'
  if (startColor === 'f9d423' && endColor === 'a241e9') return 'peach-purple'
  if (startColor === 'c5f634' && endColor === '1d98f2') return 'lime-blue'
  if (startColor === 'f9d423' && endColor === '02aabd') return 'yellow-teal'

  // Check in reverse order too
  if (endColor === 'f9d423' && startColor === 'ff4e50') return 'yellow-pink'
  if (endColor === 'f9d423' && startColor === 'a241e9') return 'peach-purple'
  if (endColor === 'c5f634' && startColor === '1d98f2') return 'lime-blue'
  if (endColor === 'f9d423' && startColor === '02aabd') return 'yellow-teal'

  return 'yellow-pink'
}

export const calculatePropertyStats = (data: any[], property: string) => {
  if (!data || !property || data.length === 0) {
    return {
      min: 0,
      max: 1,
      step: 0.1,
    }
  }

  // Extract all valid numeric values for the property
  const values = data
    .map((item) => parseFloat(item.properties?.[property]))
    .filter((value) => !isNaN(value))

  if (values.length === 0) {
    return {
      min: 0,
      max: 1,
      step: 0.1,
    }
  }

  // Calculate min and max
  const min = Math.min(...values)
  const max = Math.max(...values)

  // Calculate a reasonable step based on the range
  const range = max - min
  let step = 1

  if (range > 100) {
    step = 10
  } else if (range > 10) {
    step = 1
  } else if (range > 1) {
    step = 0.1
  } else {
    step = 1
  }

  return {
    min,
    max,
    step,
  }
}

// Helper function to get heatmap-specific WMS configuration
export const getHeatmapLayerConfig = () => {
  return {
    maxRetries: 4,
    retryDelay: 30000,
    timeout: 60000,
  }
}
