import { get as getProjection } from 'ol/proj'
import { transform } from 'ol/proj'
import { DEFAULT_PROJECTION, WGS84_PROJECTION } from '../constants/defaults'

export const transformCoordinates = (
  coords: number[],
  from = WGS84_PROJECTION,
  to = DEFAULT_PROJECTION
) => {
  return transform(coords, from, to)
}

export const getProjectionByCode = (code: string) => {
  return getProjection(code)
}
