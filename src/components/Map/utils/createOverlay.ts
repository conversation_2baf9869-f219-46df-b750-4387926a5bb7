import { Overlay } from 'ol'

export function createOverlay(
  element: HTMLElement,
  coordinate: number[],
  options: {
    offset?: [number, number]
    stopEvent?: boolean
    positioning?:
      | 'center-left'
      | 'center-right'
      | 'top-left'
      | 'top-right'
      | 'bottom-center'
    autoPan?: boolean
    autoPanAnimation?: {
      duration?: number
    }
  } = {}
): Overlay {
  const {
    offset = [10, 0],
    positioning = 'center-center',
    stopEvent = true,
    autoPan = true,
    autoPanAnimation = { duration: 250 },
  } = options

  return new Overlay({
    element,
    position: coordinate,
    positioning,
    offset,
    stopEvent,
    autoPan: autoPan
      ? {
          animation: autoPanAnimation,
        }
      : undefined,
  })
}
