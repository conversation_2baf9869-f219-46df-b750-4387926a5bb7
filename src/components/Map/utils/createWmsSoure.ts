import { getFullToken } from '@/shared/utils/generals'
import ImageW<PERSON> from 'ol/source/ImageWMS'
import { getUrlParams } from './getUrlParams'
import { Routes } from '@/shared/utils/routes'
import WmsRequestManager from '../services/WmsRequestManager'

const wmsBaseUrl = import.meta.env.VITE_WMS_BASE_URL
const baseUrl = new URL(import.meta.env.VITE_BASE_URL || '')
const SCHEME = baseUrl.protocol === 'http:' ? 'http' : 'https'
const IMAGE_FORMAT = 'image/vnd.jpeg-png8'
const FETCH_METHOD = 'GET'

// Helper: Create headers for WMS requests
const createWmsHeaders = (): Headers => {
  const headers = new Headers()
  const token = getFullToken()
  if (token) {
    headers.append('Authorization', token)
  }
  return headers
}

// Updated: Load image with abort functionality using WmsRequestManager
const loadImageWithAbort = (
  image: any,
  src: string,
  controller: AbortController
): Promise<void> => {
  return fetch(src, {
    headers: createWmsHeaders(),
    method: FETCH_METHOD,
    mode: 'cors',
    signal: controller.signal,
  })
    .then((response) => {
      // Check if response contains WMS exception
      const contentType = response.headers.get('Content-Type')
      if (contentType?.includes('xml')) {
        return response.text().then((text) => {
          if (text.includes('ServiceException')) {
            throw new Error('Layer still publishing or not ready')
          }
          return response.blob()
        })
      }
      return response.blob()
    })
    .then((blob) => {
      let imgUrl = URL.createObjectURL(blob)

      // Ensure the image URL matches the required scheme
      if (SCHEME === 'https') {
        imgUrl = imgUrl.replace(/^http:/, 'https:')
      }

      const imageWrapper = image.getImage() as HTMLImageElement
      imageWrapper.crossOrigin = 'anonymous'
      imageWrapper.src = imgUrl
    })
    .catch((error) => {
      // Only log and rethrow if it's not an AbortError
      if (error.name === 'AbortError') {
        // Silently handle abort errors
        return Promise.resolve()
      }
      console.error('Error loading WMS image:', error)
      throw error
    })
}

// Updated: Retry load image using WmsRequestManager
const retryLoadImage = (
  image: any,
  src: string,
  layerName: string,
  isRetry = false
): Promise<void> => {
  const requestManager = WmsRequestManager.getInstance()

  // Only start a new request if this is not a retry
  const controller = isRetry
    ? requestManager.getRequestState(layerName)?.controller ||
      new AbortController()
    : requestManager.startRequest(layerName)

  return loadImageWithAbort(image, src, controller as any)
    .then(() => {
      requestManager.completeRequest(layerName, true)
    })
    .catch((error) => {
      if (error.message.includes('Layer still publishing')) {
        return requestManager
          .handleRetry(layerName, error.message)
          .then(() => retryLoadImage(image, src, layerName, true))
          .catch((retryError) => {
            if (retryError.includes('Max retries')) {
              requestManager.completeRequest(layerName, false, retryError)
              throw new Error(retryError)
            }
          })
      }

      requestManager.completeRequest(layerName, false, error.message)
      throw error
    })
}

// Factory function: Create WMS source with loading callbacks
export function createWmsSource({
  wmsLayerName,
  wmsLayerId,
  cqlFilter,
  onLoading,
  onLoaded,
  color,
  customStyle,
}: {
  wmsLayerName: string
  wmsLayerId?: number
  cqlFilter?: string
  onLoading?: () => void
  onLoaded?: () => void
  color?: string
  customStyle?: string
}) {
  if (!wmsLayerName) return null

  const handleLoading = () => {
    onLoading?.()
  }

  const handleLoaded = () => {
    onLoaded?.()
  }

  const { queryParams, pathParams } = getUrlParams(
    window.location.href,
    `/${Routes.mapWorkspace}`
  )
  const orgId = queryParams.orgId
  const workspaceId = pathParams.workspaceId

  const params = {
    LAYERS: wmsLayerName.includes(',')
      ? wmsLayerName.split(',')
      : [wmsLayerName],
    ORGANIZATION: 16,
    FORMAT: IMAGE_FORMAT,
    FORMAT_OPTIONS: 'antialias:none',
    TIME: new Date().toISOString(),
    ENV: `color:${color}`,
    STYLES: customStyle
      ? customStyle
      : wmsLayerName.includes(',')
        ? wmsLayerName.split(',')
        : [wmsLayerName],
    workspace_id: workspaceId,
    organization: orgId,
  } as any

  if (cqlFilter) {
    params.CQL_FILTER = `layer_id='${wmsLayerId}' AND (${cqlFilter})`
  }

  return new ImageWMS({
    url: wmsBaseUrl,
    params,
    crossOrigin: 'anonymous',
    imageLoadFunction: (image, src) => {
      handleLoading()
      retryLoadImage(image, src, wmsLayerName).finally(() => {
        handleLoaded()
      })
    },
  })
}
