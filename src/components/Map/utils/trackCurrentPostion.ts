import { Feature } from 'ol'
import { Fill, Stroke, Style } from 'ol/style'
import { Circle as CircleStyle } from 'ol/style'

export const positionFeature = new Feature()
positionFeature.setStyle([
  new Style({
    image: new CircleStyle({
      radius: 15,
      fill: new Fill({
        color: 'rgba(51, 153, 204, 0.5)',
      }),
    }),
  }),
  new Style({
    image: new CircleStyle({
      radius: 6,
      fill: new Fill({
        color: '#3399CC',
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2,
      }),
    }),
  }),
])
