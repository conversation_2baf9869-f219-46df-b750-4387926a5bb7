import { Map } from 'ol'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'

export type MapInstance = Map
export type GeometryType =
  | 'Point'
  | 'LineString'
  | 'Polygon'
  | 'Circle'
  | 'Rectangle'

export interface WmsLayerConfig {
  key: string
  id?: number
}

export interface BaseMapConfig {
  label: string
  title: string
  icon: string
  visible: boolean
}

export interface DrawingOptions {
  type: GeometryType
  onComplete?: (feature: Feature) => void
  autoDeactivate?: boolean
}
export interface MapFeature extends Feature<Geometry> {
  properties?: Record<string, any>
}
