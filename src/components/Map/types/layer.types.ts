import { Layer } from 'ol/layer'
import VectorSource from 'ol/source/Vector'
import { ImageWMS } from 'ol/source'
import { DrawType } from '@/shared/store/slices/mapSlice'
import { Feature } from 'ol'

export type InteractionType =
  | 'wmsClick'
  | 'wmsHover'
  | 'draw'
  | 'modify'
  | 'snap'
  | 'translate'
  | 'select'
export interface CreateInteractionOptions {
  interactionType: InteractionType
  source: VectorSource | ImageWMS
  layer: Layer
  drawType?: DrawType
  onComplete?: (feature: Feature) => void
  autoDeactivate?: boolean
}
