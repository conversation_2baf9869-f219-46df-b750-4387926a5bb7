import { useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { useMapManager } from './hooks/useMapManager';
import { useWmsFeatureHandling } from './hooks/useWmsFeatureHandling';
import { useWmsLayerSetup } from './hooks/useWmsLayerSetup';
import { MapComponents } from './components/MainUIMap/MainUIMap';
import { LoadingOverlay } from './components/LoadingOverlay/LoadingOverlay';
import MapManager from './core/MapManager';
import { useFeatureOverlay } from './hooks/useFeatureOverlay';
import { FeatureInfoAlert } from './components/FeatureInfoAlert/FeatureInfoAlert';

export const MapComponent = () => {
    const mapContainerRef = useRef<HTMLDivElement>(null)
    const mapManager = useMapManager(mapContainerRef.current)
    const overlayRef = useFeatureOverlay(mapManager)
    const { activeDrawType } = useSelector((state: RootState) => state.map);
    const selectedWorkspace = useSelector((state: RootState) => state.workspace.selectedWorkspace)

    // Ensure the document title and localStorage are in sync with the selected workspace
    useEffect(() => {
        if (selectedWorkspace) {
            const savedTitle = localStorage.getItem('currentWorkspaceTitle');

            // Update title if it's different from the current workspace name
            if (savedTitle !== selectedWorkspace.name) {
                document.title = selectedWorkspace.name;
                localStorage.setItem('currentWorkspaceTitle', selectedWorkspace.name);
            }
        }
    }, [selectedWorkspace]);

    const {
        featureState,
        isFeatureLoading,
        handlePin,
        handleUnpin,
        handleNextFeature,
        handlePreviousFeature,
    } = useWmsFeatureHandling(mapManager, mapContainerRef)

    useWmsLayerSetup(mapManager as MapManager);

    return (
        <div className="relative w-full">
            <div ref={mapContainerRef} className="h-[calc(100vh-74px)] w-full">
                <LoadingOverlay isFeatureLoading={(isFeatureLoading && !activeDrawType)} />
                {mapManager && <MapComponents.MainUI />}
            </div>
            <div ref={overlayRef} className="absolute pointer-events-none">
                {featureState.feature && (
                    <div className="pointer-events-auto">
                        <FeatureInfoAlert
                            selectedFeature={featureState.feature}
                            isPinned={featureState.isPinned}
                            position={featureState.position}
                            hasMultiple={featureState.hasMultiple}
                            currentIndex={featureState.currentIndex}
                            totalFeatures={featureState.totalFeatures}
                            onPin={handlePin}
                            onClose={handleUnpin}
                            onNext={featureState.hasMultiple ? handleNextFeature : undefined}
                            onPrevious={featureState.hasMultiple ? handlePreviousFeature : undefined}
                        />
                    </div>
                )}
            </div>
        </div>
    )
}