import { Style, Circle as CircleStyle, Fill, Stroke } from 'ol/style'

export const MAP_STYLES = {
  point: new Style({
    image: new CircleStyle({
      radius: 7,
      fill: new Fill({
        color: 'rgba(255, 0, 0, 0.7)',
      }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 2,
      }),
    }),
  }),
  line: new Style({
    stroke: new Stroke({
      color: 'green',
      width: 2,
    }),
  }),
  polygon: new Style({
    stroke: new Stroke({
      color: 'blue',
      width: 3,
    }),
    fill: new Fill({
      color: 'rgba(0, 0, 255, 0.1)',
    }),
  }),
  highlight: new Style({
    stroke: new Stroke({
      color: '#39FF14',
      width: 3,
    }),
    fill: new Fill({
      color: 'rgba(57, 255, 20, 0.2)',
    }),
    image: new CircleStyle({
      radius: 4,
      fill: new Fill({
        color: 'rgba(57, 255, 20, 0.2)',
      }),
      stroke: new Stroke({
        color: '#39FF14',
        width: 1,
      }),
    }),
  }),
  circle: new Style({
    stroke: new Stroke({
      color: 'purple',
      width: 2,
    }),
    fill: new Fill({
      color: 'rgba(128, 0, 128, 0.1)',
    }),
  }),
  rectangle: new Style({
    stroke: new Stroke({
      color: 'orange',
      width: 2,
    }),
    fill: new Fill({
      color: 'rgba(255, 165, 0, 0.1)',
    }),
  }),
}
