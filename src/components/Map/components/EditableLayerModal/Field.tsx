import { Input } from '@/components/ui/input';
import { FieldType } from '@/shared/utils/recordHelpers';
import { useState, useEffect, useMemo } from 'react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Upload, X } from 'lucide-react';
import { isImageField } from '@/shared/utils/tabelrecordHelper';
import { SignedUrlService } from '@/shared/graphQl/services/signedUrl.service';
import { showToast } from '@/shared/utils/toastConfig';
import { useTranslation } from 'react-i18next';

interface FieldProps {
    field: {
        key: string;
        label: string;
        type: FieldType;
        required?: boolean;
        enumValues?: string[];
        isArray?: boolean;
    };
    value: string | number | boolean | readonly string[] | undefined | any;
    isEditMode: boolean;
    onChange: (key: string, value: string | string[], fieldType: FieldType) => void;
    error?: string;
}

export const FieldInput: React.FC<FieldProps> = ({ field, value, isEditMode, onChange, error }) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const [showOtherInput, setShowOtherInput] = useState(false);
    const [customValue, setCustomValue] = useState('');
    const UploadSignedUrlFile = new SignedUrlService();

    useEffect(() => {
        if (field.enumValues && !field.enumValues.includes(String(value)) && value) {
            setShowOtherInput(true);
            setCustomValue(String(value));
        }
    }, [value, field.enumValues]);

    const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (!files) return;

        try {
            const fileList = Array.from(files);
            const uploadPromises = fileList.map(file => UploadSignedUrlFile.uploadViaSignedUrl(file));
            const uploadedUrls = await Promise.all(uploadPromises);

            if (field.isArray) {
                const currentUrls = Array.isArray(value) ? value : value ? [value] : [];
                const updatedUrls = [...currentUrls, ...uploadedUrls.filter(url => url)];
                onChange(field.key, updatedUrls, field.type);
            } else {
                onChange(field.key, uploadedUrls[0], field.type);
            }

            showToast.success(t('alerts.success.imageUploaded'));
        } catch (error) {
            console.error('Error uploading images:', error);
            showToast.error(t('alerts.error.imageUpload'));
        }
    };

    const removeImage = (indexToRemove: number) => {
        if (field.isArray && Array.isArray(value)) {
            const updatedUrls = value.filter((_, index) => index !== indexToRemove);
            onChange(field.key, updatedUrls, field.type);
        } else {
            onChange(field.key, '', field.type);
        }
    };
    const handleSelectChange = (selectedValue: string) => {
        setShowOtherInput(selectedValue === 'other');
        if (selectedValue === 'other') {
            onChange(field.key, '', field.type);
        } else {
            onChange(field.key, field.isArray ? JSON.stringify([selectedValue]) : selectedValue, field.type);
        }
    };

    const handleCustomInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        setCustomValue(inputValue);
        onChange(field.key, field.isArray ? JSON.stringify([inputValue]) : inputValue, field.type);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        if (field.isArray) {
            // Send as array directly
            onChange(field.key, [inputValue], field.type);
        } else {
            onChange(field.key, inputValue, field.type);
        }
    };

    // Parse and format display value
    const displayValue = useMemo(() => {
        if (field.isArray) {
            if (Array.isArray(value)) {
                return value;
            }
            if (typeof value === 'string') {
                try {
                    const parsed = JSON.parse(value);
                    return Array.isArray(parsed) ? parsed : [value];
                } catch {
                    return [value];
                }
            }
            return [];
        }
        return value?.toString() || '';
    }, [value, field.isArray]);

    return (
        <div key={field.key} className="space-y-2">
            <label className={`block text-sm font-medium text-gray-700 ${direction}`}>
                {field.label}
                {field.required && <span className="text-red-500 mr-1">*</span>}
                {field.isArray && <span className="text-sm text-gray-500">({t('field.array')})</span>}
            </label>
            {isEditMode ? (
                <div className="space-y-2">
                    {isImageField(field.key) ?
                        (
                            <>
                                <label
                                    className={`flex items-center justify-center p-4 border-2 border-dashed rounded-md cursor-pointer
                                ${error ? 'border-red-500' : 'border-gray-300'} hover:border-[#5E58EE]`}
                                >
                                    <input
                                        type="file"
                                        className="hidden"
                                        accept="image/*"
                                        multiple={field.isArray}
                                        onChange={handleImageUpload}
                                        required={field.required}
                                    />
                                    <div className="text-center">
                                        <Upload className="mx-auto h-4 w-4 text-gray-400" />
                                        <span className="mt-2 block text-sm font-medium text-gray-900">
                                            {field.isArray ? t('multipleImageUpload') : t('imageUpload')}
                                        </span>
                                    </div>
                                </label>
                                {value && (
                                    <div className="grid grid-cols-3 gap-2 mt-2">
                                        {(field.isArray ? Array.isArray(value) ? value : [value] : [value]).map((img, index) => (
                                            <div key={index} className="relative group">
                                                <img
                                                    src={img}
                                                    alt={`Uploaded ${index + 1}`}
                                                    className="w-full h-24 object-cover rounded-md"
                                                />
                                                {isEditMode && (
                                                    <button
                                                        onClick={() => removeImage(index)}
                                                        className="absolute top-1 rtl:right-1 ltr:left-1 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            field.type === 'enum' && field.enumValues ? (
                                <>
                                    <Select
                                        dir={direction}
                                        required={field.required}
                                        value={displayValue}
                                        onValueChange={handleSelectChange}
                                    >
                                        <SelectTrigger className={`w-full ${direction} ${error ? 'border-red-500' : ''} no-border-focus`}>
                                            <SelectValue placeholder={t('selectValue')} />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem disabled value="select-value"> {t('selectValue')}</SelectItem>
                                            {field.enumValues?.filter(value => value.trim() !== '')
                                                .map((option) => (
                                                    <SelectItem key={option} value={option}>
                                                        {option}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                    {showOtherInput && (
                                        <Input
                                            required={field.required}
                                            type="text"
                                            value={customValue}
                                            onChange={handleCustomInputChange}
                                            placeholder={t('field.customValue')}
                                            className={`no-border-focus ${direction} mt-2 ${error && !customValue ? 'border-red-500' : ''}`}
                                        />
                                    )}
                                </>
                            ) : field.type === 'boolean' ? (
                                <Select
                                    dir={direction}
                                    required={field.required}
                                    value={String(displayValue)}
                                    onValueChange={(value) => onChange(field.key, value, field.type)}
                                >
                                    <SelectTrigger className={`w-full ${direction} ${error ? 'border-red-500' : ''} no-border-focus`}>
                                        <SelectValue placeholder={t('selectValue')} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="true">true</SelectItem>
                                        <SelectItem value="false">false</SelectItem>
                                    </SelectContent>
                                </Select>
                            ) : (
                                <Input
                                    required={field.required}
                                    type={field.type === 'integer' || field.type === 'float' ? 'number' : field.type === 'date' ? 'date' : 'text'}
                                    value={displayValue}
                                    onChange={handleInputChange}
                                    placeholder={field.label}
                                    className={`no-border-focus ${direction} ${error ? 'border-red-500' : ''}`}
                                    step={field.type === 'float' ? '0.01' : '1'}
                                />
                            )
                        )}
                    {error && (
                        <p className={`text-red-500 text-sm ${direction}`}>{error}</p>
                    )}
                </div>
            ) : (
                <div className={`text-sm text-gray-800 bg-gray-100 rounded-md p-2 ${direction} overflow-y-auto`}>
                    {isImageField(field.key) ? (
                        <div className="grid grid-cols-3 gap-2">
                            {(field.isArray ? Array.isArray(displayValue) ? displayValue : [displayValue] : [displayValue])
                                .filter(img => img)
                                .map((img, index) => (
                                    <img
                                        key={index}
                                        src={img}
                                        alt={`${field.label} ${index + 1}`}
                                        className="w-full h-24 object-cover rounded-md"
                                    />
                                ))}
                        </div>
                    ) : (
                        displayValue || '—'
                    )}
                </div>
            )}
        </div>
    );
};