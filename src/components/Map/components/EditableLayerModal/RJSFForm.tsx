import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Pencil, X } from 'lucide-react';
import { useState, useEffect } from 'react';
import { CustomThemedForm } from '@/components/RJSF/theme/ThemedForm';
import { useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { SignedUrlService } from '@/shared/graphQl/services/signedUrl.service';
import { showToast } from '@/shared/utils/toastConfig';
import { useTranslation } from 'react-i18next';
import { LayerMetadata } from '@/shared/store/slices/mapSlice';

interface EditableLayerModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (data: Record<string, any>) => void;
    mode: 'add' | 'edit' | 'view';
    schema?: any;
    uiSchema?: any;
    initialData?: Record<string, any>;
    loading?: boolean
    selectedLayer?: LayerMetadata
}

export const EditableLayerModal: React.FC<EditableLayerModalProps> = ({
    isOpen,
    onClose,
    onSave,
    mode,
    initialData = {},
    loading,
    selectedLayer
}) => {
    const [isEditMode, setIsEditMode] = useState(mode === 'add' || mode === 'edit');
    const [formData, setFormData] = useState(initialData);
    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const isReadOnly = selectedLayer?.readOnly ?? selectedMainLayer?.readOnly;
    const UploadSignedUrlFile = new SignedUrlService();
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    // Reset form data when loading is true
    useEffect(() => {
        if (loading) {
            setFormData({});
        } else {
            setFormData(initialData);
        }
    }, [loading, initialData]);

    const handleSubmit = ({ formData }: { formData: any }) => {
        onSave(formData);
        if (mode !== 'add') {
            setIsEditMode(false);
        }
    };

    const getModalTitle = () => {
        switch (mode) {
            case 'add':
                return t('modal.addRecord');
            case 'edit':
                return t('modal.editLayer');
            default:
                return t('modal.viewLayer');
        }
    };

    // Handle file changes
    const onFileChange = async (file: File) => {
        try {
            return await UploadSignedUrlFile.uploadViaSignedUrl(file);
        }
        catch (error) {
            console.error('Error uploading file:', error);
            showToast.error(t('workspace.fileUpload.alerts.uploadError'));
        }
    };

    if (loading) {
        return <div>{t('loading')}</div>
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose} >
            <DialogContent dir={direction} className={`sm:max-w-[600px] max-h-[80vh] flex flex-col gap-6 [&>button]:hidden ${direction}`}>
                <DialogHeader className={`flex-row items-center ${direction}`}>
                    <div className="flex justify-between items-center mb-4 w-full">
                        <div className="flex items-center gap-2">
                            <DialogTitle className="rtl:text-right ltr:text-left">{getModalTitle()}</DialogTitle>
                            {mode !== 'add' && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() => setIsEditMode((prev) => !prev)}
                                    disabled={isReadOnly}
                                >
                                    <Pencil className="h-5 w-5" />
                                </Button>
                            )}
                        </div>
                        <Button variant="ghost" size="icon" onClick={onClose}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto">
                    <CustomThemedForm
                        schema={selectedLayer?.jsonSchema ?? selectedMainLayer?.jsonSchema}
                        uiSchema={selectedLayer?.webUiJsonSchema ?? selectedMainLayer?.webUiJsonSchema ?? {}}
                        formData={formData}
                        onSubmit={handleSubmit}
                        disabled={!isEditMode}
                        formContext={{ onFileChange }}
                    >
                        <div />
                    </CustomThemedForm>

                    {isEditMode && (
                        <DialogFooter className="flex gap-4 ltr mt-4">
                            <Button variant="outline" onClick={onClose} className="text-gray-600 py-2 px-8 rounded-full">
                                {t('common.cancel')}
                            </Button>
                            <Button type="submit" form="root_form" className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-full">
                                {t('common.save')}
                            </Button>
                        </DialogFooter>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};
