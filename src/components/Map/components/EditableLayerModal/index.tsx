import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { Skeleton } from '@/components/Skeleton/Skeleton';
import { Pencil } from 'lucide-react';
import { FieldType, processFieldValue } from '@/shared/utils/recordHelpers';
import { FieldInput } from './Field';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '@/shared/store';

interface EditableLayerModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (data: Record<string, string | number>) => void;
    mode: 'add' | 'edit' | 'view';
    initialData?: Record<string, string | number>;
    fields: {
        key: string;
        label: string;
        type: FieldType
        required?: boolean;
    }[];
    loading?: boolean;
}

export const EditableLayerModal: React.FC<EditableLayerModalProps> = ({
    isOpen,
    onClose,
    onSave,
    mode,
    initialData = {},
    fields,
}) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();

    // Always keep edit mode active when adding new records
    const [isEditMode, setIsEditMode] = useState(true);
    const [formData, setFormData] = useState<Record<string, string | number>>(() => {
        const initialFormData = { ...initialData };
        fields.forEach((field) => {
            if (!(field.key in initialFormData)) {
                initialFormData[field.key] = '';
            }
        });
        return initialFormData;
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const isReadOnly = selectedMainLayer?.readOnly;

    const validateForm = () => {
        const newErrors: Record<string, string> = {};
        fields.forEach((field) => {
            const value = formData[field.key];
            if (field.required) {
                if (field.type === 'boolean') {
                    if (value === undefined || value === null) {
                        newErrors[field.key] = `${field.label} - ${t('field.required')}`;
                    }
                } else {
                    if (!value || value === '' || value === 'other') {
                        newErrors[field.key] = `${field.label} - ${t('field.required')}`;
                    }
                }
            }
        });
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleSave = () => {
        if (validateForm()) {
            // Add null values for fields that are not in the form
            const completeFormData = { ...formData };
            Object.entries(selectedMainLayer?.schema?.properties || {}).forEach(([key, value]: [string, any]) => {
                const types = Array.isArray(value.type) ? value.type : [value.type];
                if (types.length === 1 && types[0] === 'null') {
                    completeFormData[key] = null as any;
                }
            });

            onSave(completeFormData);
            if (mode !== 'add') {
                setIsEditMode(false);
            }
        }
    };

    useEffect(() => {
        if (mode !== 'add') setFormData(initialData);
    }, [initialData]);

    // Keep form in edit mode for 'add' mode
    useEffect(() => {
        if (mode === 'add') {
            setIsEditMode(true);
        }
        else setIsEditMode(mode === 'edit');
    }, [mode]);

    const handleChange = (key: string, value: string | any, fieldType: FieldType) => {
        const processedValue = processFieldValue(value, fieldType);
        setFormData((prev: any) => ({ ...prev, [key]: processedValue }));

        if (processedValue && errors[key]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[key];
                return newErrors;
            });
        }
    };

    const getModalTitle = () => {
        switch (mode) {
            case 'add':
                return t('modal.addRecord');
            case 'edit':
                return t('modal.editLayer');
            default:
                return t('modal.viewLayer');
        }
    };
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className={`sm:max-w-[600px] max-h-[60vh] flex flex-col gap-6 [&>button]:hidden ${direction}`}>
                <DialogHeader className={`flex-row items-center ${direction}`}>
                    <DialogTitle className={direction}>{getModalTitle()}</DialogTitle>
                    {mode !== 'add' && (
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => setIsEditMode((prev) => !prev)}
                            disabled={isReadOnly}
                        >
                            <Pencil className="h-5 w-5" />
                        </Button>
                    )}
                </DialogHeader>
                <div className="border-t" />
                <ScrollArea className={`flex-1 max-h-[40vh] overflow-y-auto rounded-md p-4 bg-white ${direction}`}>
                    {fields.length === 0 ? (
                        <div className="grid grid-cols-2 gap-4">
                            {[1, 2, 3, 4, 5, 6].map((index) => (
                                <div key={index} className="space-y-2">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-10 w-full rounded-md" />
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="grid grid-cols-2 gap-4">
                            {fields.map((field) => (
                                <FieldInput
                                    key={field.key}
                                    field={field}
                                    value={formData[field.key]}
                                    isEditMode={isEditMode}
                                    onChange={handleChange}
                                    error={errors[field.key]}
                                />
                            ))}
                        </div>
                    )}
                </ScrollArea>

                {isEditMode && (
                    <>
                        <div className="border-t" />
                        <DialogFooter className="flex gap-4 ltr">
                            <Button variant="outline" onClick={onClose} className="text-gray-600 py-2 px-8 rounded-full">
                                {t('common.cancel')}
                            </Button>
                            <Button onClick={handleSave} className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-full">
                                {t('common.save')}
                            </Button>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    );
};
