import { useState } from 'react'
import {
    DndContext,
    DragEndEvent,
    useSensor,
    useSensors,
    PointerSensor,
} from '@dnd-kit/core'
import {
    restrictToVerticalAxis,
    restrictToParentElement
} from '@dnd-kit/modifiers'
import { DraggableWrapper } from '../DraggableWrapper/DraggableWrapper'
import { MapLegendCard } from './MapLegendCard'

export const MapLegend = () => {
    const [position, setPosition] = useState({
        x: 0,
        y: Math.max(window.innerHeight * 0.65, 100)
    });

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 1,
            },
        })
    );

    const handleDragEnd = (event: DragEndEvent) => {
        const { delta } = event;
        setPosition(prev => {
            let newY = prev.y + delta.y;
            // Restrict the card within the screen height
            if (newY < 0) newY = 0;
            if (newY > window.innerHeight) newY = window.innerHeight;
            return {
                ...prev,
                y: newY,
            };
        });
    };

    return (
        <div className="fixed right-[20rem] top-[5rem] bottom-[1rem] w-64">
            <DndContext
                sensors={sensors}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis, restrictToParentElement]}
            >
                <DraggableWrapper
                    id="draggable-legend"
                    position={position}
                >
                    <MapLegendCard />
                </DraggableWrapper>
            </DndContext>
        </div>
    );
};



