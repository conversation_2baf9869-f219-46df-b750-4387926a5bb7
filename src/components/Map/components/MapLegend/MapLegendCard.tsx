import { useState } from 'react'
import { X, GripVertical } from 'lucide-react'
import { Card, CardHeader, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useDispatch, useSelector } from 'react-redux'
import { toggleLegend } from '@/shared/store/slices/mapSlice'
import { RootState } from '@/shared/store'
import { useTranslation } from 'react-i18next'

export const MapLegendCard = () => {
    const { i18n } = useTranslation()
    const direction = i18n.dir()
    const dispatch = useDispatch();
    const mainLayers = useSelector((state: RootState) => state.map.backendLayers)
    const [isHovered, setIsHovered] = useState(false)

    return (
        <Card
            id="map-legend-card"
            className="w-full cursor-grab active:cursor-grabbing shadow-lg"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <CardHeader className="flex flex-row items-center justify-between px-4 py-1">
                <span className={`${direction} font-medium`}>مفتاح الخريطة</span>
                <div className="flex items-center gap-2">
                    {isHovered && (
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-10 absolute top-[-10px] rtl:left-1/2  ltr:right-1/2 transform -translate-x-1/2 bg-white rounded-md shadow-md p-0"
                        >
                            <GripVertical
                                className="h-4 w-4 transform rotate-90 text-gray-600"
                            />
                        </Button>
                    )}
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => dispatch(toggleLegend())}
                    >
                        <X className="h-4 w-4" />
                    </Button>
                </div>
            </CardHeader>
            <div className="border-t mt-2 mb-4" />
            <CardContent className="p-4 pt-0 overflow-y-auto max-h-[10rem]">
                <div className="flex flex-col gap-4" dir={direction}>
                    {mainLayers.map((layer, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <div className={`w-5 h-5 rounded-sm`} style={{ backgroundColor: layer.color }} />
                            <span className="text-sm font-medium">{layer.title}</span>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    )
}
