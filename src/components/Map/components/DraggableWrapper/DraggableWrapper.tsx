import { useDraggable } from "@dnd-kit/core";

interface DraggableWrapperProps {
    id: string;
    children: React.ReactNode;
    position: { x: number; y: number };
}

export const DraggableWrapper = ({ id, children, position }: DraggableWrapperProps) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
    } = useDraggable({
        id,
    });

    const style = {
        position: 'absolute' as const,
        top: `${position.y}px`,
        left: 0,
        transform: transform ? `translate3d(0, ${transform.y}px, 0)` : undefined,
        zIndex: 1000,
        touchAction: 'none',
        width: '100%',
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className="touch-none"
            {...attributes}
            {...listeners}
        >
            {children}
        </div>
    );
};
