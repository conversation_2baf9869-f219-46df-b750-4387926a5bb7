import { FeatureInfoAlert } from "./FeatureInfoAlert";

export const PinnedFeatureRenderer = ({
    pinnedFeature,
    currentSelection,
    onPin,
    onUnpin,
    onCloseSelection,
}: {
    pinnedFeature: { feature: any; position: any } | null;
    currentSelection: { feature: any; position: any } | null;
    onPin: (feature: any, position: { x: number; y: number }) => void;
    onUnpin: () => void;
    onCloseSelection: () => void;
}) => {

    return (
        <>
            {/* Render Pinned Feature */}
            {pinnedFeature && (
                <FeatureInfoAlert
                    selectedFeature={pinnedFeature.feature}
                    position={pinnedFeature.position}
                    isPinned={true}
                    onPin={onUnpin}
                    onClose={onUnpin}
                />
            )}

            {/* Render Current Selection */}
            {currentSelection && (
                <FeatureInfoAlert
                    selectedFeature={currentSelection.feature}
                    position={currentSelection.position}
                    isPinned={pinnedFeature?.feature === currentSelection.feature} // Only allow one pin
                    onPin={() => onPin(currentSelection.feature, currentSelection.position)}
                    onClose={onCloseSelection}
                />
            )}
        </>
    );
};
