import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface PaginationAlertProps {
    hasMultiple: boolean;
    currentIndex: number;
    totalFeatures: number;
    onNext?: () => void;
    onPrevious?: () => void;
}

export const PaginationAlert: React.FC<PaginationAlertProps> = ({
    hasMultiple,
    currentIndex,
    totalFeatures,
    onNext,
    onPrevious,
}) => {
    const { t } = useTranslation();

    if (!hasMultiple || totalFeatures <= 1) {
        return null;
    }

    return (
        <div className="mb-2">
            <div className="bg-white shadow-lg rounded-md border border-gray-200 w-fit">
                <div className="flex items-center justify-center gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onPrevious}
                        disabled={!onPrevious}
                        className="h-8 w-8 p-0 hover:bg-gray-100"
                        title={t('featureInfo.previous')}
                    >
                        <ChevronLeft className="h-4 w-4" />
                    </Button>

                    <span className="text-sm text-gray-600 font-medium px-3 whitespace-nowrap">
                        {currentIndex + 1} / {totalFeatures}
                    </span>

                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onNext}
                        disabled={!onNext}
                        className="h-8 w-8 p-0 hover:bg-gray-100"
                        title={t('featureInfo.next')}
                    >
                        <ChevronRight className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
};
