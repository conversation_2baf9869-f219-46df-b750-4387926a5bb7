import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Settings, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLazyQuery, useMutation } from '@apollo/client';
import { GET_RECORD_DETAILS } from '@/shared/graphQl/queries/layers';
import { EditableLayerModal } from '../EditableLayerModal/RJSFForm';
import { EditSummaryFieldsModal } from '../EditSummaryFieldsModal/EditSummaryFieldsModal';
import { UPDATE_RECORD } from '@/shared/graphQl/mutations/layers';
import { showToast } from '@/shared/utils/toastConfig';
import { formatInitialData, formatMapData, handleGraphQLError } from '@/shared/utils/recordHelpers';
import { useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import MapManager from '../../core/MapManager';
import { Fullscreen } from 'lucide-react';
import { PaginationAlert } from './PaginationAlert';
import { LayerMetadata } from '@/shared/store/slices/mapSlice';
import { getColorBySldType } from '@/shared/utils/layerUtils';

interface FeatureInfoAlertProps {
    selectedFeature: any | null;
    onClose: () => void;
    position?: { x: number; y: number } | null;
    isPinned?: boolean;
    onPin?: () => void;
    isLoading?: boolean;
    // New pagination props
    hasMultiple?: boolean;
    currentIndex?: number;
    totalFeatures?: number;
    onNext?: () => void;
    onPrevious?: () => void;
}

export const FeatureInfoAlert: React.FC<FeatureInfoAlertProps> = React.memo(({
    selectedFeature,
    onClose,
    position,
    hasMultiple = false,
    currentIndex = 0,
    totalFeatures = 1,
    onNext,
    onPrevious,
}) => {
    const [selectedLayer, setSelectedLayer] = useState<LayerMetadata>()
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isSummaryFieldsModalOpen, setIsSummaryFieldsModalOpen] = useState(false);
    const selectedColor = getColorBySldType(selectedLayer?.slds);
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get("orgId");
    const { backendLayers } = useSelector((state: RootState) => state.map);
    const [updateRecord] = useMutation(UPDATE_RECORD);
    const [getRecord, { data: recordDetails, loading }] = useLazyQuery(GET_RECORD_DETAILS, {
        variables: {
            layerId: selectedFeature?.properties?.layer_id,
            orgId: orgId,
            pk: selectedFeature?.properties?.id,
        },
    });
    const mapManager = MapManager.getInstance()

    useEffect(() => {
        if (position && selectedFeature) {
            const alertWidth = 420
            const alertHeight = 420
            const margin = 20

            const viewportWidth = window.innerWidth
            const viewportHeight = window.innerHeight

            const rightOverflow = (position.x + alertWidth + margin) - viewportWidth
            const leftOverflow = margin - position.x
            const bottomOverflow = position.y + alertHeight + margin - viewportHeight
            const topOverflow = margin - position.y

            if (rightOverflow > 0) mapManager.panMap(rightOverflow + margin, 0)
            if (leftOverflow > 0) mapManager.panMap(-leftOverflow - margin, 0)
            if (bottomOverflow > 0) mapManager.panMap(0, bottomOverflow + margin)
            if (topOverflow > 0) mapManager.panMap(0, -topOverflow - margin)
        }
    }, [position])

    const handleGetRecordClick = () => {
        if (selectedFeature?.properties) {
            setIsModalOpen(true);
            getRecord();
        }
    };

    const handleRecordUpdate = async (data: Record<string, string | number>) => {
        try {
            await updateRecord({
                variables: {
                    dataInput: {
                        recordId: selectedFeature?.properties?.id,
                        orgId: orgId,
                        formData: JSON.stringify(data)
                    }
                }
            });
            showToast.success(t('alerts.success.recordUpdated'));
            setIsModalOpen(false);
            onClose()
        } catch (error: any) {
            const errorMessage = handleGraphQLError(error);
            showToast.error(errorMessage);
        }
    };

    useEffect(() => {
        if (selectedFeature) {
            const layerId = selectedFeature?.properties?.layer_id;
            if (layerId) {
                const layer = backendLayers.find(layer => layer.id === layerId);
                if (layer) {
                    setSelectedLayer(layer)
                }
            }
        }
    }, [selectedFeature]);

    if (!selectedFeature) return null;

    return (
        <>
            {!isModalOpen && !isSummaryFieldsModalOpen && (
                <div className="flex flex-col items-center">
                    {/* Pagination Alert - Separate Component */}
                    <PaginationAlert
                        hasMultiple={hasMultiple}
                        currentIndex={currentIndex}
                        totalFeatures={totalFeatures}
                        onNext={onNext}
                        onPrevious={onPrevious}
                    />

                    {/* Main Feature Info Alert */}
                    <div className="w-80">
                        <Alert className={`bg-white shadow-lg rounded-2xl ${direction} py-0`}>
                            {/* Header with title and action buttons */}
                            <div className="flex flex-row items-center justify-between py-1 px-1 border-b">
                                <div className="flex justify-between items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10" fill={selectedColor} />
                                    </svg>
                                    <h3 className="text-base font-semibold">{selectedLayer?.title ?? t('layer.recordDetails')}</h3>
                                </div>
                                <div className="flex items-center">
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 hover:bg-gray-100"
                                        onClick={handleGetRecordClick}
                                        disabled={loading}
                                        title={t('featureInfo.viewDetails')}
                                    >
                                        <Fullscreen className="h-5 w-5" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 hover:bg-gray-100"
                                        onClick={() => setIsSummaryFieldsModalOpen(true)}
                                        title={t('featureInfo.editSummaryFields')}
                                    >
                                        <Settings className="h-5 w-5" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 hover:bg-gray-100"
                                        onClick={onClose}
                                        title={t('featureInfo.close')}
                                    >
                                        <X className="h-5 w-5" />
                                    </Button>
                                </div>
                            </div>

                            {/* Alert content - unchanged */}
                            <AlertDescription className="px-1 py-3 space-y-2 max-h-72 overflow-auto">
                                <div className="text-sm space-y-1">
                                    {selectedFeature.properties?.map_data && (
                                        <>
                                            {formatMapData(selectedFeature.properties.map_data).map(({ key, value }) => {
                                                return (
                                                    <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                                                        <div className="text-sm font-medium text-gray-600">{key}</div>
                                                        <div className="text-sm text-gray-800 max-w-[60%] text-left break-words">
                                                            {value?.startsWith('http') ? (
                                                                <a href={value} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                                                    {t('link')}
                                                                </a>
                                                            ) : value.startsWith('{') || value.startsWith('[') ? (
                                                                <span className="text-gray-500 italic">{t('viewInDetails')}</span>
                                                            ) : value}
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </>
                                    )}
                                </div>
                            </AlertDescription>
                        </Alert>
                    </div>
                </div>
            )}

            {isModalOpen && (
                <EditableLayerModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    onSave={handleRecordUpdate}
                    mode="view"
                    selectedLayer={selectedLayer}
                    initialData={formatInitialData(recordDetails)}
                    loading={loading}
                />
            )}
            {isSummaryFieldsModalOpen && <EditSummaryFieldsModal
                isOpen={isSummaryFieldsModalOpen}
                onClose={() => setIsSummaryFieldsModalOpen(false)}
                layerId={selectedFeature?.properties?.layer_id}
                onCloseAlert={onClose}
            />}
        </>
    );
});