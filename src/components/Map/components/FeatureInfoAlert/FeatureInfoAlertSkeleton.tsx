import { Alert, AlertDescription } from '@/components/ui/alert';
import { Layers } from 'lucide-react';

export const FeatureInfoAlertSkeleton = ({ position }: { position: { x: number; y: number } | null }) => {
    const style: React.CSSProperties = position ? {
        position: 'fixed',
        left: `${position.x + 2}px`,
        top: `${position.y + 65}px`,
        zIndex: 1000,
        pointerEvents: 'auto',
    } : {};

    return (
        <div style={style} className="w-80">
            <Alert className="bg-white shadow-lg rounded-2xl">
                <div className="flex flex-row items-center justify-between py-1 px-1 border-b">
                    <div className="flex justify-between items-center gap-2">
                        <Layers className="h-4 w-4" />
                        <div className="h-4 w-20 bg-gray-200 animate-pulse rounded" />
                    </div>
                    <div className="flex items-center gap-2">
                        {[1, 2, 3, 4].map((i) => (
                            <div key={i} className="h-8 w-8 rounded-full bg-gray-200 animate-pulse" />
                        ))}
                    </div>
                </div>
                <AlertDescription className="px-4 py-3">
                    {[1, 2, 3].map((i) => (
                        <div key={i} className="flex justify-between py-2">
                            <div className="h-4 w-24 bg-gray-200 animate-pulse rounded" />
                            <div className="h-4 w-32 bg-gray-200 animate-pulse rounded" />
                        </div>
                    ))}
                </AlertDescription>
            </Alert>
        </div>
    );
};
