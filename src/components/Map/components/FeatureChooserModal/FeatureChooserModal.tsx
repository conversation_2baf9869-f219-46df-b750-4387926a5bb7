import React, { useEffect, useState } from 'react';
import ReactD<PERSON> from 'react-dom';
import { useDispatch } from 'react-redux';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { setSelectedWmsFeature } from '@/shared/store/slices/mapSlice';
import MapManager from '../../core/MapManager';
import { X } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area"
import { SortableContext } from '@dnd-kit/sortable';

interface FeatureChooserModalProps {
    open: boolean;
    features: any[];
    onClose: () => void;
}

export const FeatureChooserModal: React.FC<FeatureChooserModalProps> = ({
    open,
    features,
    onClose,
}) => {
    const [container, setContainer] = useState<HTMLElement | null>(null);
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const dispatch = useDispatch();
    const mapManager = MapManager.getInstance()

    useEffect(() => {
        // Create a div element for the portal and append it to the body
        const div = document.createElement('div');
        document.body.appendChild(div);
        setContainer(div);

        return () => {
            // Cleanup: Remove the div from the body when the component unmounts
            document.body.removeChild(div);
        };
    }, []);

    if (!container) {
        return null;
    }

    const handleFeatureSelect = (feature: any) => {
        mapManager.highlightFeature(feature);
        dispatch(setSelectedWmsFeature(feature));
        onClose();
    };

    return ReactDOM.createPortal(
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="max-w-sm h-fit [&>button]:hidden">
                <DialogHeader>
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                            <DialogTitle className={`text-lg font-medium text-gray-800 ${direction}`}>
                                {t('Please choose a Feature')}
                            </DialogTitle>
                        </div>
                        <Button variant="ghost" size="icon" onClick={onClose} className='no-border-focus'>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </DialogHeader>
                <ScrollArea className={`max-h-[50vh] overflow-y-auto p-0 ${direction}`}>
                    <SortableContext items={features}>
                        <div className={`divide-y divide-gray-200 ${direction}`}>
                            {features.map((feature, index) => (
                                <div key={feature.id || index}>
                                    <Button
                                        variant="ghost"
                                        className="w-full text-left p-4 no-border-focus"
                                        onClick={() => handleFeatureSelect(feature)}
                                    >
                                        {feature?.properties?.id}-{feature?.geometry?.geometries[0]?.type}-layerid:{feature?.properties?.layer_id}
                                    </Button>
                                </div>
                            ))}
                        </div>
                    </SortableContext>
                </ScrollArea>
            </DialogContent>
        </Dialog>
        ,
        container
    );
};
