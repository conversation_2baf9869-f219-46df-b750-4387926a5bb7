import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>, Trash } from "lucide-react";
import offEyeIcon from "@/assets/offeye.svg";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { LayerMetadata, SldType, setSelectedMainLayer, setSidebarView, updateLayer } from "@/shared/store/slices/mapSlice";
import { Button } from "@/components/ui/button";
import MapManager from "../../core/MapManager";
import { useState } from "react";
import { RootState } from "@/shared/store";
import { useDispatch, useSelector } from "react-redux";
import { useDeleteLayer } from '../../hooks/useDeleteLayer'
import { DeleteLayerConfirmation } from "../DeleteLayerModal/DeleteLayerConfirmation";
import { useMutation } from "@apollo/client";
import { UPDATE_LAYER } from "@/shared/graphQl/mutations/layers";
import { useTranslation } from "react-i18next";
import { BarLoader } from "@/components/ui/BarLoader";
import { updateLayersVisibility } from "@/shared/utils/layerVisibilityUtils";
import { useSearchParams } from 'react-router-dom';
import { getColorBySldType } from "@/shared/utils/layerUtils";

export const MainLayerItem = (layer: LayerMetadata) => {
    const { title, slds, isVisible, layerKey, className, id, dataset } = layer
    const dispatch = useDispatch();
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const isRTL = direction === 'rtl';
    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const letterMappings = useSelector((state: RootState) => state.dataset.letterMappings);
    const letter = letterMappings[dataset?.id] || ''
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');

    const color = getColorBySldType(slds, SldType.POINTS);
    const isSelected = selectedMainLayer?.id === id;
    const mapManager = MapManager.getInstance()

    const switchLayer = (layerName: string) => {
        const newVisibility = mapManager.toggleVisibility(layerName)
        dispatch(
            updateLayer({
                id: id,
                updates: { isVisible: newVisibility },
            })
        )
    }

    const handleShowLayerOnly = () => {
        updateLayersVisibility(
            () => mapManager.showLayerOnly(layerKey as string),
            layerKey,
            false,
            () => setDropdownOpen(false)
        )
    }

    const handleLayerSelectionWithZoom = () => {
        dispatch(setSelectedMainLayer({ ...layer, fromLayerClick: true }));
    }

    const handleShowAllLayers = () => {
        updateLayersVisibility(
            () => mapManager.showAllLayers(),
            undefined,
            true,
            () => setDropdownOpen(false)
        )
    }

    const [isEditing, setIsEditing] = useState(false);
    const [newTitle, setNewTitle] = useState(layer.title);

    const [dropdownOpen, setDropdownOpen] = useState(false)
    const { showDeleteModal, setShowDeleteModal, handleDeleteLayer } = useDeleteLayer()
    const [updateLayerBackend, loading] = useMutation(UPDATE_LAYER);

    const handleRename = () => {
        setIsEditing(true);
    };

    const handleTitleSubmit = () => {
        dispatch(updateLayer({
            id: layer.id,
            updates: { title: newTitle }
        }));
        setIsEditing(false);
        updateLayerBackend({
            variables: {
                dataInput: {
                    layerId: selectedMainLayer.id,
                    title: newTitle,
                    orgId
                }
            }
        });
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleTitleSubmit();
        }
        if (e.key === 'Escape') {
            setNewTitle(layer.title);
            setIsEditing(false);
        }
    };

    const handleSettingsClick = () => {
        dispatch(setSelectedMainLayer(layer));
        dispatch(setSidebarView('settings'));
    };

    const handleDeleteClick = () => {
        setDropdownOpen(false)
        setShowDeleteModal(true)
    }

    const isLayerLoading = useSelector((state: RootState) =>
        state.map.loadingLayers[layerKey as string]
    )

    return (
        <div
            className={cn(
                "bg-gray-50 relative gap-2.5 rounded-lg px-5 py-6 flex items-center justify-between max-w-[280px] rtl:flex-row ltr:flex-row-reverse overflow-hidden",
                isSelected ? "border bg-white" : "",
                !isVisible ? "opacity-70" : "",
                className
            )}
            style={isSelected ? {
                borderColor: 'rgba(134, 73, 255, 1)',
                boxShadow: '0 0 0 3px rgba(223, 222, 252, 1)'
            } : {}}
        >
            {/* Accent bar - right side for RTL, left side for LTR */}
            <div
                className={cn(
                    "absolute top-0 bottom-0 w-1.5 rounded-xl",
                    isRTL ? "right-0 rounded-r-xl" : "left-0 rounded-l-xl"
                )}
                style={{ backgroundColor: color }}
            />

            {isLayerLoading && (
                <div className="absolute bottom-0 left-0 right-0 flex items-center justify-center bg-white/50 rounded-b-lg">
                    <BarLoader />
                </div>
            )}
            <div
                className={cn(
                    "flex items-center rtl:flex-row ltr:flex-row-reverse")}
            >
                <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                    <DropdownMenuTrigger className="no-border-focus" asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4 text-gray-500" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className={`w-40 no-border-focus ${direction}`}>
                        <DropdownMenuItem onClick={handleSettingsClick}>{t('layerActions.layerSettings')}</DropdownMenuItem>
                        <div className="border-t my-1" />
                        <DropdownMenuItem onClick={handleLayerSelectionWithZoom}>{t('layerActions.zoom_to_layer')}</DropdownMenuItem>
                        <div className="border-t my-1" />
                        <DropdownMenuItem onClick={handleRename}>{t('layerActions.rename')}</DropdownMenuItem>
                        <div className="border-t my-1" />
                        <DropdownMenuItem onClick={handleShowLayerOnly}>
                            {t('layerActions.showThisLayerOnly')}
                        </DropdownMenuItem>
                        <div className="border-t my-1" />
                        <DropdownMenuItem onClick={handleShowAllLayers}>
                            {t('layerActions.showAllLayers')}
                        </DropdownMenuItem>
                        <div className="border-t my-1" />
                        <DropdownMenuItem
                            className={`flex justify-between gap-2 text-red-600 ${direction}`}
                            onClick={handleDeleteClick}
                        >
                            <span>{t('layerActions.delete')}</span>
                            <Trash className="h-4 w-4" />
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 p-0"
                    onClick={() => switchLayer(layerKey as string)}
                >
                    {isVisible ? (
                        <Eye className="h-4 w-4 text-gray-500" />
                    ) : (
                        <img src={offEyeIcon} className="h-4 w-4 text-gray-500" alt="Hidden" />
                    )}
                </Button>

                <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 p-0"
                    onClick={handleSettingsClick}
                >
                    <Settings className="h-4 w-4 text-gray-500" />
                </Button>
            </div>

            <div className="flex items-center gap-2.5 rtl:text-right ltr:text-left min-w-0" onDoubleClick={handleRename}>
                <div className="flex flex-col rtl:items-end ltr:items-start gap-1">
                    <div className="flex items-center gap-1 rtl:flex-row ltr:flex-row-reverse">
                        {/* <div className="flex h-5 w-5 items-center justify-center rounded bg-purple-100 text-purple-600">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 7H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                                <path d="M6 12H18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                                <path d="M10 17H14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                            </svg>
                        </div> */}
                        <span className="text-xs text-gray-500 flex-shrink-0">
                            {selectedMainLayer?.locationFieldMapping?.coordinate_type === 'point'
                                ? direction === 'rtl' ? 'نقاط' : "Points"
                                : direction === 'rtl' ? 'اخري' : "Other"
                            }
                        </span>
                        {letter && <div className="px-1.5 py-0.5 bg-[#E1E1E1] rounded text-xs font-bold flex-shrink-0">{letter}</div>}
                    </div>
                    <div className="flex items-center gap-1.5">
                        {isEditing ? (
                            <textarea
                                value={newTitle}
                                onChange={(e) => setNewTitle(e.target.value)}
                                onBlur={handleTitleSubmit}
                                onKeyDown={handleKeyDown}
                                className="text-sm font-medium min-h-[20px] no-border-focus w-full resize-none bg-transparent overflow-hidden"
                                autoFocus
                                rows={1}
                                style={{ minHeight: '20px', height: 'auto' }}
                                ref={(el) => {
                                    if (el) {
                                        el.style.height = 'auto';
                                        el.style.height = `${el.scrollHeight}px`;
                                    }
                                }}
                            />
                        ) : (
                            <span
                                className={cn(
                                    "text-sm font-medium break-words",
                                    isVisible ? "cursor-pointer" : ""
                                )}
                                onClick={
                                    isVisible
                                        ? () => dispatch(setSelectedMainLayer({ ...layer, fromLayerClick: false }))
                                        : undefined
                                }
                            >{loading ? newTitle : title}</span>
                        )}
                    </div>
                </div>
            </div>

            {showDeleteModal && (
                <DeleteLayerConfirmation
                    isOpen={showDeleteModal}
                    onClose={() => setShowDeleteModal(false)}
                    onConfirm={() => handleDeleteLayer(id, layerKey)}
                    title={title}
                />
            )}
        </div>
    );
}