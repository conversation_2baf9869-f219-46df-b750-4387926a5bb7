import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { MainLayerItem } from "./MainLayerItem";
import { LayerMetadata } from "@/shared/store/slices/mapSlice";
import { GripVertical } from "lucide-react";
import { useTranslation } from "react-i18next";

export const SortableMainLayerItem = (props: LayerMetadata) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
    } = useSortable({ id: props.id });
    const { i18n } = useTranslation()
    const direction = i18n.dir()
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div ref={setNodeRef} style={style} className="group relative">
            <MainLayerItem {...props} />
            <div
                {...attributes}
                {...listeners}
                className={`${direction === 'rtl' ? 'right-0' : 'left-0'} absolute top-1/2 -translate-y-1/2 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity rtl:mr-[.25rem] ltr:ml-[.25rem]`}
            >
                <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
        </div>
    );
};