import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Eye, EyeOff, Trash2 } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";


interface DrawingLayerItemProps {
    id?: string;
    title: string;
    className?: string;
}

export const DrawingLayerItem = ({ title, className }: DrawingLayerItemProps) => {
    const [visible,] = useState(true);
    const { i18n } = useTranslation()
    const direction = i18n.dir()

    return (
        <div className={cn(
            "flex items-center justify-between gap-4 rounded-lg bg-[#F7F7F7] p-3",
            className
        )}>
            <div className="flex items-center gap-1">
                <Button variant="ghost" size="icon" className="h-5 w-5">
                    {visible ? (
                        <Eye className="h-4 w-4 text-gray-500" />
                    ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                </Button>
                <Button variant="ghost" size="icon" className="h-5 w-5">
                    <Trash2 className="w-4 h-4 ml-auto" />
                </Button>
            </div>
            <div className={direction}>
                <span className="text-sm font-medium">{title}</span>
            </div>
        </div>
    );
}