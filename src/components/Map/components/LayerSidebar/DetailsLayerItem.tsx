import { MoreVertical, Trash } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { useParams, useSearchParams } from "react-router-dom";
import { useMutation } from "@apollo/client";
import { CREATE_LAYER_FROM_DATASET } from "@/shared/graphQl/mutations/layers";
import { useDispatch } from "react-redux";
import { addBackendLayer } from "@/shared/store/slices/mapSlice";
import { useTranslation } from "react-i18next";

export const DetailsLayerItem = (dataset: any) => {
    const { title, className, letter, id } = dataset;
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const { workspaceId } = useParams()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');
    const dispatch = useDispatch();
    const [createLayer] = useMutation(CREATE_LAYER_FROM_DATASET);

    const handleCreateLayer = async () => {
        try {
            const { data } = await createLayer({
                variables: {
                    dataInput: {
                        datasetId: id,
                        workspaceId,
                        orgId
                    }
                }
            });

            if (data?.createLayerFromDataset?.layer) {
                dispatch(addBackendLayer({ ...data.createLayerFromDataset.layer, isVisible: true, }));
            }
        } catch (error) {
            console.error('Error creating layer:', error);
        }
    };

    return (
        <>
            <div className={cn(
                `flex items-center justify-between gap-4 rounded-lg bg-[#F7F7F7] p-4 ${direction}`,
                className
            )}>
                <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-6 h-6 rounded bg-gray-200 text-sm font-semibold text-gray-800">
                        {letter && `${letter}`}
                    </div>
                    <span className="text-sm font-medium">
                        {title}
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <DropdownMenu>
                        <DropdownMenuTrigger className="no-border-focus">
                            <MoreVertical className="h-4 w-4 text-gray-500" />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className={`w-40 no-border-focus ${direction}`}>
                            <DropdownMenuItem onClick={handleCreateLayer}>{t('layerActions.addLayer')}</DropdownMenuItem>
                            <div className="border-t my-1" />
                            <DropdownMenuItem disabled> {t('layerActions.rename')}</DropdownMenuItem>
                            <div className="border-t my-1" />
                            <DropdownMenuItem className={`flex justify-between gap-2 text-red-600 ${direction}`} disabled>
                                <span>{t('layerActions.delete')}</span>
                                <Trash className="h-4 w-4" />
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </>
    );
};
