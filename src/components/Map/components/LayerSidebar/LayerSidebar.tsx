import { <PERSON><PERSON> } from "@/components/ui/button";
import { DirectionalScrollArea } from "./DirectionalScrollArea";
import { ChevronDown, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useSelector } from "react-redux";
import { RootState } from "@/shared/store";
import { DetailsLayerItem } from "./DetailsLayerItem";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { SortableMainLayerItem } from "./SortableMainLayerItem";
import { useDispatch } from "react-redux";
import { setLayerOrder } from "@/shared/store/slices/mapSlice";
import MapManager from "../../core/MapManager";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { LayerSettings } from './LayerSettings/LayerSettings';
import { useDatasetLetters } from "@/shared/hooks/useDatasetLetters";
import { useParams } from "react-router-dom";
import { useMutation, useQuery } from "@apollo/client";
import { UPDATE_WORKSPACE } from "@/shared/graphQl/mutations/layers";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";
import { WorkspaceMethods } from "@/shared/utils/routes";
import { Routes } from "@/shared/utils/routes";
import { checkForExistingWorkspaceId } from "@/shared/utils/workspaceUtils";
import { CHECK_WORKSPACE_ID_EXISTENCE } from "@/shared/graphQl/queries/dataset";

export const LayerSidebar = () => {
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [isFooterOpen, setIsFooterOpen] = useState(false);
    const dispatch = useDispatch();
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const { backendLayers: mainLayers, sidebarView } = useSelector((state: RootState) => state.map);
    const { selectedOrg, navigateWithOrg } = useOrganizationManager()
    const mapManager = MapManager.getInstance();
    const { letterMappings, datasets } = useDatasetLetters();
    const { workspaceId } = useParams();

    const [updateWorkspaceLayersSort] = useMutation(UPDATE_WORKSPACE);

    // Query to check workspace existence
    const { data: workspaceExistenceData } = useQuery(CHECK_WORKSPACE_ID_EXISTENCE, {
        variables: { orgId: selectedOrg?.id },
        skip: !selectedOrg?.id,
    });

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const handleDragEnd = (event: any) => {
        const { active, over } = event;

        if (active.id !== over.id) {
            const oldIndex = mainLayers.findIndex((layer) => layer.id === active.id);
            const newIndex = mainLayers.findIndex((layer) => layer.id === over.id);

            const newOrder = arrayMove(mainLayers, oldIndex, newIndex) as any;
            dispatch(setLayerOrder(newOrder));
            mapManager.reorderLayers(newOrder.map((layer: any) => layer.layerKey));

            const layerIds = newOrder.map((layer: any) => layer.id);
            updateWorkspaceLayersSort({
                variables: {
                    dataInput: {
                        workspaceId: parseInt(workspaceId!),
                        layersSortedIds: layerIds,
                        orgId: selectedOrg?.id,
                    }
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }
    };

    //  Handle the "Add DataSet From Existing Workspace"
    const handleAddDataClick = () => {
        if (workspaceId) {
            const params = new URLSearchParams();
            params.set('orgId', selectedOrg?.id.toString() || '');
            const existingWorkspaceId = checkForExistingWorkspaceId(workspaceExistenceData);
            if (existingWorkspaceId) {
                params.set('workspaceId', workspaceId);
            } else if ((workspaceExistenceData?.workspaceRequests?.data ?? []).length <= 0) { params.set('workspaceId', workspaceId) };
            navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/0?${params.toString()}`);
        }
    };

    return (
        <aside className={cn(
            `absolute rtl:right-4 ltr:left-4 top-4 w-[300px] border rounded-lg bg-white transition-all duration-200 ease-in-out z-10`,
            isSidebarOpen ? "h-[calc(91vh-98px)]" : "h-16",
        )}>
            <Tabs defaultValue="layers" className="h-full">
                <div className={cn(
                    "p-3 border-b",
                    !isSidebarOpen && "border-b-0",
                )}>
                    <div className={`flex items-center justify-between w-full ${direction}`}>
                        <TabsList className="h-9">
                            <TabsTrigger value="layers" className="text-sm px-6">{t('layers')}</TabsTrigger>
                            <TabsTrigger value="details" className="text-sm px-6">{t('filters')} </TabsTrigger>
                        </TabsList>
                        <ChevronDown
                            className={cn(
                                "h-4 w-4 transition-transform duration-200 cursor-pointer",
                                isSidebarOpen ? "rotate-180" : ""
                            )}
                            onClick={toggleSidebar}
                        />
                    </div>
                </div>

                <div className={cn(
                    "transition-all duration-200 ease-in-out overflow-hidden",
                    isSidebarOpen ? "opacity-100 h-[calc(100%-4rem)]" : "opacity-0 h-0"
                )}>
                    <TabsContent value="layers">
                        {sidebarView === 'layers' ? (
                            <>
                                <DndContext
                                    collisionDetection={closestCenter}
                                    onDragEnd={handleDragEnd}
                                    modifiers={[restrictToVerticalAxis, restrictToParentElement]}
                                >
                                    <DirectionalScrollArea className={`${isFooterOpen ? 'h-[calc(58vh-5rem)]' : 'h-[calc(77vh-6rem)]'}`}>
                                        <SortableContext items={mainLayers}>
                                            <div className="space-y-2 p-3 px-4">
                                                {mainLayers.map((layer: any) => (
                                                    <SortableMainLayerItem {...layer} key={layer.id} />
                                                ))}
                                            </div>
                                        </SortableContext>
                                    </DirectionalScrollArea>
                                </DndContext>
                                <div className={cn(
                                    "bg-white border-t transition-all duration-200 ease-in-out",
                                    isFooterOpen ? "absolute bottom-0 rtl:left-0 ltr:right:0 w-full" : ""
                                )}>
                                    <Collapsible open={isFooterOpen} onOpenChange={setIsFooterOpen} className="w-full">
                                        <div className="flex items-center justify-between px-4 py-3 border-b">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="bg-[#EFEFEF] rounded-md"
                                                onClick={handleAddDataClick}
                                            >
                                                <Plus className="h-4 w-4" />
                                                <span className="text-xs">{t('addData')}</span>
                                            </Button>
                                            <CollapsibleTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="text-gray-500 hover:text-gray-700 p-0"
                                                >
                                                    <ChevronDown
                                                        className={cn(
                                                            "h-4 w-4 transition-transform duration-200",
                                                            isFooterOpen ? "rotate-180" : ""
                                                        )}
                                                    />
                                                    <span className="text-xs">{t('dataset')}</span>
                                                </Button>
                                            </CollapsibleTrigger>

                                        </div>
                                        <CollapsibleContent>
                                            <DirectionalScrollArea className="h-[calc(15vh)] space-y-2">
                                                <div className="space-y-2 p-2">
                                                    {datasets.length > 0 ?
                                                        datasets?.map((dataset: any) => (
                                                            <DetailsLayerItem
                                                                key={dataset.id}
                                                                {...dataset}
                                                                letter={letterMappings[dataset.id]}
                                                            />
                                                        )) : ''}
                                                </div>
                                            </DirectionalScrollArea>
                                        </CollapsibleContent>
                                    </Collapsible>
                                </div>
                            </>
                        ) : (
                            <DirectionalScrollArea className="h-[calc(76.5vh-6rem)]">
                                <LayerSettings />
                            </DirectionalScrollArea>
                        )}
                    </TabsContent>
                    <TabsContent value="details" className="flex-1 p-4">
                        <div className="space-y-2">
                        </div>
                    </TabsContent>
                </div>
            </Tabs>
        </aside>
    );
};
