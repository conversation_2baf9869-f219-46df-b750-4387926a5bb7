import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMutation } from "@apollo/client";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { ChevronLeft, ChevronRight, Eye, MoreVertical, Trash } from "lucide-react";
import offEyeIcon from "@/assets/offeye.svg";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { DeleteLayerConfirmation } from "../../DeleteLayerModal/DeleteLayerConfirmation";
import { cn } from "@/lib/utils";
import { useDeleteLayer } from '../../../hooks/useDeleteLayer'
import { setSidebarView, updateLayer } from "@/shared/store/slices/mapSlice";
import { RootState } from "@/shared/store";
import MapManager from "@/components/Map/core/MapManager";
import { UPDATE_LAYER } from "@/shared/graphQl/mutations/layers";

export const SettingLayerItem = () => {
  // Hooks & Initialization
  const { t, i18n } = useTranslation();
  const direction = i18n.dir();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const orgId = searchParams.get('orgId');

  // Redux Selectors
  const selectedMainLayer = useSelector(
    (state: RootState) => state.map.selectedMainLayer
  );
  const letterMappings = useSelector(
    (state: RootState) => state.dataset.letterMappings
  );

  // State
  const [isEditing, setIsEditing] = useState(false);
  const [newTitle, setNewTitle] = useState(selectedMainLayer?.title || '');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const { showDeleteModal, setShowDeleteModal, handleDeleteLayer } = useDeleteLayer();
  const [updateLayerBackend] = useMutation(UPDATE_LAYER);

  // Early return if no layer selected
  if (!selectedMainLayer) return null;

  // Destructure layer properties
  const { title, isVisible, layerKey, className, id, dataset } = selectedMainLayer as any;
  const letter = letterMappings[dataset?.id] || '';

  // Handlers
  const switchLayer = (layerName: string) => {
    const newVisibility = MapManager.getInstance().toggleVisibility(layerName);
    dispatch(updateLayer({
      id: id,
      updates: { isVisible: newVisibility },
    }));
  };

  const handleRename = () => setIsEditing(true);

  const handleTitleSubmit = () => {
    dispatch(updateLayer({
      id: selectedMainLayer.id,
      updates: { title: newTitle }
    }));

    updateLayerBackend({
      variables: {
        dataInput: {
          layerId: selectedMainLayer.id,
          title: newTitle,
          orgId
        }
      }
    });
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleTitleSubmit();
    if (e.key === 'Escape') {
      setNewTitle(selectedMainLayer.title);
      setIsEditing(false);
    }
  };

  const handleDeleteClick = () => {
    setDropdownOpen(false);
    setShowDeleteModal(true);
  };

  return (
    <div
      className={cn(
        `flex items-center justify-between gap-4 rounded-lg px-0 py-2 ${direction}`,
        className
      )}
    >
      <div
        className={cn(
          'flex items-center gap-2',
          !isVisible ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        )}
        onDoubleClick={handleRename}
      >
        <Button
          variant="ghost"
          size="icon"
          onClick={() => dispatch(setSidebarView('layers'))}
          className="p-0 h-6 w-6"
        >
          {direction === "rtl" ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
        {letter && <span className="text-sm font-medium"> {`(${letter})`}</span>}
        {isEditing ? (
          <Input
            type="text"
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onBlur={handleTitleSubmit}
            onKeyDown={handleKeyDown}
            className="text-sm font-medium h-7 no-border-focus"
            autoFocus
          />
        ) : (
          <span className="text-sm font-medium">{title}</span>
        )}
      </div>
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 hover:bg-transparent"
          onClick={() => switchLayer(layerKey as string)}
        >
          {isVisible ? (
            <Eye className="h-4 w-4" />
          ) : (
            <img src={offEyeIcon} className="h-4 w-4 text-gray-500" alt="hidden" />
          )}
        </Button>
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger className="no-border-focus">
            <MoreVertical
              className={cn(
                'h-4 w-4',
                !isVisible ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
              )}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className={`w-40 no-border-focus ${direction}`}>
            <DropdownMenuItem onClick={handleRename}>
              {' '}
              {t('layerActions.rename')}
            </DropdownMenuItem>
            <div className="border-t my-1" />
            <DropdownMenuItem
              className={`flex justify-between gap-2 text-red-600 ${direction}`}
              onClick={handleDeleteClick}
            >
              <span>{t('layerActions.delete')}</span>
              <Trash className="h-4 w-4" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {showDeleteModal && (
        <DeleteLayerConfirmation
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => handleDeleteLayer(id, layerKey)}
          title={title}
        />
      )}
    </div>
  )
}
