import React from 'react';
import { Slider } from '@/components/ui/slider';
import { useTranslation } from 'react-i18next';
import { formatNumber } from '@/shared/utils/formatters';

interface RangeSliderProps {
    label: string;
    value: number;
    onChange: (value: number) => void;
    min?: number;
    max?: number;
    step?: number;
    unit?: string;
    showValue?: boolean;
    decimals?: number;
}

export const RangeSlider: React.FC<RangeSliderProps> = ({
    label,
    value,
    onChange,
    min = 0,
    max = 100,
    step = 1,
    unit = '',
    showValue = true,
    decimals = 0
}) => {
    const { i18n } = useTranslation();
    const direction = i18n.dir();

    const handleValueChange = (values: number[]) => {
        onChange(values[0]);
    };

    return (
        <div className="space-y-2">
            <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">{label}</span>
            </div>
            <div className="flex items-center gap-4">
                <Slider
                    dir={direction}
                    value={[value]}
                    min={min}
                    max={max}
                    step={step}
                    onValueChange={handleValueChange}
                    className="flex-1 h-8 no-border-focus"
                />
                {showValue && (
                    <span className="text-sm text-gray-500 min-w-[50px] text-right">
                        {formatNumber(value, decimals)}{unit}
                    </span>
                )}
            </div>
        </div>
    );
};