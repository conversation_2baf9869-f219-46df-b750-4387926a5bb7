import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Trash2, Eye, EyeOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { getOperationsForFieldType } from '@/shared/utils/filterOperations'
import { getReadableFilterText } from '@/shared/utils/filterUtils'
import { buildGraphQLFilters, buildLayerFilters, serializeLayerFilters } from '@/shared/utils/filters/filterBuilder'
import { useMutation } from '@apollo/client'
import { UPDATE_LAYER_FILTERS } from '@/shared/graphQl/mutations/layers'
import { useSearchParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import MapManager from '@/components/Map/core/MapManager'

import { FilterValueInput } from './FilterValueInput'

interface FilterConditionRowProps {
  filter: FilterCondition
  availableFields: Array<{ name: string; label: string; type: string }>
  onUpdate: (updates: Partial<FilterCondition>) => void
  onRemove: () => void
}

export const FilterConditionRow: React.FC<FilterConditionRowProps> = ({
  filter,
  availableFields,
  onUpdate,
  onRemove,
}) => {
  const { t, i18n } = useTranslation()
  const direction = i18n.dir()
  const [inputValue, setInputValue] = useState<string>('')
  const [searchParams] = useSearchParams()
  const mapManager = MapManager.getInstance()

  const { selectedMainLayer } = useSelector((state: RootState) => state.map)
  const orgId = searchParams.get('orgId')

  const [updateLayerFilters] = useMutation(UPDATE_LAYER_FILTERS)

  const handleFieldChange = (fieldName: string) => {
    const selectedField = availableFields.find((f) => f.name === fieldName)
    const fieldType = selectedField?.type as 'string' | 'number' | 'boolean' | 'date' | 'time' | 'integer'

    // Get first operation as default
    const operations = getOperationsForFieldType(fieldType)
    const firstOperation = operations[0]

    onUpdate({
      field: fieldName,
      dataType: fieldType,
      operator: firstOperation?.value || '',
      isNot: firstOperation?.isNot || false,
      value: null, // Reset value when field changes
    })
  }

  const handleOperationChange = (operationIndex: string) => {
    const operations = getOperationsForFieldType(filter.dataType)
    const selectedOperation = operations[parseInt(operationIndex)]

    if (selectedOperation) {
      onUpdate({
        operator: selectedOperation.value,
        isNot: selectedOperation.isNot || false,
        value: null, // Reset value when operator changes
      })
    }
  }

  const handleValueChange = (value: any) => {
    onUpdate({ value })
  }

  const handleApplyFilter = async () => {
    if (!selectedMainLayer?.id || !orgId) return

    try {
      // Get all existing applied filters from the layer
      const currentFilters = selectedMainLayer?.filters?.graphql_filters || []
      const existingAppliedFilters = currentFilters.filter((f: FilterCondition) =>
        f.applied && f.id !== filter.id
      )

      // Add the current filter as applied and visible
      const updatedFilter = { ...filter, applied: true, isVisible: true }

      // Combine existing applied filters with the new one
      const allAppliedFilters = [...existingAppliedFilters, updatedFilter]

      // Only visible applied filters for CQL
      const visibleAppliedFilters = allAppliedFilters.filter((f: FilterCondition) => f.isVisible)

      const layerFilters = buildLayerFilters(visibleAppliedFilters) as any

      // Build complete GraphQL filters with all applied filters (visible + hidden)
      const completeGraphQLFilters = buildGraphQLFilters(allAppliedFilters)

      const serializedFilters = serializeLayerFilters({
        cql_filter: layerFilters.cql_filter,
        graphql_filters: completeGraphQLFilters
      })

      await updateLayerFilters({
        variables: {
          dataInput: {
            orgId: parseInt(orgId),
            layerId: selectedMainLayer.id,
            filters: serializedFilters
          }
        }
      })

      mapManager.addOrUpdateWmsLayer({
        wmsLayerName: selectedMainLayer?.layerKey,
        wmsLayerId: selectedMainLayer?.id,
        cqlFilter: layerFilters?.cql_filter,
      })

      // Mark this filter as applied
      onUpdate({ applied: true, isVisible: true })
    } catch (error) {
      console.error('Failed to apply filter:', error)
    }
  }

  const handleVisibilityToggle = async () => {
    if (!selectedMainLayer?.id || !orgId) return

    const newVisibility = !filter.isVisible

    try {
      // Update Redux store first
      onUpdate({ isVisible: newVisibility })

      // If filter is applied, update backend and map
      if (filter.applied) {
        const currentFilters = selectedMainLayer?.filters?.graphql_filters || []
        const updatedFilters = currentFilters.map((f: FilterCondition) =>
          f.id === filter.id ? { ...f, isVisible: newVisibility } : f
        )

        // Get all applied filters (visible and hidden) for GraphQL
        const allAppliedFilters = updatedFilters.filter((f: FilterCondition) => f.applied)

        // Only visible applied filters for CQL
        const visibleAppliedFilters = allAppliedFilters.filter((f: FilterCondition) => f.isVisible)

        const layerFilters = buildLayerFilters(visibleAppliedFilters) as any

        // Build complete GraphQL filters with all applied filters (visible + hidden)
        const completeGraphQLFilters = buildGraphQLFilters(allAppliedFilters)

        const serializedFilters = serializeLayerFilters({
          cql_filter: layerFilters.cql_filter,
          graphql_filters: completeGraphQLFilters
        })

        await updateLayerFilters({
          variables: {
            dataInput: {
              orgId: parseInt(orgId),
              layerId: selectedMainLayer.id,
              filters: serializedFilters
            }
          }
        })

        // Update map with visible filters only
        mapManager.addOrUpdateWmsLayer({
          wmsLayerName: selectedMainLayer?.layerKey,
          wmsLayerId: selectedMainLayer?.id,
          cqlFilter: layerFilters?.cql_filter,
        })
      }
    } catch (error) {
      console.error('Failed to update filter visibility:', error)
    }
  }

  const handleRemoveFilter = async () => {
    if (!selectedMainLayer?.id || !orgId) return

    try {
      // Get all current filters except the one being removed
      const currentFilters = selectedMainLayer?.filters?.graphql_filters || []
      const remainingFilters = currentFilters.filter((f: FilterCondition) => f.id !== filter.id)

      // Update Redux store first
      onRemove()

      // If the removed filter was applied, update backend
      if (filter.applied) {
        const remainingAppliedFilters = remainingFilters.filter((f: FilterCondition) => f.applied)
        const layerFilters = buildLayerFilters(remainingAppliedFilters) as any
        const serializedFilters = serializeLayerFilters(layerFilters)

        await updateLayerFilters({
          variables: {
            dataInput: {
              orgId: parseInt(orgId),
              layerId: selectedMainLayer.id,
              filters: serializedFilters
            }
          }
        })

        // Update the map with remaining filters
        mapManager.addOrUpdateWmsLayer({
          wmsLayerName: selectedMainLayer?.layerKey,
          wmsLayerId: selectedMainLayer?.id,
          cqlFilter: layerFilters?.cql_filter,
        })
      }
    } catch (error) {
      console.error('Failed to remove filter:', error)
    }
  }

  // Update input value when filter value changes
  useEffect(() => {
    setInputValue(filter.value ? String(filter.value) : '')
  }, [filter.value])



  // If filter is applied, show readable format
  if (filter.applied) {
    return (
      <div className="flex items-center justify-between gap-3 py-0 px-2 border rounded-lg bg-white border-[#E4E4E7]">
        <div className="flex items-center gap-1 w-[78%]">
          <span
            className="text-sm font-medium text-gray-800 line-clamp-2"
            dir={direction}
          >
            {getReadableFilterText(filter, availableFields)}
          </span>
        </div>
        <div className="flex gap-1 w-[20%]">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleVisibilityToggle}
            className="hover:bg-transparent p-1"
          >
            {filter?.isVisible ? (
              <Eye className="h-4 w-4" />
            ) : (
              <EyeOff className="h-4 w-4" />)}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRemoveFilter}
            className="hover:bg-transparent p-1"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  // Show filter editing form
  const isComplete =
    filter.field &&
    filter.operator &&
    filter.value !== null &&
    filter.value !== undefined

  return (
    <div dir={direction} className="flex flex-col items-center gap-2 p-3 border rounded-lg bg-gray-50">
      {/* Step 1: Field Selection */}
      <Select value={filter.field} onValueChange={handleFieldChange} dir={direction}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t('layerSettings.filters.selectField')} />
        </SelectTrigger>
        <SelectContent dir={direction}>
          {availableFields.map((field) => (
            <SelectItem key={field.name} value={field.name}>
              <div className="flex items-center justify-between w-full gap-1">
                <span className="text-xs rounded-sm text-[#5E58EE] bg-[#EFEEFD] px-1">{field.type}</span>
                <span>{field.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Step 2: Operation Selection (only show if field is selected) */}
      {filter.field && (
        <div className={`w-full ${getOperationsForFieldType(filter.dataType).length <= 1 ? 'hidden' : 'p-0 m-0'}`}>
          <Select
            value={(() => {
              const operations = getOperationsForFieldType(filter.dataType)
              const index = operations.findIndex(op =>
                op.value === filter.operator && (op.isNot || false) === (filter.isNot || false)
              )
              return index >= 0 ? index.toString() : '0'
            })()}
            onValueChange={handleOperationChange}
            dir={direction}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t("layerSettings.filters.select.operation")} />
            </SelectTrigger>
            <SelectContent>
              {getOperationsForFieldType(filter.dataType).map((op, index) => (
                <SelectItem key={index} value={index.toString()}>
                  {op.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Step 3: Value Input (only show if operation is selected) */}
      {filter.field && filter.operator && (
        <FilterValueInput
          filter={filter}
          inputValue={inputValue}
          onInputChange={setInputValue}
          onValueChange={handleValueChange}
        />
      )}

      {/* Action Buttons */}
      <div className="flex gap-2 mt-2 w-full" dir={direction}>
        {isComplete && (
          <Button
            variant="default"
            size="sm"
            onClick={handleApplyFilter}
            className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white w-[50%]"
          >
            {t('common.apply')}
          </Button>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-[#18181B] hover:text-gray-700 w-[50%]"
        >
          {t('common.delete')}
        </Button>
      </div>
    </div>
  )
}