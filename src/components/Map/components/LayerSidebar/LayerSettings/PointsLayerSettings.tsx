import { useTranslation } from 'react-i18next';
import { ColorPicker } from './ColorPicker';
import { TransparencySlider } from './TransparencySlider';
import { BorderSettings } from './BorderSettings';

export const PointsLayerSettings = () => {
    const { t } = useTranslation();

    return (
        <>
            <div className="space-y-4">
                <span className="text-sm font-medium">{t('layerSettings.layerColor')}</span>
                {/* Using default ColorPicker with layer integration */}
                <ColorPicker />
                <TransparencySlider />
            </div>
            <div className="border-t my-1" />
            <BorderSettings />
        </>
    );
};