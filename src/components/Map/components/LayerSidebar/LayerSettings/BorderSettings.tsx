import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { Button } from '@/components/ui/button';
import { ColorPicker } from './ColorPicker';
import { RangeSlider } from './RangeSlider';
import { CollapsibleSection } from '@/components/ui/CollapsibleSection';
import {
    toggleLayerBoundaries,
    setApplyingBorderSettings,
    setStrokeColor,
    setStrokeWidth,
    setStrokeOpacity
} from '@/shared/store/slices/layerSettingsSlice';
import { updateLayer } from '@/shared/store/slices/mapSlice';
import { showToast } from '@/shared/utils/toastConfig';
import { useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { CREATE_LAYER_SLD } from '@/shared/graphQl/mutations/layers';
import { useSearchParams } from 'react-router-dom';
import MapManager from '@/components/Map/core/MapManager';

export const BorderSettings = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get('orgId')
    const mapManager = MapManager.getInstance();

    const { isLayerBoundariesOpen, applyingBorderSettings, featureStyle } = useSelector(
        (state: RootState) => state.layerSettings
    );

    const [createLayerSld] = useMutation(CREATE_LAYER_SLD);

    // Find the SLD that matches the layer key
    const matchingSld = selectedMainLayer?.slds?.find(
        (sld: any) => sld.title === selectedMainLayer.layerKey
    );

    // Extract stroke values from selectedMainLayer's SLD
    useEffect(() => {
        if (selectedMainLayer?.layerKey && selectedMainLayer?.slds) {
            if (matchingSld?.featureStyle) {
                // Extract stroke properties from the matching SLD's featureStyle
                if (matchingSld.featureStyle.stroke_color) {
                    dispatch(setStrokeColor(matchingSld.featureStyle.stroke_color));
                }

                if (matchingSld.featureStyle.stroke_width !== undefined) {
                    dispatch(setStrokeWidth(matchingSld.featureStyle.stroke_width));
                }

                if (matchingSld.featureStyle.stroke_opacity !== undefined) {
                    dispatch(setStrokeOpacity(matchingSld.featureStyle.stroke_opacity));
                }
            }
        }
    }, [selectedMainLayer?.id, isLayerBoundariesOpen]);

    const applyBorderSettings = async () => {
        if (!selectedMainLayer?.id) {
            showToast.error(t('layerSettings.noLayerSelected') || 'No layer selected');
            return;
        }

        dispatch(setApplyingBorderSettings(true));

        try {
            const sldInput = {
                layerId: selectedMainLayer.id,
                orgId: parseInt(orgId || '0'),
                color: matchingSld?.featureStyle?.color || '#000000',
                opacity: matchingSld?.featureStyle?.opacity || 1,
                strokeColor: featureStyle.stroke_color,
                strokeWidth: featureStyle.stroke_width,
                strokeOpacity: featureStyle.stroke_opacity
            };

            const { data } = await createLayerSld({
                variables: {
                    dataInput: sldInput
                }
            });

            if (data?.createLayerSld?.sld?.id) {
                // Update the layer in Redux store with new SLD
                const updatedSlds = selectedMainLayer.slds.map((sld: any) =>
                    sld.title === selectedMainLayer.layerKey
                        ? {
                            ...sld,
                            id: data.createLayerSld.sld.id,
                            featureStyle: {
                                color: sldInput.color,
                                opacity: sldInput.opacity,
                                stroke_color: sldInput.strokeColor,
                                stroke_width: sldInput.strokeWidth,
                                stroke_opacity: sldInput.strokeOpacity
                            }
                        }
                        : sld
                );

                dispatch(updateLayer({
                    id: selectedMainLayer.id,
                    updates: {
                        slds: updatedSlds
                    }
                }));

                // Update WMS layer with new border settings
                mapManager.addOrUpdateWmsLayer({
                    wmsLayerName: selectedMainLayer.layerKey,
                });

                showToast.success(t('layerSettings.borderSettingsApplied') || 'Border settings applied successfully');
            }

        } catch (error) {
            console.error('Error updating border settings:', error);
            showToast.error(t('layerSettings.errorApplyingSettings') || 'Error applying border settings');
        } finally {
            dispatch(setApplyingBorderSettings(false));
        }
    };

    // Calculate opacity percentage from opacity (0-1 to 0-100)
    const currentOpacity = featureStyle?.stroke_opacity || 1;
    const opacityPercentage = Math.round(currentOpacity * 100);

    return (
        <CollapsibleSection
            title="حدود الطبقة"
            isOpen={isLayerBoundariesOpen}
            onToggle={() => dispatch(toggleLayerBoundaries())}
        >
            <div className="space-y-4">
                {/* Border Color Picker */}
                <ColorPicker
                    value={featureStyle?.stroke_color || '#000000'}
                    onChange={(color) => dispatch(setStrokeColor(color))}
                    useLayerIntegration={false}
                />

                {/* Border Opacity Slider */}
                <RangeSlider
                    label={t('layerSettings.transparency') || 'العتامة'}
                    value={opacityPercentage}
                    onChange={(opacityPercent) => {
                        const opacity = opacityPercent / 100;
                        dispatch(setStrokeOpacity(opacity));
                    }}
                    min={0}
                    max={100}
                    step={1}
                    unit="%"
                />

                {/* Border Width Slider */}
                <RangeSlider
                    label="السمك"
                    value={featureStyle?.stroke_width || 1}
                    onChange={(width) => dispatch(setStrokeWidth(width))}
                    min={0.5}
                    max={10}
                    step={0.5}
                    unit="px"
                />

                {/* Apply Button for Border Settings */}
                <div className="pt-4">
                    <Button
                        className="w-full bg-[#5E58EE] hover:bg-[#4A44D8] text-white disabled:opacity-50"
                        onClick={applyBorderSettings}
                        disabled={applyingBorderSettings}
                    >
                        {applyingBorderSettings ? t('common.loading') : t('layerSettings.apply')}
                    </Button>
                </div>
            </div>
        </CollapsibleSection>
    );
};