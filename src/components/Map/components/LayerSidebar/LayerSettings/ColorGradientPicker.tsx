import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from 'react-i18next';

interface ColorGradientPickerProps {
    value: string;
    onChange: (value: string) => void;
}

export const ColorGradientPicker: React.FC<ColorGradientPickerProps> = ({ value, onChange }) => {
    const { i18n } = useTranslation();
    const direction = i18n.dir();

    const gradients = [
        {
            id: 'yellow-pink',
            colors: 'linear-gradient(to right, #F9D423, #FF4E50)',
            label: 'Yellow to Pink'
        },
        {
            id: 'peach-purple',
            colors: 'linear-gradient(to right, #F9D423, #F83600, #A241E9)',
            label: 'Peach to Purple'
        },
        {
            id: 'lime-blue',
            colors: 'linear-gradient(to right, #C5F634, #1D98F2)',
            label: 'Lime to Blue'
        },
        {
            id: 'yellow-teal',
            colors: 'linear-gradient(to right, #F9D423, #02AABD)',
            label: 'Yellow to Teal'
        }
    ];

    const selectedGradient = gradients.find(g => g.id === value) || gradients[0];

    return (
        <div className="bg-gray-50 rounded-lg px-3">
            <Select dir={direction} value={value} onValueChange={onChange}>
                <SelectTrigger className="w-full no-border-focus">
                    <SelectValue>
                        <div className="flex items-center gap-2">
                            <div
                                className="w-52 h-5 rounded"
                                style={{ background: selectedGradient.colors }}
                            />
                        </div>
                    </SelectValue>
                </SelectTrigger>
                <SelectContent>
                    {gradients.map((gradient) => (
                        <SelectItem key={gradient.id} value={gradient.id} dir={direction}>
                            <div className="flex items-center gap-2" dir={direction}>
                                <div
                                    className="w-52 h-5 rounded"
                                    style={{ background: gradient.colors }}
                                />
                            </div>
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
};
