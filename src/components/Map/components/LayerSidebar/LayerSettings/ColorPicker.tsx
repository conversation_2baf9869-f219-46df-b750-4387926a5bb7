import MapManager from '@/components/Map/core/MapManager'
import { Input } from '@/components/ui/input'
import { CREATE_LAYER_SLD } from '@/shared/graphQl/mutations/layers'
import { useDebounce } from '@/shared/hooks/useDebounce'
import { RootState } from '@/shared/store'
import { SldType, updateLayer } from '@/shared/store/slices/mapSlice'
import { getColorBySldType, getOpacityBySldType } from '@/shared/utils/layerUtils'
import { useMutation } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'react-redux'
import { useSearchParams } from 'react-router-dom'

interface ColorPickerProps {
  value?: string
  onChange?: (color: string) => void
  useLayerIntegration?: boolean
}

export const ColorPicker = ({
  value,
  onChange,
  useLayerIntegration = true
}: ColorPickerProps) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const selectedMainLayer = useSelector(
    (state: RootState) => state.map.selectedMainLayer
  )
  const mapManager = MapManager.getInstance()
  const selectedColor = getColorBySldType(selectedMainLayer?.slds, SldType.POINTS);
  const opacity = getOpacityBySldType(selectedMainLayer?.slds, SldType.POINTS)

  // Use value prop if provided, otherwise use selectedColor
  const initialColor = value || selectedColor || '#000000'
  const [color, setColor] = useState(initialColor)
  const debouncedColor = useDebounce(color, 300)
  const [createLayerSld] = useMutation(CREATE_LAYER_SLD)
  const [searchParams] = useSearchParams()
  const orgId = searchParams.get('orgId')

  // Update color when value prop changes
  useEffect(() => {
    if (value && value !== color) {
      setColor(value)
    }
  }, [value])

  // Update color when selectedMainLayer changes (only for layer integration)
  useEffect(() => {
    if (useLayerIntegration && !value) {
      setColor(selectedColor || '#000000')
    }
  }, [selectedMainLayer?.id, useLayerIntegration, value, selectedColor])

  useEffect(() => {
    // If onChange prop is provided, use it (for custom usage like border color)
    if (onChange && color !== initialColor) {
      onChange(color)
      return
    }

    // If useLayerIntegration is true, update the layer (for default usage)
    if (useLayerIntegration && selectedMainLayer && color !== selectedColor) {
      createLayerSld({
        variables: {
          dataInput: {
            orgId: parseInt(orgId || '0'),
            layerId: selectedMainLayer.id,
            color: color,
            opacity: opacity || 1.0,
          },
        },
      }).then((response) => {
        const newSldId = response.data?.createLayerSld?.sld?.id
        if (newSldId) {
          // Update the layer's slds array with the actual color data
          const updatedSlds = selectedMainLayer.slds ? [...selectedMainLayer.slds] : []
          const existingSldIndex = updatedSlds.findIndex(sld => sld.id === newSldId)

          if (existingSldIndex >= 0) {
            // Update existing SLD with new color and id
            updatedSlds[existingSldIndex] = {
              ...updatedSlds[existingSldIndex],
              featureStyle: {
                ...updatedSlds[existingSldIndex].featureStyle,
                color: color
              }
            }
          }

          dispatch(
            updateLayer({
              id: selectedMainLayer.id,
              updates: { slds: updatedSlds },
            })
          )
        }
      })
      mapManager.addOrUpdateWmsLayer({
        wmsLayerName: selectedMainLayer.layerKey,
        wmsLayerId: selectedMainLayer.id,
        cqlFilter: selectedMainLayer.filters?.cql_filter,
        color: color,
        opacity: selectedMainLayer.opacity,
      })
    }
  }, [debouncedColor, onChange, useLayerIntegration])

  // Don't render if using layer integration but no layer is selected
  if (useLayerIntegration && !selectedMainLayer) return null

  return (
    <div className="flex justify-between items-center">
      <span className="text-sm">{t('layerSettings.color')}</span>
      <div className="bg-gray-50 rounded-md p-2">
        <div className="relative w-[220px] h-6 bg-gray-50 rounded-md overflow-hidden">
          <Input
            type="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            className="absolute inset-0 w-full h-full p-0 m-0 opacity-0 cursor-pointer z-10"
          />
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              backgroundColor: color,
              borderRadius: '6px',
            }}
          />
        </div>
      </div>
    </div>
  )
}