import { useTranslation } from 'react-i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { updateLayerBackgroundType } from '@/shared/store/slices/mapSlice';
import MapManager from '@/components/Map/core/MapManager';
import WmsRequestManager from '@/components/Map/services/WmsRequestManager';
import { extractNumberProperties, getHeatmapLayerConfig } from '@/components/Map/utils/layerSettingUtils';
import { Sld, SldType } from '@/shared/store/slices/mapSlice';

export const BackgroundTypeSelector = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch();

    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const properties = extractNumberProperties(selectedMainLayer?.jsonSchema);
    const isHeatmapDisabled = !properties || properties.length === 0;

    // Get the background type directly from the selected layer, default to 'points'
    const currentBackgroundType = selectedMainLayer?.selectedBackgroundType || 'points';

    const handleBackgroundTypeChange = (type: string) => {
        const backgroundType = type as 'points' | 'heatMap';

        // Update the layer's background type preference directly in mapSlice
        if (selectedMainLayer) {
            dispatch(updateLayerBackgroundType({
                layerId: selectedMainLayer.id,
                backgroundType: backgroundType
            }));
        }

        const mapManager = MapManager.getInstance();
        const wmsManager = WmsRequestManager.getInstance();

        if (mapManager && selectedMainLayer) {
            const heatmapSld = selectedMainLayer?.slds.find(
                (sld: Sld) => sld.sldType === SldType.HEATMAP
            )
            if (backgroundType === 'heatMap' && heatmapSld?.title) {
                // Set heatmap-specific config for the layer
                const heatmapConfig = getHeatmapLayerConfig();
                wmsManager.setLayerConfig(heatmapSld?.title, heatmapConfig);

                // Show heatmap layer with config
                mapManager.addOrUpdateWmsLayer({
                    wmsLayerName: `${selectedMainLayer?.layerKey}`,
                    wmsLayerId: selectedMainLayer?.id,
                    cqlFilter: selectedMainLayer.filters?.cql_filter,
                    customStyle: `${heatmapSld?.title}`,
                });
            } else if (backgroundType === 'points') {
                // Show regular layer
                if (heatmapSld?.title) {
                    mapManager.addOrUpdateWmsLayer({
                        wmsLayerName: `${selectedMainLayer?.layerKey}`,
                        wmsLayerId: selectedMainLayer?.id,
                        cqlFilter: selectedMainLayer.filters?.cql_filter,
                    });
                }
            }
        }
    };

    return (
        <div className="flex justify-between items-center">
            <span className="text-sm font-medium w-[25%]">{t('layerSettings.layerType')}</span>
            <div className="bg-none rounded-lg px-3 w-[80%]">
                <Select dir={direction} value={currentBackgroundType} onValueChange={handleBackgroundTypeChange}>
                    <SelectTrigger className="w-full no-border-focus">
                        <SelectValue placeholder={t('layerSettings.selectLayerType')} />
                        <span className="text-sm font-medium"></span>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="points">{t('layerSettings.points')}</SelectItem>
                        <SelectItem value="heatMap" disabled={isHeatmapDisabled}>
                            {t('layerSettings.heatMap')}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
};