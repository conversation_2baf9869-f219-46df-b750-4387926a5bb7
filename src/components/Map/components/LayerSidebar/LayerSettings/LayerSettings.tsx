import { useTranslation } from 'react-i18next';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { SettingLayerItem } from './index';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { initializeFromLayer, resetLayerSettings } from '@/shared/store/slices/layerSettingsSlice';
import { BackgroundTypeSelector } from './BackgroundTypeSelector';
import { HeatmapSettings } from './HeatmapSettings';
import { PointsLayerSettings } from './PointsLayerSettings';
import { FiltersSettings } from './FiltersSettings';

export const LayerSettings = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch();
    const [activeTab, setActiveTab] = useState('display');

    const { selectedMainLayer } = useSelector((state: RootState) => state.map);

    // Reset settings when layer changes, then initialize from new layer if available
    useEffect(() => {
        // Reset all settings first
        dispatch(resetLayerSettings());

        // Then initialize from the new layer if it has heatmap settings
        if (selectedMainLayer && selectedMainLayer?.selectedBackgroundType === "heatMap") {
            dispatch(initializeFromLayer(selectedMainLayer));
        }
    }, [selectedMainLayer?.id, selectedMainLayer?.selectedBackgroundType, selectedMainLayer?.slds, selectedMainLayer]);

    return (
        <div className={`h-full flex flex-col ${direction}`}>
            <ScrollArea className={`h-full flex-1 ${direction} custom-layerSetting-height`}>
                <div className="p-2 space-y-4 max-w-[290px] h-full">
                    {/* Layer Info Header */}
                    <SettingLayerItem />
                    <div className="border-t my-1" />

                    {/* Tabbed Interface */}
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir={direction}>
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="display">{t('layerSettings.display')}</TabsTrigger>
                            <TabsTrigger value="advanced">{t('layerSettings.advancedOptions')}</TabsTrigger>
                            <TabsTrigger value="filters" disabled={!selectedMainLayer?.jsonSchema}>{t('layerSettings.filtersTab')}</TabsTrigger>
                        </TabsList>

                        {/* Display Tab */}
                        <TabsContent value="display" className="space-y-4 mt-4">
                            <div className="p-2 space-y-4">
                                {/* Background Type Selector */}
                                <BackgroundTypeSelector />

                                {/* Conditional rendering based on selected background type */}
                                {selectedMainLayer?.selectedBackgroundType === 'heatMap' ? (
                                    <HeatmapSettings />
                                ) : (
                                    <PointsLayerSettings />
                                )}
                            </div>
                        </TabsContent>

                        {/* Advanced Options Tab */}
                        <TabsContent value="advanced" className="space-y-4 mt-4">
                            <div className="text-center text-muted-foreground py-8">
                                {t('layerSettings.advancedOptions')}
                            </div>
                        </TabsContent>

                        {/* Filters Tab */}
                        <TabsContent value="filters" className="space-y-4 mt-4">
                            <div className="p-2">
                                <FiltersSettings />
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>
            </ScrollArea>
        </div>
    );
};