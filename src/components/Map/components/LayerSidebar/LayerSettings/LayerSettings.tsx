import { useTranslation } from 'react-i18next';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SettingLayerItem } from './index';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { initializeFromLayer, resetLayerSettings } from '@/shared/store/slices/layerSettingsSlice';
import { BackgroundTypeSelector } from './BackgroundTypeSelector';
import { HeatmapSettings } from './HeatmapSettings';
import { PointsLayerSettings } from './PointsLayerSettings';

export const LayerSettings = () => {
    const { i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch();

    const { selectedMainLayer } = useSelector((state: RootState) => state.map);

    // Reset settings when layer changes, then initialize from new layer if available
    useEffect(() => {
        // Reset all settings first
        dispatch(resetLayerSettings());

        // Then initialize from the new layer if it has heatmap settings
        if (selectedMainLayer && selectedMainLayer?.selectedBackgroundType === "heatMap") {
            dispatch(initializeFromLayer(selectedMainLayer));
        }
    }, [selectedMainLayer?.id, selectedMainLayer?.selectedBackgroundType, selectedMainLayer?.slds]);

    return (
        <div className={`h-full flex flex-col ${direction}`}>
            <ScrollArea className={`flex-1 ${direction}`}>
                <div className="p-2 space-y-4">
                    {/* Layer Info Header */}
                    <SettingLayerItem />
                    <div className="border-t my-1" />

                    {/* Background Type Selector */}
                    <BackgroundTypeSelector />

                    {/* Conditional rendering based on selected background type */}
                    {selectedMainLayer?.selectedBackgroundType === 'heatMap' ? (
                        <HeatmapSettings />
                    ) : (
                        <PointsLayerSettings />
                    )}
                </div>
            </ScrollArea>
        </div>
    );
};