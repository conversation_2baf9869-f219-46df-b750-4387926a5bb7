import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ColorGradientPicker } from './ColorGradientPicker';
import { RangeSlider } from './RangeSlider';
import { DensityRangeSelector } from './DensityRangeSelector';
import { useMutation } from '@apollo/client';
import { CREATE_HEATMAP } from '@/shared/graphQl/mutations/layers';
import { showToast } from '@/shared/utils/toastConfig';
import { useSearchParams } from 'react-router-dom';
import MapManager from '@/components/Map/core/MapManager';
import WmsRequestManager from '@/components/Map/services/WmsRequestManager';
import { updateLayer } from '@/shared/store/slices/mapSlice';
import {
    setSelectedProperty,
    setSelectedGradient,
    setHeatmapRadius,
    setHeatmapIntensity,
    setDensityRangeType,
    setDensityRangeValues,
    setCreatingHeatmap
} from '@/shared/store/slices/layerSettingsSlice';
import { extractNumberProperties, getColorRangeFromGradient, getHeatmapLayerConfig } from '@/components/Map/utils/layerSettingUtils';

export const HeatmapSettings = () => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    const dispatch = useDispatch();
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');

    const {
        selectedProperty,
        selectedGradient,
        heatmapRadius,
        heatmapIntensity,
        densityRange,
        creatingHeatmap
    } = useSelector((state: RootState) => state.layerSettings);

    const { selectedMainLayer } = useSelector((state: RootState) => state.map);
    const properties = extractNumberProperties(selectedMainLayer?.jsonSchema);

    const mapManager = MapManager.getInstance();
    const wmsManager = WmsRequestManager.getInstance();

    const [createHeatMap] = useMutation(CREATE_HEATMAP, {
        onCompleted: (data) => {
            // Get the response data
            const newSldId = data?.createHeatMap?.sld?.id;
            const newTitle = data?.createHeatMap?.sld?.title
            if (newSldId && mapManager) {
                const updatedSlds = selectedMainLayer.slds ? [...selectedMainLayer.slds] : []
                const existingSldIndex = updatedSlds.findIndex(sld => sld.id === newSldId)
                const isValidDensityRange = densityRange &&
                    typeof densityRange === 'object' &&
                    'min' in densityRange &&
                    'max' in densityRange;
                if (existingSldIndex >= 0) {
                    // Update existing SLD with new color and id
                    updatedSlds[existingSldIndex] = {
                        ...updatedSlds[existingSldIndex],
                        featureStyle: {
                            ...updatedSlds[existingSldIndex].featureStyle,
                            radius: Math.min(heatmapRadius, 10),
                            opacity: heatmapIntensity / 100,
                            color_range: getColorRangeFromGradient(selectedGradient),
                            weight_field: selectedProperty,
                            data_weight_field: selectedProperty,
                            ...(isValidDensityRange && { density_range: densityRange })
                        }
                    }
                }
                // Configure layer-specific settings for the new heatmap layer
                const heatmapConfig = getHeatmapLayerConfig();
                wmsManager.setLayerConfig(newTitle, heatmapConfig);

                // Add or update the WMS layer with the heatmap key and config
                mapManager.addOrUpdateWmsLayer({
                    wmsLayerName: `${selectedMainLayer?.layerKey}`,
                    customStyle: `${newTitle}`
                });

                // Update the layer in Redux store with the updated SLDs
                dispatch(updateLayer({
                    id: parseInt(selectedMainLayer.id, 10),
                    updates: {
                        slds: updatedSlds
                    },
                }));

                showToast.success(t('layerSettings.heatmapCreated'));
                dispatch(setCreatingHeatmap(false));
            }
        },
        onError: (error) => {
            console.error('Error creating heatmap:', error);
            dispatch(setCreatingHeatmap(false));
        }
    });

    const applyHeatmapSettings = () => {
        if (!selectedMainLayer?.id) {
            showToast.error(t('layerSettings.noLayerSelected') || 'No layer selected');
            return;
        }

        // Check if property is selected
        if (!selectedProperty) {
            showToast.error(t('layerSettings.selectWeightPropertyError') || 'Please select a weight property');
            return;
        }

        dispatch(setCreatingHeatmap(true));

        const mutationInput = {
            orgId: parseInt(orgId || '0', 10),
            layerId: parseInt(selectedMainLayer.id, 10),
            weightField: selectedProperty,
            colorRange: getColorRangeFromGradient(selectedGradient),
            opacity: heatmapIntensity / 100,
            radius: Math.min(heatmapRadius, 10)
        } as any;

        // Only include densityRange if the range type is manual
        if (densityRange.type === 'manual') {
            mutationInput['densityRange'] = [densityRange.min, densityRange.max];
        }

        // Call the createHeatMap mutation
        createHeatMap({
            variables: {
                dataInput: mutationInput
            }
        });
    };

    return (
        <>
            <div className="flex justify-between items-center">
                <span className="text-sm font-medium w-[25%]">{t('layerSettings.weightBy')}</span>
                <div className="bg-none rounded-lg px-3 w-[80%]">
                    <Select
                        dir={direction}
                        value={selectedProperty}
                        onValueChange={(value) => dispatch(setSelectedProperty(value))}
                    >
                        <SelectTrigger className="w-full no-border-focus">
                            <SelectValue placeholder={t('layerSettings.selectProperty')} />
                            <span className="text-sm font-medium"></span>
                        </SelectTrigger>
                        <SelectContent>
                            {properties.map(property => (
                                <SelectItem key={property} value={property}>
                                    {property}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>
            <div className="border-t my-1" />
            <div className="space-y-4">
                <span className="text-sm font-medium">{t('layerSettings.colorGradient')}</span>
                <ColorGradientPicker
                    value={selectedGradient}
                    onChange={(value) => dispatch(setSelectedGradient(value))}
                />
                <RangeSlider
                    label={t('layerSettings.transparency')}
                    value={heatmapIntensity}
                    onChange={(value) => dispatch(setHeatmapIntensity(value))}
                    min={0}
                    max={100}
                    step={1}
                    unit="%"
                />
                <RangeSlider
                    label={t('layerSettings.heatmapRadius')}
                    value={heatmapRadius}
                    onChange={(value) => dispatch(setHeatmapRadius(value))}
                    min={1}
                    max={10}
                    step={1}
                    unit="%"
                />
                <DensityRangeSelector
                    rangeType={densityRange.type}
                    onRangeTypeChange={(type) => dispatch(setDensityRangeType(type))}
                    minValue={densityRange.min}
                    maxValue={densityRange.max}
                    onMinValueChange={(value) => dispatch(setDensityRangeValues({ min: value, max: densityRange.max }))}
                    onMaxValueChange={(value) => dispatch(setDensityRangeValues({ min: densityRange.min, max: value }))}
                />
            </div>
            <div className="p-2 sticky bottom-0 bg-white">
                <Button
                    className="w-full bg-[#5E58EE] hover:bg-[#5E58EE] text-white"
                    onClick={applyHeatmapSettings}
                >
                    {creatingHeatmap ? t('common.loading') : t('layerSettings.apply')}
                </Button>
            </div>
        </>
    );
};
