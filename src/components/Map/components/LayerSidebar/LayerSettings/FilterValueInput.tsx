import React from 'react'
import { useTranslation } from 'react-i18next'
import { useLazyQuery } from '@apollo/client'
import { useSelector } from 'react-redux'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import {
  AsyncMultiSelect,
  SelectOption,
} from '@/components/ui/AsyncMultiSelect'
import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { formatSelectValue, extractSelectValues } from '@/shared/utils/filterUtils'
import { GET_FIELD_VALUES } from '@/shared/graphQl/queries/layers'
import { RootState } from '@/shared/store/store'
import { useSearchParams } from 'react-router-dom'

interface FilterValueInputProps {
  filter: FilterCondition
  inputValue: string
  onInputChange: (value: string) => void
  onValueChange: (value: any) => void
}

export const FilterValueInput: React.FC<FilterValueInputProps> = ({
  filter,
  inputValue,
  onInputChange,
  onValueChange,
}) => {
  const { t, i18n } = useTranslation()
  const direction = i18n.dir()
  const [searchParams] = useSearchParams();
  const orgId = searchParams.get('orgId');
  const { selectedMainLayer } = useSelector((state: RootState) => state.map)

  const [getFieldValues] = useLazyQuery(GET_FIELD_VALUES)

  const loadFieldValues = async (
    fieldName: string,
    searchTerm: string,
    offset: number = 0
  ): Promise<{ options: SelectOption[], hasMore: boolean, total: number }> => {
    // Only call API if user has started searching
    if (!searchTerm || searchTerm.trim().length === 0) {
      return { options: [], hasMore: false, total: 0 }
    }

    if (!orgId || !selectedMainLayer?.id) {
      return { options: [], hasMore: false, total: 0 }
    }

    try {
      const limit = 10
      const { data } = await getFieldValues({
        variables: {
          layerId: selectedMainLayer.id,
          orgId: orgId,
          fieldName: `data__${fieldName}`,
          searchTerm,
          limit,
          offset
        }
      })

      const records = data?.records?.data || []
      const totalCount = data?.records?.count || 0
      const uniqueValues = new Set<string>()

      records.forEach((record: any) => {
        const value = record.data?.[fieldName]
        if (value !== null && value !== undefined &&
          String(value).toLowerCase().includes(searchTerm.toLowerCase())) {
          uniqueValues.add(String(value))
        }
      })

      const options = Array.from(uniqueValues).map(value => ({
        value,
        label: value
      }))

      const hasMore = (offset + limit) < totalCount

      return {
        options,
        hasMore,
        total: totalCount
      }
    } catch (error) {
      console.error('Error loading field values:', error)
      return { options: [], hasMore: false, total: 0 }
    }
  }

  if (!filter.field || !filter.operator) return null

  const fieldType = filter.dataType

  if (['exact', 'iexact'].includes(filter.operator)) {
    return (
      <AsyncMultiSelect
        placeholder={t('layerSettings.filters.enterValue')}
        loadOptions={(searchTerm, offset) => loadFieldValues(filter.field, searchTerm, offset)}
        value={formatSelectValue(filter.value)}
        onChange={(selected) => {
          onValueChange(extractSelectValues(selected))
        }}
        isMulti={true}
        className="flex-1 w-full no-border-focus"
        supportsPagination={true}
      />
    )
  }
  // For string fields with certain operations, show dropdown with existing values
  if (fieldType === 'string') {
    if (['isempty', 'isnull'].includes(filter.operator)) {
      // Set a value so the apply button appears
      if (!filter.value) {
        onValueChange("true")
      }
      return (
        <Input
          type="text"
          placeholder={t('layerSettings.filters.noInputRequired')}
          value="true"
          disabled={true}
          className="flex-1 bg-gray-100 hidden"
        />
      )
    }
  }

  // For numeric fields
  if (fieldType === "number" || fieldType === "integer") {
    // For range operator
    if (filter.operator === "range") {
      const parseRangeValue = (value: string): [number, number] => {
        try {
          const parsed = JSON.parse(value)
          return Array.isArray(parsed) && parsed.length === 2 ? [parsed[0], parsed[1]] : [0, 0]
        } catch {
          return [0, 0]
        }
      }

      const [minValue, maxValue] = filter.value ? parseRangeValue(filter.value) : [0, 0]

      return (
        <div className="flex gap-2 flex-1">
          <Input
            type="number"
            placeholder={t('layerSettings.filters.min')}
            value={minValue || ''}
            onChange={(e) => {
              const min = parseFloat(e.target.value) || 0
              const max = maxValue || 0
              const rangeValue = `[${min}, ${max}]`
              onInputChange(rangeValue)
              onValueChange(rangeValue)
            }}
            className="flex-1"
          />
          <Input
            type="number"
            placeholder={t('layerSettings.filters.max')}
            value={maxValue || ''}
            onChange={(e) => {
              const min = minValue || 0
              const max = parseFloat(e.target.value) || 0
              const rangeValue = `[${min}, ${max}]`
              onInputChange(rangeValue)
              onValueChange(rangeValue)
            }}
            className="flex-1"
          />
        </div>
      )
    }

    return (
      <Input
        type="number"
        placeholder={t('layerSettings.filters.enterValue')}
        value={inputValue}
        onChange={(e) => {
          onInputChange(e.target.value)
          const numValue = parseFloat(e.target.value)
          onValueChange(isNaN(numValue) ? null : numValue)
        }}
        className="flex-1"
      />
    )
  }

  // For date fields
  if (fieldType === 'date') {
    return (
      <Input
        type="date"
        placeholder={t('layerSettings.filters.enterValue')}
        value={inputValue}
        onChange={(e) => {
          onInputChange(e.target.value)
          onValueChange(e.target.value || null)
        }}
        className="flex-1"
      />
    )
  }

  // For logical fields
  if (fieldType === 'boolean') {
    return (
      <Select
        value={filter.value?.toString() || ''}
        onValueChange={onValueChange}
        dir={direction}
      >
        <SelectTrigger className="flex-1">
          <SelectValue placeholder={t('layerSettings.filters.enterValue')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="true"> {t('buttons.yes')}</SelectItem>
          <SelectItem value="false"> {t('buttons.no')}</SelectItem>
        </SelectContent>
      </Select>
    )
  }

  // Default: text input for string fields
  return (
    <Input
      type="text"
      placeholder={t('layerSettings.filters.enterValue')}
      value={inputValue}
      onChange={(e) => {
        onInputChange(e.target.value)
        onValueChange(e.target.value || null)
      }}
      className="flex-1"
    />
  )
}