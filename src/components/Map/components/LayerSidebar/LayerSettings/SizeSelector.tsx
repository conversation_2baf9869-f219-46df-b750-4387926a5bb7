
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { useTranslation } from "react-i18next";

export const SizeSelector = ({ size, unit, onSizeChange, onUnitChange }: { size: number, unit: string, onSizeChange: (size: number) => void, onUnitChange: (unit: string) => void }) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()

    return (
        <div className="flex justify-between items-center">
            <span className="text-sm">{t('layerSettings.size')}</span>
            <span className="text-sm">{t('layerSettings.size')}</span>
            <div className='flex items-center gap-2'>
                <div className="bg-gray-50 rounded-lg p-2">
                    <Input
                        type="number"
                        value={size}
                        onChange={(e) => onSizeChange(Number(e.target.value))}
                        className="w-16 h-8 border-0 bg-transparent text-center no-border-focus"
                    />
                </div>
                <Select dir={direction} value={unit} onValueChange={onUnitChange}>
                    <SelectTrigger className="w-20 h-8 border-0 bg-transparent no-border-focus">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="w-20">
                        <SelectItem value="meter">{t('layerSettings.units.meter')}</SelectItem>
                        <SelectItem value="kilometer">{t('layerSettings.units.kilometer')}</SelectItem>
                        <SelectItem value="centimeter">{t('layerSettings.units.centimeter')}</SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>
    )
};