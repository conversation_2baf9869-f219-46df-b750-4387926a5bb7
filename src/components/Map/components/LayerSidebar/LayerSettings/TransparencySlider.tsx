import { Input } from "@/components/ui/input";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { RootState } from "@/shared/store";
import { SldType, updateLayer } from "@/shared/store/slices/mapSlice";
import { useSelector, useDispatch } from "react-redux";
import { useMutation } from '@apollo/client';
import { CREATE_LAYER_SLD } from '@/shared/graphQl/mutations/layers';
import { useEffect, useState } from "react";
import MapManager from "@/components/Map/core/MapManager";
import { useDebounce } from "@/shared/hooks/useDebounce";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { formatNumber, roundNumber } from "@/shared/utils/formatters";
import { getOpacityBySldType, getColorBySldType } from "@/shared/utils/layerUtils";

export const TransparencySlider = () => {
    const selectedMainLayer = useSelector((state: RootState) => state.map.selectedMainLayer);
    const mapManager = MapManager.getInstance();
    const { t, i18n } = useTranslation();
    const dispatch = useDispatch();
    const [createLayerSld] = useMutation(CREATE_LAYER_SLD);
    const selectedOpacity = getOpacityBySldType(selectedMainLayer?.slds, SldType.POINTS);
    const color = getColorBySldType(selectedMainLayer?.slds, SldType.POINTS);
    const [opacity, setOpacity] = useState(selectedOpacity || 1);
    const debouncedOpacity = useDebounce(opacity, 1000);
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');

    useEffect(() => {
        setOpacity(selectedOpacity || 1)
    }, [selectedMainLayer?.id])

    useEffect(() => {
        if (!mapManager || !selectedMainLayer) return;

        if (opacity !== selectedOpacity) {
            createLayerSld({
                variables: {
                    dataInput: {
                        orgId: parseInt(orgId || '0'),
                        layerId: selectedMainLayer.id,
                        color: color || '#000000',
                        opacity: opacity,
                    },
                },
            }).then((response) => {
                const newSldId = response.data?.createLayerSld?.sld?.id;
                if (newSldId) {
                    // Update the layer's slds array with the actual opacity data
                    const updatedSlds = selectedMainLayer.slds ? [...selectedMainLayer.slds] : [];
                    const existingSldIndex = updatedSlds.findIndex(sld => sld.id === newSldId);

                    if (existingSldIndex >= 0) {
                        // Update existing SLD with new opacity and id
                        updatedSlds[existingSldIndex] = {
                            ...updatedSlds[existingSldIndex],
                            featureStyle: {
                                ...updatedSlds[existingSldIndex].featureStyle,
                                opacity: opacity
                            }
                        };
                    }

                    dispatch(
                        updateLayer({
                            id: selectedMainLayer.id,
                            updates: { slds: updatedSlds },
                        })
                    );
                }
            }).catch((err) => {
                console.error(err);
            });

            mapManager.addOrUpdateWmsLayer({
                wmsLayerName: selectedMainLayer.layerKey,
                color: color || '#000000',
                opacity: opacity,
            });
        }
    }, [debouncedOpacity, mapManager, selectedMainLayer, color, orgId]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        setOpacity(roundNumber(value, 1));
    };

    // Don't render if no layer is selected
    if (!selectedMainLayer) return null;

    return (
        <div className="flex justify-between items-center gap-3">
            <span className="text-sm">{t('layerSettings.transparency')}</span>
            <Slider
                dir={i18n.dir()}
                value={[opacity]}
                onValueChange={([v]) => {
                    setOpacity(roundNumber(v, 1));
                }}
                max={1}
                step={0.1}
            />
            <Input
                type="number"
                value={formatNumber(opacity, 1)}
                onChange={handleInputChange}
                className="w-16 h-8 no-border-focus"
                min={0}
                max={1}
                step={0.1}
            />
        </div>
    );
};
