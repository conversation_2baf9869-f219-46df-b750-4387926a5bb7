import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
interface DensityRangeSelectorProps {
    rangeType: 'automatic' | 'manual';
    onRangeTypeChange: (value: 'automatic' | 'manual') => void;
    minValue: number;
    maxValue: number;
    onMinValueChange: (value: number) => void;
    onMaxValueChange: (value: number) => void;
}

export const DensityRangeSelector: React.FC<DensityRangeSelectorProps> = ({
    rangeType,
    onRangeTypeChange,
    minValue,
    maxValue,
    onMinValueChange,
    onMaxValueChange
}) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();

    const handleMinValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onMinValueChange(Number(e.target.value));
    };

    const handleMaxValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onMaxValueChange(Number(e.target.value));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <span className="text-sm font-medium w-[25%]">{t('layerSettings.densityRange', 'نطاق الكثافة')}</span>
                <div className="bg-white  rounded-lg px-3 w-[80%]">
                    <Select
                        dir={direction}
                        value={rangeType}
                        onValueChange={(value) => onRangeTypeChange(value as 'automatic' | 'manual')}
                    >
                        <SelectTrigger className="w-full no-border-focus">
                            <SelectValue placeholder={t('layerSettings.selectRangeType', 'اختر نوع النطاق')} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="automatic">{t('layerSettings.automatic', 'تعيين تلقائي')}</SelectItem>
                            <SelectItem value="manual">{t('layerSettings.manual', 'تعيين يدوي')}</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            {rangeType === 'manual' && (
                <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                        <label className="text-sm text-gray-500">{t('layerSettings.minValue', 'القيمة الدنيا')}</label>
                        <input
                            type="number"
                            value={minValue}
                            onChange={handleMinValueChange}
                            className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm"
                            step="1"
                            min="0"
                            max="1000000000"
                        />
                    </div>
                    <div className="space-y-1">
                        <label className="text-sm text-gray-500">{t('layerSettings.maxValue', 'القيمة القصوى')}</label>
                        <input
                            type="number"
                            value={maxValue}
                            onChange={handleMaxValueChange}
                            className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm"
                            step="1"
                            min="0"
                            max="1000000000"
                        />
                    </div>
                </div>
            )}
        </div>
    );
};
