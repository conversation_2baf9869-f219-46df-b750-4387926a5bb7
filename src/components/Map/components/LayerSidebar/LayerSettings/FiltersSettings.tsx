import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'react-redux'
import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { RootState } from '@/shared/store'
import { updateLayer, FilterCondition } from '@/shared/store/slices/mapSlice'
import { FilterConditionRow } from './FilterConditionRow'
import { useMemo } from 'react'
import { buildLayerFilters } from '@/shared/utils/filters/filterBuilder'

export const FiltersSettings = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()

  const { selectedMainLayer } = useSelector((state: RootState) => state.map)

  // Get filters from the selected layer
  const filters = Array.isArray(selectedMainLayer?.filters?.graphql_filters) ? selectedMainLayer.filters.graphql_filters : []

  // Extract available fields from layer dataFields
  const availableFields = useMemo(() => {
    if (
      !selectedMainLayer?.dataFields ||
      !Array.isArray(selectedMainLayer.dataFields)
    )
      return []

    return selectedMainLayer.dataFields.map((field: any) => ({
      name: field.name || field.key || field.field,
      label:
        field.label || field.title || field.name || field.key || field.field,
      type: field.field_type,
    }))
  }, [selectedMainLayer?.dataFields])

  const handleAddFilter = () => {
    if (!selectedMainLayer?.id) return

    const newFilter: FilterCondition = {
      id: `filter_${Date.now()}`,
      field: '',
      operator: '',
      value: null,
      dataType: 'string',
      isVisible: true,
      isNot: false
    }

    // Maintain the proper filter structure
    const currentFilters = selectedMainLayer?.filters || {}
    const currentGraphqlFilters = Array.isArray(currentFilters.graphql_filters)
      ? currentFilters.graphql_filters
      : []

    dispatch(updateLayer({
      id: selectedMainLayer.id,
      updates: {
        filters: {
          ...currentFilters,
          graphql_filters: [...currentGraphqlFilters, newFilter]
        }
      }
    }))
  }

  const handleUpdateFilter = (
    filterId: string,
    updates: Partial<FilterCondition>
  ) => {
    if (!selectedMainLayer?.id) return

    const currentFilters = selectedMainLayer?.filters || {}
    const updatedGraphqlFilters = filters.map((filter: any) =>
      filter.id === filterId ? { ...filter, ...updates } : filter
    )

    // Build CQL filter from applied and visible filters
    const appliedVisibleFilters = updatedGraphqlFilters.filter(
      (f: FilterCondition) => f.applied && f.isVisible
    )
    const layerFilters = buildLayerFilters(appliedVisibleFilters)

    dispatch(updateLayer({
      id: selectedMainLayer.id,
      updates: {
        filters: {
          ...currentFilters,
          cql_filter: layerFilters.cql_filter,
          graphql_filters: updatedGraphqlFilters
        }
      }
    }))
  }

  const handleRemoveFilter = (filterId: string) => {
    if (!selectedMainLayer?.id) return

    const currentFilters = selectedMainLayer?.filters || {}
    const updatedGraphqlFilters = filters.filter(
      (f: FilterCondition) => f.id !== filterId
    )

    dispatch(updateLayer({
      id: selectedMainLayer.id,
      updates: {
        filters: {
          ...currentFilters,
          graphql_filters: updatedGraphqlFilters
        }
      }
    }))
  }

  // Check if there are any incomplete filters (not applied)
  const hasIncompleteFilters = filters?.some((filter: FilterCondition) => !filter.applied)

  if (filters.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-sm text-muted-foreground leading-relaxed px-2">
          {t('layerSettings.filters.description')}
        </div>

        <Button
          onClick={handleAddFilter}
          className="w-full bg-[#5E58EE] hover:bg-[#5E58EE] text-white"
          aria-label={t('layerSettings.filters.addFilter')}
        >
          <Plus className="h-4 w-4 mr-2" />
          <span>{t('layerSettings.filters.addFilter')}</span>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">
          {t('records')} ({selectedMainLayer?.recordsCount || 0})
        </span>
        <Button
          onClick={handleAddFilter}
          size="sm"
          variant="outline"
          disabled={hasIncompleteFilters}
          className={
            hasIncompleteFilters
              ? 'text-gray-400 border-gray-300 cursor-not-allowed'
              : 'text-[#5E58EE] border-[#5E58EE] hover:bg-[#5E58EE] hover:text-white'
          }
          title={
            hasIncompleteFilters
              ? t('layerSettings.filters.completeCurrentFilter')
              : ''
          }
        >
          <Plus className="h-4 w-4 mr-1" />
          {t('layerSettings.filters.addFilter')}
        </Button>
      </div>

      <div className="space-y-3">
        {filters.map((filter: FilterCondition) => (
          <FilterConditionRow
            key={filter.id}
            filter={filter}
            availableFields={availableFields}
            onUpdate={(updates: any) => handleUpdateFilter(filter.id, updates)}
            onRemove={() => handleRemoveFilter(filter.id)}
          />
        ))}
      </div>
    </div>
  )
}
