import { ScrollArea } from "@/components/ui/scroll-area";
import { useTranslation } from "react-i18next";

// Create a wrapper component for ScrollArea that handles RTL/LTR
export const DirectionalScrollArea = ({ className, children, ...props }: React.ComponentPropsWithoutRef<typeof ScrollArea>) => {
    const { i18n } = useTranslation();
    const direction = i18n.dir();

    return (
        <div className={direction === 'rtl' ? 'rtl-scrollbar' : 'ltr-scrollbar'}>
            <ScrollArea className={className} {...props}>
                {children}
            </ScrollArea>
        </div>
    );
};