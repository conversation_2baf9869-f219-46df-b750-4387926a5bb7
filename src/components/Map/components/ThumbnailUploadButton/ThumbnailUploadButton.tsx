import { Save, Check, Loader2, AlertCircle } from 'lucide-react'
import { useMapThumbnail } from '../../hooks/useMapThumbnail'
import MapManager from '../../core/MapManager'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import { Button } from '@/components/ui/button'

export const ThumbnailUploadButton = () => {
    const { t } = useTranslation()
    const mapManager = MapManager.getInstanceSafe()
    const { captureAndUpload, isUploading } = useMapThumbnail(mapManager)
    const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')

    const handleSave = async () => {
        try {
            setSaveStatus('idle')
            const result = await captureAndUpload()

            if (result?.uploaded) {
                setSaveStatus('success')
                setTimeout(() => setSaveStatus('idle'), 3000)
            } else {
                setSaveStatus('error')
                setTimeout(() => setSaveStatus('idle'), 3000)
            }
        } catch (error) {
            console.error('Manual save failed:', error)
            setSaveStatus('error')
            setTimeout(() => setSaveStatus('idle'), 3000)
        }
    }

    return (
        <Button
            onClick={handleSave}
            disabled={isUploading}
            variant="ghost"
            size="sm"
            className='rounded-full max-h-6 hover:bg-transparent'
        >
            {isUploading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
            ) : saveStatus === 'success' ? (
                <Check className="h-4 w-4 text-green-600" />
            ) : saveStatus === 'error' ? (
                <AlertCircle className="h-4 w-4 text-red-600" />
            ) : (
                <Save className="h-4 w-4" />
            )}
            <span >
                {isUploading
                    ? t('thumbnail.updating')
                    : saveStatus === 'success'
                        ? t('thumbnail.updated')
                        : saveStatus === 'error'
                            ? t('thumbnail.error')
                            : t('thumbnail.button')}
            </span>
        </Button>
    )
}
