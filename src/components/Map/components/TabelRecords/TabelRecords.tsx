import { useState, useCallback, useEffect, useRef } from "react"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { X, Loader2 } from 'lucide-react'
import { useQuery } from "@apollo/client"
import { GET_LAYER_RECORDS } from "@/shared/graphQl/queries/layers"
import { cleanAndParseJson, isImageUrl } from "@/shared/utils/tabelrecordHelper"
import { ImagePreview } from "@/components/RecordsTable/ImagePreview"
import { NestedTable } from "@/components/RecordsTable/NestedTable"
import { useTranslation } from "react-i18next"
export interface RecordData {
    sourceProperties: Record<string, any>;
}

interface InfiniteTableModalProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    layerId: number;
    title: string;
    color: string
}

export const InfiniteTableModal = ({ isOpen, onOpenChange, layerId, title, color }: InfiniteTableModalProps) => {
    const { i18n } = useTranslation()
    const direction = i18n.dir()
    const [records, setRecords] = useState<RecordData[]>([]);
    const [offset, setOffset] = useState(0);
    const [totalCount, setTotalCount] = useState(0);
    const loader = useRef(null);
    const LIMIT = 10;

    const { loading, refetch } = useQuery(GET_LAYER_RECORDS, {
        variables: {
            layerId,
            limit: LIMIT,
            offset: 0
        },
        skip: !isOpen || offset > totalCount + LIMIT || !layerId,
        onCompleted: (response) => {
            setRecords(response.records.data);
            setTotalCount(response.records.count);
        }
    });

    const hasMore = records.length < totalCount;

    const loadMore = useCallback(() => {
        if (loading || !hasMore) return;

        const nextOffset = offset + LIMIT;
        refetch({
            layerId,
            limit: LIMIT,
            offset: nextOffset
        }).then((response) => {
            setRecords(prev => [...prev, ...response.data.records.data]);
            setOffset(nextOffset);
        });
    }, [loading, hasMore, offset, layerId, refetch]);

    useEffect(() => {
        if (isOpen) {
            setRecords([]);
            setOffset(0);
            refetch({
                layerId,
                limit: LIMIT,
                offset: 0
            });
        }
    }, [isOpen, layerId, refetch]);

    useEffect(() => {
        const observer = new IntersectionObserver(
            entries => {
                if (entries[0].isIntersecting && hasMore) {
                    loadMore();
                }
            },
            { threshold: 0.5 }
        );

        const currentLoader = loader.current;
        if (currentLoader) {
            observer.observe(currentLoader);
        }

        return () => {
            if (currentLoader) {
                observer.unobserve(currentLoader);
            }
        };
    }, [loadMore, hasMore]);

    const displayValue = useCallback((value: any) => {

        if (value === null || value === undefined) {
            return '_';
        }
        if (!isNaN(value)) {
            return value.toString();
        }
        if (typeof value === 'string') {
            if (isImageUrl(value)) {
                return <ImagePreview src={value} />;
            }
            const parsedJson = cleanAndParseJson(value);
            if (parsedJson) {
                return <NestedTable data={parsedJson} />;
            }
            return value;
        }
        if (typeof value === 'object') {
            return <NestedTable data={value} />
        }

        return value;
    }, []);


    // Replace the existing columns definition with:
    const columns = records.length > 0
        ? Object.keys(records.find(record =>
            Object.keys(record?.sourceProperties || {}).length > 0
        )?.sourceProperties || {})
        : [];

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent dir={direction} className="max-w-[102rem] h-[85vh] flex flex-col [&>button]:hidden overflow-hidden">
                <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-2">
                        <div className={`w-5 h-5 rounded-sm`} style={{ backgroundColor: color }} />
                        <h2 className="text-xl font-bold"> {title}</h2>
                        {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => onOpenChange(false)}>
                        <X className="h-4 w-4" />
                    </Button>
                </div>

                <div className="flex-1 rounded-md flex flex-col min-h-0">
                    {/* Main container with horizontal scroll */}
                    <div className="overflow-x-auto">
                        <div className="min-w-fit">
                            {/* Sticky Header */}
                            <div className="sticky top-0 w-full border-b z-10">
                                <div className="grid" style={{
                                    gridTemplateColumns: `repeat(${columns.length}, minmax(200px, 1fr))`
                                }}>
                                    {columns.map((column) => (
                                        <div
                                            key={column}
                                            className={`p-4 font-medium ${direction} bg-background`}
                                        >
                                            {column}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Scrollable Content */}
                            <div className="overflow-y-auto">
                                <div className="min-w-full">
                                    {records.map((record, index) => (
                                        <div
                                            key={index}
                                            className="grid border-b"
                                            style={{
                                                gridTemplateColumns: `repeat(${columns.length}, minmax(200px, 1fr))`
                                            }}
                                        >
                                            {columns.map((column) => (
                                                <div
                                                    key={column}
                                                    className={`p-4 ${direction} break-words whitespace-normal`}
                                                >
                                                    {displayValue(record?.sourceProperties[column])}
                                                </div>
                                            ))}
                                        </div>
                                    ))}
                                </div>
                                <div ref={loader} className="h-8 flex items-center justify-center">
                                    {loading && <div className="text-sm text-muted-foreground">جار التحميل...</div>}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </DialogContent>
        </Dialog>
    );
};
