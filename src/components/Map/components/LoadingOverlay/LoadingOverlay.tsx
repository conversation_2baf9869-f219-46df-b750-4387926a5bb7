import { RootState } from '@/shared/store';
import { Loader2 } from 'lucide-react';
import { useSelector } from 'react-redux';

interface LoadingOverlayProps {
    isFeatureLoading: boolean;
}

export const LoadingOverlay = ({ isFeatureLoading }: LoadingOverlayProps) => {
    const { isWmsLoading } = useSelector((state: RootState) => state.map);

    return (
        <>
            {isFeatureLoading && !isWmsLoading && (
                <div className="absolute inset-0 z-50 flex items-center justify-center pointer-events-none">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
            )}
        </>
    )
}
