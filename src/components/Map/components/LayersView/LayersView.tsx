import { memo } from 'react'
import { useSelector } from "react-redux"
import { RootState } from "@/shared/store"
import { useTranslation } from "react-i18next"
import { ScrollArea } from "@/components/ui/scroll-area"

export const LayersView = memo(() => {
    const { t, i18n } = useTranslation()
    const { backendLayers: layers } = useSelector((state: RootState) => state.map)
    const direction = i18n.dir()

    return (
        <ScrollArea className="h-[calc(100vh-74px)]" >
            <div className="divide-y px-4">
                {layers.map((layer) => (
                    <div
                        key={layer.id}
                        className={`p-4 hover:bg-gray-50 cursor-pointer ${direction}`}
                    >
                        <h3 className="font-medium">{layer.title}</h3>
                        <div className="mt-1 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                                <div
                                    className={`w-4 h-4 rounded-sm`}
                                    style={{ backgroundColor: layer.color }}
                                />
                                <span>{t("layerActions.layerShape")}</span>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </ScrollArea>
    )
})

// For better debugging in React DevTools
LayersView.displayName = 'MemoizedLayersView'