import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Send, RotateCcw } from 'lucide-react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useMutation, useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';

// Components
import { Button } from '@/components/ui/button';
import { DotsLoader } from './DotsLoader';
import { ChatMessage } from './ChatMessage';
import { MultiSelectDropdown } from '@/components/OrgSettings/WorkspacePermissions/MultiSelectDropdown/MultiSelectDropdown';
import { ScrollArea } from '@/components/ui/scroll-area';

// Store and Hooks
import { RootState } from '@/shared/store';
import { setChatActiveLayers } from '@/shared/store/slices/mapSlice';

// GraphQL
import { SEND_MESSAGE_CHAT_AI, RESET_CHAT_AI } from '@/shared/graphQl/mutations/layers';
import { GET_LAYER_MESSAGES } from '@/shared/graphQl/queries/layers';

// Types and Constants
interface Message {
    id: string | number;
    role: 'user' | 'assistant';
    content: string;
    dbResult?: any;
}

enum QuestionType {
    DATA = 'DATA',
    GIS = 'GIS',
    DATA_ACTION = 'DATA_ACTION',
    GENERAL = 'GENERAL'
}

const MESSAGES_PER_PAGE = 10;

export const MapChatSidebar: React.FC = () => {
    const { t, i18n } = useTranslation();
    const dispatch = useDispatch();
    const { workspaceId } = useParams();
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get("orgId");
    // Redux state
    const { selectedMainLayer, chatActiveLayers, backendLayers: mainLayers } = useSelector(
        (state: RootState) => state.map
    );

    // Local state
    const [isLoading, setIsLoading] = useState(false);
    const [currentOffset, setCurrentOffset] = useState(0);
    const [messages, setMessages] = useState<Message[]>([]);
    const [input, setInput] = useState('');
    const [selectedLayerIds, setSelectedLayerIds] = useState<string[]>([]);

    // Refs
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const scrollAreaRef = useRef<HTMLDivElement>(null);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // GraphQL queries and mutations
    const { data: messageHistory, fetchMore, refetch } = useQuery(GET_LAYER_MESSAGES, {
        variables: {
            layersIds: chatActiveLayers.map(layer => layer.id),
            limit: MESSAGES_PER_PAGE,
            offset: currentOffset,
            orgId,
            workspaceId
        },
        skip: !chatActiveLayers.length || !workspaceId || !selectedMainLayer?.id || !selectedLayerIds?.length || !mainLayers?.length,
    });

    const [sendMessage] = useMutation(SEND_MESSAGE_CHAT_AI);
    const [resetChatAi, { loading: loadingReset }] = useMutation(RESET_CHAT_AI);

    // Derived state
    const totalBackendMessages = useMemo(() => messageHistory?.messages?.count || 0, [messageHistory]);
    const currentBackendMessages = useMemo(() => messages.length / 2, [messages]);

    // Layer options for dropdown
    const layerOptions = useMemo(() => mainLayers.map(layer => ({
        value: layer.id.toString(),
        label: layer.title
    })), [mainLayers]);

    // Initialize selectedLayerIds from Redux state
    useEffect(() => {
        if (chatActiveLayers.length > 0) {
            setSelectedLayerIds(chatActiveLayers.map(layer => layer.id.toString()));
        } else if (selectedMainLayer) {
            setSelectedLayerIds([selectedMainLayer.id.toString()]);
            dispatch(setChatActiveLayers([selectedMainLayer]));
        } else {
            setSelectedLayerIds([]);
        }
    }, [chatActiveLayers, selectedMainLayer, workspaceId]);

    // Reset messages when active layers change
    useEffect(() => {
        setMessages([]);
        setCurrentOffset(0);
    }, [chatActiveLayers]);

    // Load message history
    useEffect(() => {
        if (messageHistory?.messages?.data) {
            const historicalMessages = messageHistory.messages.data.slice()
                .reverse().flatMap((msg: any) => [
                    { id: msg.id, role: 'user', content: msg.message.userInput, dbResult: msg.message.sqlResult },
                    { id: `${msg.id}_response`, role: 'assistant', content: msg.message.finalResult, dbResult: msg.message.sqlResult }
                ]);
            setMessages(prev => [...historicalMessages, ...prev]);
        }
    }, [messageHistory]);

    // Scroll to bottom for new messages
    useEffect(() => {
        if (currentOffset === 0) {
            scrollToBottom();
        }
    }, [messages]);

    // Scroll functions
    const scrollToTop = useCallback(() => {
        if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = 0;
        }
    }, []);

    const scrollToBottom = useCallback(() => {
        if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
    }, []);

    // Event handlers
    const handleLoadMore = useCallback(() => {
        const newOffset = currentOffset + MESSAGES_PER_PAGE;
        setCurrentOffset(newOffset);

        fetchMore({
            variables: {
                layersIds: chatActiveLayers.map(layer => layer.id),
                offset: newOffset,
                limit: MESSAGES_PER_PAGE,
                orgId,
                workspaceId
            },
            updateQuery: (prev, { fetchMoreResult }) => {
                if (!fetchMoreResult) return prev;
                return {
                    messages: {
                        ...fetchMoreResult.messages,
                    },
                };
            },
        }).then(() => {
            scrollToTop();
        });
    }, [chatActiveLayers, currentOffset, fetchMore, scrollToTop, orgId, workspaceId]);

    const handleLayerSelectionChange = useCallback((selectedValues: string[]) => {
        setSelectedLayerIds(selectedValues);

        const selectedLayers = mainLayers.filter(layer =>
            selectedValues.includes(layer.id.toString())
        );

        dispatch(setChatActiveLayers(selectedLayers));
    }, [dispatch, mainLayers]);

    const resetChat = useCallback(async () => {
        if (chatActiveLayers.length === 0) {
            setMessages([]);
            setCurrentOffset(0);
            return;
        }

        try {
            await resetChatAi({
                variables: {
                    dataInput: {
                        orgId,
                        workspaceId,
                        layersIds: chatActiveLayers.map(layer => layer.id)
                    }
                }
            });

            setMessages([]);
            setCurrentOffset(0);
            await refetch();
        } catch (error) {
            console.error('Error resetting chat:', error);
        }
    }, [chatActiveLayers]);

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) e.preventDefault();
        if (!input.trim() || chatActiveLayers.length === 0) return;

        const newUserMessage: Message = {
            id: Date.now(),
            role: 'user',
            content: input
        };

        setMessages(prev => [...prev, newUserMessage]);
        setInput('');
        setIsLoading(true);

        // Reset textarea height
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
        }

        try {
            const { data } = await sendMessage({
                variables: {
                    inputForm: {
                        layersIds: chatActiveLayers.map(layer => layer.id),
                        question: input,
                        questionType: QuestionType.DATA,
                        orgId,
                        workspaceId
                    }
                }
            });

            const assistantMessage: Message = {
                id: Date.now() + 1,
                role: 'assistant',
                content: data.sendMessageChatAi.response,
                dbResult: data.sendMessageChatAi.dbResult
            };

            setMessages(prev => [...prev, assistantMessage]);
        } catch (error) {
            console.error('Error sending message:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
    };

    const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);

        // Auto-resize the textarea
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
        }
    };

    return (
        <div className="flex h-full flex-col">
            {/* Header with layer selection */}
            <div className="flex items-center p-2 border-b">
                <div className="flex-1 flex flex-col">
                    <div className="flex items-center justify-between mb-2 flex-wrap">
                        <div className="w-fit">
                            <MultiSelectDropdown
                                options={layerOptions}
                                selectedValues={selectedLayerIds}
                                onSelect={handleLayerSelectionChange}
                                placeholder={t('selectLayers')}
                                showSelectAll={true}
                                selectAllLabel={t('selectAll')}
                                showSearch={true}
                            />
                        </div>
                        <Button
                            variant="outline"
                            onClick={resetChat}
                            className="flex items-center gap-2 hover:text-gray-900 rounded-lg"
                            disabled={isLoading}
                        >
                            <RotateCcw className="h-4 w-4" />
                            {t('reset')}
                        </Button>
                    </div>
                </div>
            </div>

            {/* Message history */}
            <ScrollArea className="flex-1 p-4 custom-scroll-area" ref={scrollAreaRef} dir={i18n.dir()}>
                {!loadingReset && totalBackendMessages > currentBackendMessages && (
                    <Button
                        variant="outline"
                        className="w-full mb-4"
                        onClick={handleLoadMore}
                        disabled={totalBackendMessages <= currentBackendMessages}
                    >
                        {t('loadMore')} ({totalBackendMessages - currentBackendMessages} {t('remaining')})
                    </Button>
                )}

                <div className="space-y-3">
                    {messages.map((message, index) => (
                        <ChatMessage
                            key={`${message.id}-${index}`}
                            role={message.role}
                            content={message.content}
                            dbResult={message.dbResult}
                        />
                    ))}

                    {isLoading && (
                        <div className="mb-4 mr-8">
                            <DotsLoader />
                        </div>
                    )}
                </div>

                <div ref={messagesEndRef} />
            </ScrollArea>

            {/* Message input */}
            <div className="border-t p-3">
                <div className={`relative flex`} >
                    <textarea
                        ref={textareaRef}
                        placeholder={selectedLayerIds.length > 0
                            ? t('askAnything')
                            : t('selectLayersFirst')}
                        value={input}
                        onChange={handleTextareaChange}
                        onKeyDown={handleKeyDown}
                        disabled={selectedLayerIds.length === 0 || isLoading}
                        rows={1}
                        className={`w-full py-2 px-3 resize-none outline-none border rounded-md max-h-[150px] min-h-[44px] ${i18n.dir()}`}
                    />
                    <button
                        onClick={() => handleSubmit()}
                        disabled={selectedLayerIds.length === 0 || !input.trim() || isLoading}
                        className="absolute bottom-2 rtl:left-2 ltr:right-2 rounded-full p-1 disabled:opacity-50 disabled:cursor-not-allowed"
                        aria-label="Send message"
                    >
                        <Send
                            size={18}
                            className={selectedLayerIds.length > 0 && input.trim() && !isLoading
                                ? "text-primary"
                                : "text-gray-400 hover:text-gray-600"
                            }
                        />
                    </button>
                </div>
            </div>
        </div>
    );
};