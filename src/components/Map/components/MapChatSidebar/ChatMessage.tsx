import { cn } from '@/lib/utils'

interface ChatMessageProps {
    role: 'user' | 'assistant'
    content: string
    dbResult?: any
}

export const ChatMessage = ({ role, content, dbResult }: ChatMessageProps) => {
    const isTableData = (data: any): data is Array<Record<string, any>> => {
        return Array.isArray(data) && data.length > 0 && typeof data[0] === 'object'
    }

    const getTableHeaders = (data: Array<Record<string, any>>) => {
        return Object.keys(data[0])
    }

    return (
        <div className={cn("mb-4", role === 'assistant' ? 'mr-8' : 'ml-8')}>
            <div className={cn(
                "rounded-lg p-4",
                role === 'assistant' ? 'bg-white border-[1px] border-[#E1E1E1]' : 'bg-[#E1E1E1]'
            )}>
                <div>{content}</div>
                {role === 'assistant' && dbResult && isTableData(dbResult) && (
                    <div className="mt-4 overflow-x-auto">
                        <table className="min-w-full border-collapse">
                            <thead>
                                <tr className="bg-gray-50">
                                    {getTableHeaders(dbResult).map((header) => (
                                        <th key={header} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {header}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {dbResult.map((row, rowIndex) => (
                                    <tr key={rowIndex}>
                                        {Object.values(row).map((value, colIndex) => (
                                            <td key={colIndex} className="p-2 text-sm text-gray-600 ltr">
                                                {(() => {
                                                    let displayValue = value;
                                                    // Try parsing if the value is a stringified JSON
                                                    if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
                                                        try {
                                                            displayValue = JSON.parse(value);
                                                            if (typeof displayValue === 'object') {
                                                                return JSON.stringify(displayValue, null, 2);
                                                            }
                                                        } catch {
                                                            displayValue = value;
                                                        }
                                                    }
                                                    if (typeof displayValue === 'string' &&
                                                        (displayValue.includes('T') || displayValue.match(/^\d{4}-\d{2}-\d{2}/))) {
                                                        const date = new Date(displayValue);
                                                        if (!isNaN(date.getTime())) {
                                                            return date.toLocaleString();
                                                        }
                                                    }
                                                    return String(displayValue);
                                                })()}
                                            </td>
                                        ))}
                                    </tr>
                                )
                                )}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </div>
    )
}