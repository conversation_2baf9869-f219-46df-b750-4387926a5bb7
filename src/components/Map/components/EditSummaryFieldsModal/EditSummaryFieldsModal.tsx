import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@apollo/client";
import { GET_WORKSPACE_SummaryFields } from "@/shared/graphQl/queries/layers";
import { UPDATE_LAYER_SUMMARY_FIELDS } from "@/shared/graphQl/mutations/layers";
import { CustomForm } from "@/components/RJSF/theme/CustomTheme";
import { Button } from "@/components/ui/button";
import { generateDynamicSchema, generateDynamicUiSchema } from '@/components/MultiStepForm/schemas/stepFiveSchema';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
    createColumnSamples,
    createColumnMappings,
    updateSchemaWithAvailableColumns,
    getSelectedColumnNames
} from "@/shared/utils/formUtils";
import { useParams, useSearchParams } from "react-router-dom";
import { X } from "lucide-react";
import { showToast } from "@/shared/utils/toastConfig";
import { useOrganizationManager } from "@/shared/hooks/useOrganizationManager";
import { useTranslation } from "react-i18next";

interface EditSummaryFieldsModalProps {
    isOpen: boolean;
    onClose: () => void;
    layerId: string;
    onCloseAlert: () => void;
}

export const EditSummaryFieldsModal = ({ isOpen, onClose, layerId, onCloseAlert }: EditSummaryFieldsModalProps) => {
    const [formSchema, setFormSchema] = useState({});
    const [uiSchema, setUiSchema] = useState({});
    const [columnSamples, setColumnSamples] = useState<Record<string, string[]>>({});
    const [formData, setFormData] = useState<any>({});
    const { workspaceId } = useParams()
    const [updateSummaryFields, { loading: mutationLoading }] = useMutation(UPDATE_LAYER_SUMMARY_FIELDS);
    const { selectedOrg } = useOrganizationManager()
    const { t, i18n } = useTranslation();
    const direction = i18n.dir()
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');

    const { data: summaryFieldsData, loading } = useQuery(GET_WORKSPACE_SummaryFields, {
        variables: { workspaceId: parseInt(workspaceId!), orgId: selectedOrg?.id, pk: parseInt(layerId) },
        skip: !workspaceId || !selectedOrg?.id,
    });

    useEffect(() => {
        if (summaryFieldsData?.layers?.data?.[0]) {
            const layerData = summaryFieldsData.layers.data[0];
            const summaryFields = layerData.data.summary_fields || [];

            let samples: Record<string, string[]> = {};
            let mappings: any[] = [];
            let columnCount = 0;

            // Check if this is a design layer (no dataset) or regular layer (with dataset)
            if (layerData.dataset && layerData.sampleData) {
                // Case 1: Regular layer with dataset and sample data
                samples = createColumnSamples({ datasetSampleData: { data: layerData.sampleData } });
                mappings = createColumnMappings({ datasetSampleData: { data: layerData.sampleData } });
                columnCount = mappings.length > 6 ? 6 : mappings.length;
            } else {
                // Case 2: Design layer without dataset
                // Use columns from the layer data itself
                const availableColumns = layerData.data.columns || [];

                // Create mappings for design layer columns
                mappings = availableColumns.map((column: string) => ({
                    column: column,
                    displayName: column,
                    title: column
                }));

                // Create sample data for UI rendering (empty samples)
                availableColumns.forEach((column: string) => {
                    samples[column] = [t('noSampleDataAvailable') || "No sample data available"];
                });

                columnCount = availableColumns.length > 6 ? 6 : availableColumns.length;
            }

            setColumnSamples(samples);

            // Generate dynamic schema based on available columns (max 6)
            const dynamicSchema = generateDynamicSchema(columnCount);
            const dynamicUiSchema = generateDynamicUiSchema(columnCount);

            // Update schema with available columns
            const updatedSchema = updateSchemaWithAvailableColumns(
                dynamicSchema,
                formData,
                mappings,
                samples,
                'advanced'
            );

            setFormSchema(updatedSchema);
            setUiSchema(dynamicUiSchema);

            // Only set initial form data when summaryFieldsData changes
            if (!Object.keys(formData).length) {
                const initialFormData = summaryFields.reduce((acc: any, field: string, index: number) => {
                    acc[`longitude${index + 1}`] = field;
                    return acc;
                }, {});
                setFormData(initialFormData);
            }
        }
    }, [summaryFieldsData, formData]);

    const handleChange = ({ formData: newFormData }: any) => {
        setFormData(newFormData);
    };

    const handleSubmit = async ({ formData }: { formData: any }) => {
        try {
            const selectedFields = getSelectedColumnNames(formData);

            const result = await updateSummaryFields({
                variables: {
                    dataInput: {
                        layerId,
                        mapDataColumns: JSON.stringify(selectedFields),
                        orgId
                    }
                }
            });

            if (result.data?.updateRecordsSummaryFields?.success) {
                showToast.success(t('alerts.success.summaryFieldsUpdateProgress'));
                onClose();
                onCloseAlert()
            }
        } catch (error: any) {
            showToast.error(t('alerts.error.summaryFields'));
            console.error('Error updating summary fields:', error);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose} >
            <DialogContent dir={direction} className={`max-w-[100rem] h-[85vh] [&>button]:hidden ${direction}`}>
                <DialogHeader className={direction}>
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                            <DialogTitle>{t('editSummaryFields')}</DialogTitle>
                        </div>
                        <Button variant="ghost" size="icon" onClick={onClose}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </DialogHeader>
                {loading ? (
                    <div>{t('loading')}</div>
                ) : (
                    <CustomForm
                        schema={formSchema}
                        uiSchema={uiSchema}
                        onSubmit={handleSubmit}
                        onChange={handleChange}
                        formContext={{ columnSamples }}
                        formData={formData}
                    >
                        <div className={`flex gap-4 mt-6 ${direction}`}>
                            <Button
                                type="submit"
                                className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white"
                                disabled={mutationLoading}
                            >
                                {mutationLoading ? t('updating') : t('update')}
                            </Button>
                            <Button variant="outline" onClick={onClose}>
                                {t('cancel')}
                            </Button>
                        </div>
                    </CustomForm>
                )}
            </DialogContent>
        </Dialog>
    );
};