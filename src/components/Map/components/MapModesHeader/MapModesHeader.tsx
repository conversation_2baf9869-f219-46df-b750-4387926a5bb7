import { Layers, Database } from "lucide-react"
import { useSelector } from 'react-redux'
import { MapMode } from '@/shared/store/slices/mapSlice'
import { useTranslation } from 'react-i18next'
import { RootState } from '@/shared/store'
import { WorkspaceDropdown } from "@/components/WorkspaceDropdown/WorkspaceDropdown"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

interface MapModesHeaderProps {
    onMapModeChange: (mode: MapMode) => void;
}

export const MapModesHeader: React.FC<MapModesHeaderProps> = ({ onMapModeChange }) => {
    const { t, i18n } = useTranslation()
    const direction = i18n.dir()
    const currentMode = useSelector((state: RootState) => state.map.mapMode)
    const selectedWorkspace = useSelector((state: RootState) => state.workspace.selectedWorkspace)
    const layersCount = selectedWorkspace?.layersData?.layersCount || 0
    const recordsCount = selectedWorkspace?.layersData?.recordsCount || 0

    return (
        <div className={`bg-gray-50 border-b ${direction}`}>
            <div className={`flex justify-between items-center px-4 py-1 ${direction}`}>
                {/* Workspace Title Section */}
                <div className="flex-1">
                    <WorkspaceDropdown />
                    <p className="text-sm text-gray-500 my-1 line-clamp-1">{selectedWorkspace?.description}</p>
                </div>

                {/* Controls Section */}
                <div className="flex-1 flex justify-center items-center space-x-1 gap-1 ">
                    {/* <Button variant="ghost" size="icon">
                        <Settings className="h-5 w-5" />
                    </Button> */}
                    <Tabs value={currentMode} onValueChange={(value) => onMapModeChange(value as MapMode)}>
                        <TabsList>
                            <TabsTrigger
                                value="exploratory"
                            >
                                {t('mapModes.exploratory')}
                            </TabsTrigger>
                            <TabsTrigger
                                value="details"
                            >
                                {t('mapModes.details')}
                            </TabsTrigger>
                        </TabsList>
                    </Tabs>

                </div>

                {/* Layers and Records Section */}
                <div className="flex-1 flex justify-end items-center space-x-6">
                    <div className="flex items-center space-x-2 gap-1">
                        <Layers className="h-5 w-5 text-gray-600" />
                        <div className="flex items-center space-x-2 gap-1">
                            <span className="text-sm font-medium text-gray-600">{t('layers')}</span>
                            <span className="text-lg font-bold">{layersCount}</span>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2 gap-1">
                        <Database className="h-5 w-5 text-gray-600" />
                        <div className="flex items-center space-x-2 gap-1">
                            <span className="text-sm font-medium text-gray-600">{t('records')}</span>
                            <span className="text-lg font-bold">{recordsCount}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div >
    )
}
