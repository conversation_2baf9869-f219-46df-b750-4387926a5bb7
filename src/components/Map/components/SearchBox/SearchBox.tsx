import React, { useState, useEffect, useRef, ChangeEvent } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import MapManager from '../../core/MapManager';
import { Search, X } from 'lucide-react';
import { transform } from 'ol/proj';
import { useTranslation } from 'react-i18next';
import { DirectionalScrollArea } from '../LayerSidebar/DirectionalScrollArea';

interface PlacePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

interface PlaceResult {
  geometry?: {
    location?: google.maps.LatLng;
  };
}

export const SearchBox: React.FC = () => {
  const [searchValue, setSearchValue] = useState<string>('');
  const [predictions, setPredictions] = useState<PlacePrediction[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [focus, setFocus] = useState<boolean>(false);

  const { t, i18n } = useTranslation();
  const direction = i18n.dir();

  // More explicit typing for service refs
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  const mapManager = MapManager.getInstance();

  useEffect(() => {
    // Type guard to ensure google is available
    if (typeof window !== 'undefined' && window.google?.maps?.places) {
      autocompleteService.current = new window.google.maps.places.AutocompleteService();
      const dummyDiv = document.createElement('div');
      placesService.current = new window.google.maps.places.PlacesService(dummyDiv);
    }
  }, []);

  const handleCoordinateSearch = (value: string): boolean => {
    // Trim whitespace from input
    const trimmedValue = value.trim();

    // 1. Check for direction-based format (e.g., "28.3838° N, 36.5662° E")
    const directionRegex = /(\d+\.?\d*)\s*°?\s*([NS])\s*,\s*(\d+\.?\d*)\s*°?\s*([EW])/i;
    const directionMatch = trimmedValue.match(directionRegex);

    if (directionMatch) {
      const [, latDeg, latDir, lonDeg, lonDir] = directionMatch;
      let lat = parseFloat(latDeg);
      let lon = parseFloat(lonDeg);

      // Adjust for direction (S/W = negative)
      if (latDir.toUpperCase() === 'S') lat = -lat;
      if (lonDir.toUpperCase() === 'W') lon = -lon;

      // Validate coordinate ranges
      if (Math.abs(lat) > 90 || Math.abs(lon) > 180) {
        console.error("Invalid coordinates: Latitude must be [-90,90], Longitude [-180,180]");
        return false;
      }

      const transformedCoords = transform([lon, lat], 'EPSG:4326', 'EPSG:3857');
      animateMapToCoordinates(transformedCoords);
      return true;
    }

    // 2. Check for plain coordinates (e.g., "36.5662, 28.3838" - assumed Lon,Lat)
    const plainCoordRegex = /^(-?\d+\.?\d*)\s*,\s*(-?\d+\.?\d*)$/;
    const plainMatch = trimmedValue.match(plainCoordRegex);

    if (plainMatch) {
      const lon = parseFloat(plainMatch[1]);
      const lat = parseFloat(plainMatch[2]);

      // Validate coordinate ranges
      if (Math.abs(lat) > 90 || Math.abs(lon) > 180) {
        console.error("Invalid coordinates: Latitude must be [-90,90], Longitude [-180,180]");
        return false;
      }

      const transformedCoords = transform([lon, lat], 'EPSG:4326', 'EPSG:3857');
      animateMapToCoordinates(transformedCoords);
      return true;
    }

    // No valid format found
    console.error("Invalid format. Use: 'Lat° N, Lon° E' or 'Lon, Lat'");
    return false;
  };

  // Helper function to avoid code duplication
  const animateMapToCoordinates = (coords: number[]) => {
    const view = mapManager.getView();
    if (view) {
      view.animate({
        center: coords,
        zoom: 15,
        duration: 3000
      });
      mapManager.addSearchMarker(coords);
    }
  };

  const handleSearch = (value: string): void => {
    setSearchValue(value);

    // Try coordinate search first
    if (handleCoordinateSearch(value)) {
      setPredictions([]);
      return;
    }

    // Fall back to Google Places search
    if (!value || !autocompleteService.current) {
      setPredictions([]);
      return;
    }

    autocompleteService.current.getPlacePredictions(
      {
        input: value,
        componentRestrictions: { country: "sa" },
        types: ["geocode"],
      },
      (
        results: PlacePrediction[] | null,
        status: google.maps.places.PlacesServiceStatus
      ) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          setPredictions(results);
        } else {
          setPredictions([]);
        }
      }
    );
  };

  const handlePlaceSelection = (prediction: PlacePrediction): void => {
    if (!placesService.current) return;

    placesService.current.getDetails(
      { placeId: prediction.place_id },
      (place: PlaceResult | null, status: google.maps.places.PlacesServiceStatus) => {
        if (
          status === google.maps.places.PlacesServiceStatus.OK &&
          place?.geometry?.location
        ) {
          const lat = place.geometry.location.lat();
          const lng = place.geometry.location.lng();

          const transformedCoords = transform([lng, lat], 'EPSG:4326', 'EPSG:3857');

          const view = mapManager.getView();
          if (view) {
            view.animate({
              center: transformedCoords,
              zoom: 13,
              duration: 3400
            });
            mapManager.addSearchMarker(transformedCoords);
          }

          setSearchValue(prediction.description);
          setPredictions([]);
        }
      }
    );
  };

  // Add keyboard handler function
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < predictions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        if (selectedIndex >= 0 && predictions[selectedIndex]) {
          handlePlaceSelection(predictions[selectedIndex]);
        }
        break;
    }
  };

  // Calculate dynamic width based on focus and search value
  const searchBoxWidth = focus || searchValue ? 'w-80' : 'w-48';

  return (
    <div
      className={`absolute top-4 rtl:-translate-x-1/2 ltr:translate-x-1/2 ${!focus ? 'w-[200px] ltr:left-[14rem] rtl:right-[14rem]' : 'w-[300px] rtl:right-[10.8rem] ltr:left-[10.8rem]'} z-10`}
    >
      <div className="relative">
        {/* Main search input */}
        <div className={`
                    flex items-center 
                    bg-white/90 backdrop-blur-sm 
                    rounded-lg 
                    ${searchValue ? 'rounded-bl-none rounded-br-none' : ''}
                    border border-gray-200/50
                    transition-all duration-300 ease-in-out
                    ${searchBoxWidth}
                    h-10
                `}>
          <Input
            type="text"
            value={searchValue}
            onFocus={() => setFocus(true)}
            onBlur={() => {
              // Delay blur to allow for click events on predictions
              setTimeout(() => {
                if (searchValue === '') {
                  setPredictions([]);
                  setFocus(false);
                }
              }, 150);
            }}
            onKeyDown={handleKeyDown}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleSearch(e.target.value)}
            placeholder={focus ? t('searchPlace') : t('mapSearch')} className={`
                            flex-1 
                            border-0 
                            bg-transparent 
                            text-sm
                            ${direction === 'rtl' ? 'text-right pr-4 pl-10' : 'text-left pl-4 pr-10'}
                            no-border-focus
                            `}
            dir={direction}
          />

          {/* Search/Clear Icon */}
          <div className={`
                        absolute 
                        ${direction === 'rtl' ? 'left-3' : 'right-3'} 
                        top-1/2 
                        transform 
                        -translate-y-1/2
                        flex 
                        items-center 
                        justify-center
                    `}>
            {searchValue ? (
              <X
                className="text-gray-400 hover:text-gray-600 cursor-pointer transition-colors"
                size={18}
                onClick={() => {
                  setSearchValue('');
                  setPredictions([]);
                  setFocus(false);
                  mapManager.clearSearchMarkers();
                }}
              />
            ) : (
              <Search
                className="text-gray-400"
                size={18}
              />
            )}
          </div>
        </div>

        {/* Predictions dropdown */}
        {predictions.length > 0 && (
          <Card className={`
                        absolute 
                        top-full 
                        ${searchBoxWidth}
                        bg-white/95 
                        backdrop-blur-sm 
                        shadow-lg 
                        border 
                        border-gray-200/50
                        border-t-0 
                        rounded-lg 
                        rounded-tl-none
                        rounded-tr-none
                        overflow-hidden
                        z-50
                        ${direction === 'rtl' ? 'right-0' : 'left-0'}
                    `}>
            <CardContent className="p-0">
              <DirectionalScrollArea className="h-60">
                <div className="space-y-0">
                  {predictions.map((prediction, index) => (
                    <div
                      key={prediction.place_id}
                      className={`
                                              flex items-center 
                                              p-3 
                                              cursor-pointer 
                                              transition-colors 
                                              border-b 
                                              border-gray-100 
                                              last:border-b-0
                                              ${index === selectedIndex ? 'bg-blue-50' : 'hover:bg-gray-50'}
                                              ${direction === 'rtl' ? 'space-x-reverse' : ''}
                                          `}
                      onClick={() => handlePlaceSelection(prediction)}
                    >
                      <div className={`
                                              flex-shrink-0 
                                              w-8 
                                              h-8 
                                              bg-gray-100 
                                              rounded-full 
                                              flex 
                                              items-center 
                                              justify-center
                                              ${direction === 'rtl' ? 'mr-3' : 'ml-3'}
                                          `}>
                        <Search size={14} className="text-gray-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">
                          {prediction.structured_formatting.main_text}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {prediction.structured_formatting.secondary_text}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </DirectionalScrollArea>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};