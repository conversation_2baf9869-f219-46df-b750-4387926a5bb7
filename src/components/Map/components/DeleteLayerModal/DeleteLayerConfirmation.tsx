import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useTranslation } from "react-i18next"

interface DeleteLayerConfirmationProps {
    isOpen: boolean
    onClose: () => void
    onConfirm: () => void
    title: string
}

export const DeleteLayerConfirmation = ({ isOpen, onClose, onConfirm, title }: DeleteLayerConfirmationProps) => {
    const { t, i18n } = useTranslation();
    const direction = i18n.dir();
    return (
        <AlertDialog open={isOpen} onOpenChange={onClose} >
            <AlertDialogContent className={direction}>
                <AlertDialogHeader className={direction}>
                    <AlertDialogTitle>{t('layer.deleteTitle')}</AlertDialogTitle>
                    <AlertDialogDescription style={{ whiteSpace: 'pre-line' }}>
                        {t('layer.deleteConfirmation', { name: title })}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className={`gap-2 ${direction}`}>
                    <AlertDialogCancel onClick={onClose} className="no-border-focus"> {t('common.cancel')} </AlertDialogCancel>
                    <AlertDialogAction onClick={onConfirm} className="bg-red-600 hover:bg-red-700">
                        {t('common.delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
