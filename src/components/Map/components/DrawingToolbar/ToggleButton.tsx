import { FC } from 'react';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { Toggle } from '@/components/ui/toggle';
import { cn } from '@/lib/utils';

interface ToggleButtonProps {
    icon: JSX.Element;
    onClick?: () => void;
    active?: boolean;
    label?: string;
    disabled?: boolean;
    classes?: string
}

export const ToggleButton: FC<ToggleButtonProps> = ({ icon, onClick, active, label, disabled, classes }) => (
    <Tooltip>
        <TooltipTrigger asChild>
            <Toggle
                pressed={active}
                onPressedChange={onClick}
                size="sm"
                className={cn(
                    "h-7 w-7 p-1 m-1",
                    active && "bg-primary/20 hover:bg-primary/30",
                    classes
                )}
                disabled={disabled}
            >
                {icon}
                <span className="sr-only">{label}</span>
            </Toggle>
        </TooltipTrigger>
        {classes ? "" : <TooltipContent side="top" sideOffset={8}>{label}</TooltipContent>
        }
    </Tooltip>
);
