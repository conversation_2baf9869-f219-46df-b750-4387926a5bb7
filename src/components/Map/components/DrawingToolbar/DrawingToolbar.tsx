import { FC, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { setActiveDrawType } from '@/shared/store/slices/mapSlice';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Square, Circle, Eraser, ChevronDownIcon } from 'lucide-react';
import MapManager from '../../core/MapManager';
import { Feature } from 'ol';
import WKT from 'ol/format/WKT'
import { v4 as uuidv4 } from 'uuid';
import { EditableLayerModal } from '../EditableLayerModal/RJSFForm';
import { handleGraphQLError } from '@/shared/utils/recordHelpers';
import { CREATE_RECORD } from '@/shared/graphQl/mutations/layers';
import { useMutation } from '@apollo/client';
import { showToast } from '@/shared/utils/toastConfig';
import selectIcon from "@/assets/select.svg";
import pointIcon from '@/assets/pointIcon.svg';
import polygonIcon from '@/assets/polygon.svg';
import measureIcon from '@/assets/mesure.svg';
import VectorSource from 'ol/source/Vector';
import { GeometryType } from '../../types/map.types';
import { Source } from 'ol/source';
import { Layer } from 'ol/layer';
import { ToggleButton } from './ToggleButton';
import Polygon from 'ol/geom/Polygon';
import { createCircleAsPolygon } from '../../utils/drawingToolUtils';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { BaseLayerSwitcher } from '../BaseLayerSwitcher/BaseLayerSwitcher';

export const DrawingToolbar: FC = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const [searchParams] = useSearchParams();
    const orgId = searchParams.get('orgId');
    const { activeDrawType, selectedMainLayer } = useSelector((state: RootState) => state.map);
    const jsonSchema = selectedMainLayer?.jsonSchema;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [drawnFeature, setDrawnFeature] = useState<Feature | null>(null);
    const [activeLayerId, setActiveLayerId] = useState<string>('main');
    const isReadOnly = !selectedMainLayer?.id || selectedMainLayer?.readOnly || selectedMainLayer?.selectedBackgroundType === "heatMap" || !jsonSchema;
    const mapManager = MapManager.getInstance();
    const [createRecord] = useMutation(CREATE_RECORD);

    // State for shape card
    const [showShapeCard, setShowShapeCard] = useState(false);

    const createNewLayer = () => {
        const newLayerId = uuidv4();
        const layer = mapManager.getOrCreateDrawingLayer(newLayerId);
        setActiveLayerId(newLayerId);
        return layer;
    };
    const drawingLayerRef = useRef<Layer<Source> | null>(null);

    const handleDrawTypeChange = (drawType: GeometryType) => {
        if (activeDrawType === drawType) {
            dispatch(setActiveDrawType(null));
            mapManager.deactivateCurrentInteraction();
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', true);
            setShowShapeCard(false);
        } else {
            // Create layer only if it doesn't exist
            if (!drawingLayerRef.current) {
                drawingLayerRef.current = createNewLayer();
            }
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', false);
            dispatch(setActiveDrawType(drawType));
            mapManager.activateInteraction({
                interactionType: 'draw',
                source: drawingLayerRef.current.getSource() as VectorSource,
                layer: drawingLayerRef.current,
                drawType,
                autoDeactivate: true,
                onComplete: (feature) => {
                    setDrawnFeature(feature);
                    dispatch(setActiveDrawType(null));
                    mapManager.deactivateCurrentInteraction();
                    setIsModalOpen(true);
                    setShowShapeCard(false);
                },
            });
        }
    };

    const handleClearDrawings = () => {
        mapManager.clearDrawingLayer(activeLayerId);
        mapManager.toggleWmsClickInteraction('virtualWmsLayer', true);
        mapManager.clearMeasurements();
        setShowShapeCard(false);
    };

    const handleSelectToggle = () => {
        if (activeDrawType === 'none') {
            dispatch(setActiveDrawType(null));
            mapManager.deactivateCurrentInteraction();
        } else {
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', true);
            dispatch(setActiveDrawType(null));
            const drawingLayer = mapManager.getOrCreateDrawingLayer(activeLayerId);
            mapManager.activateInteraction({
                interactionType: 'select',
                source: drawingLayer.getSource() as VectorSource,
                layer: drawingLayer,
            });
        }
    };

    const handleRecordSubmit = async (formData: Record<string, any>) => {
        if (!drawnFeature) return;
        try {
            const geometry = drawnFeature.getGeometry();
            if (!geometry) return;
            let wktGeometry;

            if (geometry.getType() === 'Circle') {
                // Convert circle to polygon using createCircleAsPolygon function
                const circle = geometry as any;
                const center = circle.getCenter();
                const radius = circle.getRadius();
                const polygonCoordinates = createCircleAsPolygon(center, radius, 64);
                const transformedGeometry = new Polygon([polygonCoordinates]).transform('EPSG:3857', 'EPSG:4326');
                const wktFormat = new WKT();
                wktGeometry = wktFormat.writeGeometry(transformedGeometry);
            } else {
                const transformedGeometry = geometry.clone().transform('EPSG:3857', 'EPSG:4326');
                const wktFormat = new WKT();
                wktGeometry = wktFormat.writeGeometry(transformedGeometry);
            }

            await createRecord({
                variables: {
                    dataInput: {
                        orgId,
                        layerId: selectedMainLayer?.id,
                        formData: JSON.stringify(formData),
                        geometry: wktGeometry
                    }
                }
            });
            handleClearDrawings()
            mapManager.addOrUpdateWmsLayer({
                wmsLayerName: selectedMainLayer?.layerKey,
                wmsLayerId: selectedMainLayer?.id,
                cqlFilter: selectedMainLayer.filters?.cql_filter,
            });
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', true);
            window.dispatchEvent(new Event('wmsUpdate'));
            setIsModalOpen(false);
            setDrawnFeature(null);
            showToast.success(t('alerts.success.recordCreated'));
        } catch (error: any) {
            const errorMessage = handleGraphQLError(error);
            showToast.error(errorMessage);
        }
    };

    const handleMeasureToggle = () => {
        if (activeDrawType === 'LineString') {
            dispatch(setActiveDrawType(null));
            mapManager.deactivateCurrentInteraction();
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', true);
        } else {
            dispatch(setActiveDrawType('LineString'));
            mapManager.toggleWmsClickInteraction('virtualWmsLayer', false);
            mapManager.activateMeasurement();
        }
        setShowShapeCard(false);
    };

    return (
        <>
            <TooltipProvider>
                <div className="absolute bottom-24 ltr:right-2 rtl:left-2 z-10 h-fit">
                    {/* Main toolbar - simplified bottom positioning */}
                    <div className='flex gap-1 flex-row-reverse p-0 items-center'>
                        <BaseLayerSwitcher />
                        {/* Main toolbar in row */}
                        <div className="flex flex-row items-center gap-1 rounded-lg bg-white">
                            <div className="relative flex items-center">
                                {/* Shape selection card */}
                                {showShapeCard && (
                                    <div className="absolute rtl:left-0 ltr:right-0 bottom-10 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                                        <div className="flex flex-row">
                                            <ToggleButton
                                                icon={<img src={pointIcon} alt="Point" className="h-3 w-3" />}
                                                onClick={() => handleDrawTypeChange('Point')}
                                                active={activeDrawType === 'Point'}
                                                label={t('drawPoint')}
                                                disabled={isReadOnly}
                                            />
                                            <ToggleButton
                                                icon={<Square className="h-4 w-4" />}
                                                onClick={() => handleDrawTypeChange('Rectangle')}
                                                active={activeDrawType === 'Rectangle'}
                                                label={t('drawRectangle')}
                                                disabled={isReadOnly}
                                            />
                                            <ToggleButton
                                                icon={<Circle className="h-4 w-4" />}
                                                onClick={() => handleDrawTypeChange('Circle')}
                                                active={activeDrawType === 'Circle'}
                                                label={t('drawCircle')}
                                                disabled={isReadOnly}
                                            />
                                            <ToggleButton
                                                icon={<img src={polygonIcon} alt="Polygon" className="h-5 w-5" />}
                                                onClick={() => handleDrawTypeChange('Polygon')}
                                                active={activeDrawType === 'Polygon'}
                                                label={t("drawPolygon")}
                                                disabled={isReadOnly}
                                            />
                                        </div>
                                    </div>
                                )}
                                <ToggleButton
                                    icon={
                                        <div className="flex items-center">
                                            <ChevronDownIcon size={15} className='mx-1' />
                                            <img src={polygonIcon} alt="Polygon" className="h-5 w-5" />
                                        </div>
                                    }
                                    onClick={() => setShowShapeCard(!showShapeCard)}
                                    disabled={isReadOnly}
                                    classes='w-14'
                                />
                            </div>
                            <div className="h-5 w-px bg-border" />
                            <ToggleButton
                                icon={<img src={selectIcon} alt="Select" className="h-5 w-5" />}
                                onClick={handleSelectToggle}
                                active={activeDrawType === 'none'}
                                label={t('selectFeature')}
                            />
                            <div className="h-5 w-px bg-border" />
                            <ToggleButton
                                icon={<img src={measureIcon} alt="measure" className="h-5 w-5" />}
                                onClick={handleMeasureToggle}
                                active={activeDrawType === "LineString"}
                                label={t("measureDistance")}
                            />

                            <ToggleButton
                                icon={<Eraser className="h-5 w-5" />}
                                onClick={handleClearDrawings}
                                label={t("clearDrawings")}
                            />
                        </div>
                    </div>
                </div>
            </TooltipProvider>

            {isModalOpen && (
                <EditableLayerModal
                    isOpen={isModalOpen}
                    onClose={() => {
                        setIsModalOpen(false)
                        handleClearDrawings()
                    }}
                    onSave={handleRecordSubmit}
                    mode="add"
                    schema={jsonSchema}
                    uiSchema={selectedMainLayer?.webUiJsonSchema}
                    initialData={{}}
                />
            )}
        </>
    );
};