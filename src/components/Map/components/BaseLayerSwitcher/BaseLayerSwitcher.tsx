import { useState } from 'react'
import { baseMaps } from './constants'
import MapManager from '../../core/MapManager'
import { cn } from "@/lib/utils"

export const BaseLayerSwitcher = () => {
    const mapManager = MapManager.getInstance()
    const [currentBaseLayer, setCurrentBaseLayer] = useState(mapManager.getCurrentBaseLayer())
    const [isOpen, setIsOpen] = useState(false)

    const handleBaseLayerSwitch = (name: string) => {
        mapManager.switchBaseLayer(name)
        setCurrentBaseLayer(name)
        setIsOpen(false)
    }

    const toggleSelector = () => {
        setIsOpen(!isOpen)
    }

    // Find the currently selected base layer
    const selectedMap = baseMaps.find(map => map.name === currentBaseLayer) || baseMaps[0]

    return (
        <div
        // className={`absolute top-[35rem] bottom-0 ltr:right-0 rtl:left-0 z-10`}
        >
            <div className="relative">
                {/* Selected Map */}
                <div
                    className={cn("flex flex-col items-center p-0 cursor-pointer transition-all w-fit")}
                    onClick={toggleSelector}
                >
                    <div className="w-9 h-9 relative overflow-hidden rounded-md border-4 border-white">
                        <img
                            src={selectedMap.icon}
                            alt={selectedMap.title}
                            className="w-full h-full object-cover"
                        />
                    </div>
                </div>

                {/* Map Selection Menu */}
                {isOpen && (
                    <div className="absolute bottom-full rtl:left-10  ltr:right-10 mb-1 p-1 bg-white rounded-lg shadow-lg border border-gray-200 z-10 w-max">
                        <div className="grid grid-cols-4 gap-2">
                            {baseMaps.map((map) => (
                                <div
                                    key={map.name}
                                    className={cn("flex flex-col items-center p-1 cursor-pointer transition-all")}
                                    onClick={() => handleBaseLayerSwitch(map.name)}
                                >
                                    <div className={cn("w-20 h-20 relative overflow-hidden rounded-md border-2",
                                        currentBaseLayer === map.name ? "border-[#5E58EE]" : "border-gray-200")}
                                    >
                                        <img
                                            src={map.icon}
                                            alt={map.title}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <span className="mt-1 text-xs text-center">{map.label}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
