import { useEffect, useRef } from 'react'
import MapManager from '../core/MapManager'
import { createMap } from '../core/base/createMap'
import { resetBackendLayers } from '@/shared/store/slices/mapSlice'
import { useDispatch } from 'react-redux'
import WmsRequestManager from '../services/WmsRequestManager'

export const useMapManager = (mapElement: HTMLDivElement | null) => {
  const mapManagerRef = useRef<MapManager | null>(null)
  const dispatch = useDispatch()

  useEffect(() => {
    // Initialize WMS Request Manager with default settings
    const wmsManager = WmsRequestManager.getInstance()
    wmsManager.setGlobalConfig({
      maxRetries: 10,
      retryDelay: 40000,
      timeout: 60000,
    })

    // Reset permanent failures on initialization
    wmsManager.resetAllPermanentFailures()

    // Restore workspace title
    const savedTitle = localStorage.getItem('currentWorkspaceTitle')
    if (savedTitle) {
      document.title = savedTitle
    }
    if (mapElement && !mapManagerRef.current) {
      const map = createMap(mapElement)
      mapManagerRef.current = new MapManager(map)
    }

    return () => {
      if (mapManagerRef.current) {
        mapManagerRef.current.resetLayersExceptBaseLayers()
        dispatch(resetBackendLayers())
      }
      MapManager.destroyInstance()
      mapManagerRef.current = null

      // Abort all WMS requests on unmount
      WmsRequestManager.getInstance().abortAllRequests()

      // Clear thumbnail data on cleanup
      localStorage.removeItem('mapThumbnail')
      localStorage.removeItem('mapThumbnail_viewState')
    }
  }, [mapElement])

  return mapManagerRef.current
}
