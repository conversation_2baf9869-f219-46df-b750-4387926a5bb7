import { useEffect, useCallback, useRef, useState } from 'react'
import MapManager from '../core/MapManager'
import { getLastSavedThumbnail } from '../utils/mapCapture'
import { SignedUrlService } from '@/shared/graphQl/services/signedUrl.service'
import { graphQlAppClient } from '@/shared/graphQl/graphQlAppClient'
import { useParams, useSearchParams } from 'react-router-dom'
import { UPDATE_WORKSPACE_THUMBNAIL } from '@/shared/graphQl/mutations/layers'

interface MapThumbnailOptions {
  width?: number
  height?: number
  quality?: number
  format?: 'image/png' | 'image/jpeg'
  storageKey?: string
  onCapture?: (thumbnail: string, viewState: any) => void
}

export const useMapThumbnail = (
  mapManager: MapManager | null,
  options: MapThumbnailOptions = {}
) => {
  const {
    width = 600,
    height = 400,
    quality = 0.85,
    format = 'image/jpeg',
    storageKey = 'mapThumbnail',
    onCapture,
  } = options

  const { workspaceId } = useParams()
  const [searchParams] = useSearchParams()
  const orgId = searchParams.get('orgId')

  const lastUploadedThumbnailRef = useRef<string | null>(null)
  const [isCapturing, setIsCapturing] = useState(false)
  const [isUploading, setIsUploading] = useState(false)

  // Function to capture and save ONLY to localStorage
  const captureAndSaveLocally = useCallback(async () => {
    if (!mapManager || isCapturing) return null

    setIsCapturing(true)

    try {
      // Wait for any pending WMS requests to complete
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Use the MapManager's local capture method
      const thumbnail = await mapManager.captureMapThumbnailLocal({
        width,
        height,
        quality,
        format,
        storageKey,
      })

      // Save view state
      const viewState = mapManager.saveCurrentViewState()

      // Store in localStorage
      localStorage.setItem(storageKey, thumbnail)
      localStorage.setItem(`${storageKey}_viewState`, JSON.stringify(viewState))

      // Call callback if provided
      if (onCapture) {
        onCapture(thumbnail, viewState)
      }

      setIsCapturing(false)
      return { thumbnail, viewState }
    } catch (error) {
      console.error('Failed to capture map thumbnail locally:', error)
      setIsCapturing(false)
      return null
    }
  }, [
    mapManager,
    isCapturing,
    width,
    height,
    quality,
    format,
    storageKey,
    onCapture,
  ])

  // Function to upload current localStorage thumbnail to server
  const uploadCurrentThumbnail = useCallback(async () => {
    const currentThumbnail = getLastSavedThumbnail(storageKey)

    if (!currentThumbnail) {
      console.warn('No thumbnail found in localStorage to upload')
      return false
    }

    if (!workspaceId || !orgId) {
      console.warn('No workspace or organization ID available for upload')
      return false
    }

    // Skip if this is the same as the last thumbnail we uploaded
    if (lastUploadedThumbnailRef.current === currentThumbnail) {
      return true
    }

    setIsUploading(true)

    try {
      // Convert data URL to File object
      const blob = await fetch(currentThumbnail).then((res) => res.blob())
      const file = new File([blob], 'map-thumbnail.jpg', {
        type: 'image/jpeg',
      })

      // Upload using SignedUrlService
      const signedUrlService = new SignedUrlService()
      const blobUrl = await signedUrlService.uploadViaSignedUrl(file)

      if (blobUrl) {
        // Update workspace with new thumbnail URL
        await graphQlAppClient.mutate({
          mutation: UPDATE_WORKSPACE_THUMBNAIL,
          variables: {
            dataInput: {
              orgId: orgId,
              workspaceId: workspaceId,
              thumbnail: blobUrl,
            },
          },
        })

        lastUploadedThumbnailRef.current = currentThumbnail
        setIsUploading(false)
        return true
      }

      setIsUploading(false)
      return false
    } catch (error) {
      console.error('Failed to upload map thumbnail:', error)
      setIsUploading(false)
      return false
    }
  }, [storageKey, workspaceId, orgId])

  // Function to capture and upload (for manual save or page leave)
  const captureAndUpload = useCallback(async () => {
    try {
      // First capture locally
      const result = await captureAndSaveLocally()
      if (result) {
        // Then upload
        const uploadSuccess = await uploadCurrentThumbnail()
        return { ...result, uploaded: uploadSuccess }
      }
      return null
    } catch (error) {
      console.error('Failed to capture and upload thumbnail:', error)
      // Try to upload existing thumbnail as fallback
      const uploadSuccess = await uploadCurrentThumbnail()
      return { uploaded: uploadSuccess }
    }
  }, [captureAndSaveLocally, uploadCurrentThumbnail])

  // Only upload on page unload/visibility change - NO automatic uploads
  useEffect(() => {
    if (!mapManager) return

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Upload current thumbnail when user leaves
        uploadCurrentThumbnail()
      }
    }

    const handleBeforeUnload = () => {
      // Upload current thumbnail when page unloads
      uploadCurrentThumbnail()
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)

      // Upload on component unmount
      uploadCurrentThumbnail()
    }
  }, [mapManager, uploadCurrentThumbnail])

  return {
    captureThumbnail: captureAndSaveLocally, // For regular captures (local only)
    uploadThumbnail: uploadCurrentThumbnail, // For manual upload
    captureAndUpload, // For manual save with capture
    isCapturing,
    isUploading,
  }
}
