import { useDispatch } from 'react-redux'
import MapManager from '../core/MapManager'
import { useState } from 'react'
import {
  removeBackendLayer,
  setSidebarView,
} from '@/shared/store/slices/mapSlice'
import { useMutation } from '@apollo/client'
import { DELETE_LAYER } from '@/shared/graphQl/mutations/layers'
import { useSearchParams } from 'react-router-dom'

export const useDeleteLayer = () => {
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const dispatch = useDispatch()
  const mapManager = MapManager.getInstance()
  const [deleteLayerMutation] = useMutation(DELETE_LAYER)
  const [searchParams] = useSearchParams()
  const orgId = searchParams.get('orgId')

  const handleDeleteLayer = async (layerId: number, layerKey: any) => {
    try {
      const { data } = await deleteLayerMutation({
        variables: {
          dataInput: {
            layerId,
            orgId,
          },
        },
      })

      if (data?.deleteLayer?.success) {
        mapManager.removeLayer(layerKey)
        dispatch(removeBackendLayer(layerId))
        setShowDeleteModal(false)
        dispatch(setSidebarView('layers'))
      }
    } catch (error) {
      console.error('Error deleting layer:', error)
    }
  }

  return {
    showDeleteModal,
    setShowDeleteModal,
    handleDeleteLayer,
  }
}
