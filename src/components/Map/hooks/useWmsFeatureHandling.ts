import { useState, useCallback, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import MapManager from '../core/MapManager'

export const useWmsFeatureHandling = (
  mapManager: any,
  mapContainerRef: any
) => {
  const { selectedWmsFeature } = useSelector((state: RootState) => state.map)
  const [availableFeatures, setAvailableFeatures] = useState<any[]>([])
  const [currentFeatureIndex, setCurrentFeatureIndex] = useState(0)
  const [clickCoordinate, setClickCoordinate] = useState<any>(null)
  const [isFeatureLoading, setIsFeatureLoading] = useState(false)
  const [featureState, setFeatureState] = useState<{
    feature: any | null
    position?: { x: number; y: number } | null
    isPinned: boolean
    hasMultiple: boolean
    currentIndex: number
    totalFeatures: number
  }>({
    feature: null,
    position: null,
    isPinned: false,
    hasMultiple: false,
    currentIndex: 0,
    totalFeatures: 0,
  })

  const lastClickEventRef = useRef<number>(0)

  // Helper function to highlight feature on map
  const highlightFeatureOnMap = useCallback(
    (feature: any) => {
      if (mapManager && feature) {
        const mapManagerInstance = MapManager.getInstance()
        if (mapManagerInstance && mapManagerInstance.highlightFeature) {
          mapManagerInstance.highlightFeature(feature)
        }
      }
    },
    [mapManager]
  )

  const handleWmsFeatureClick = useCallback(
    (event: CustomEvent<any>) => {
      if (!mapManager || !mapContainerRef.current) return
      const { features, coordinate, isSingle } = event.detail

      lastClickEventRef.current = Date.now()
      setClickCoordinate(coordinate)
      setAvailableFeatures(features)
      setCurrentFeatureIndex(0)
      mapManager.updateOverlayPosition('featureInfo', coordinate)

      const pixel = mapManager.getPixelFromCoordinate(coordinate)
      // Always show the first feature, whether single or multiple
      const firstFeature = features[0]
      setFeatureState({
        feature: firstFeature,
        position: { x: pixel[0], y: pixel[1] },
        isPinned: false,
        hasMultiple: !isSingle,
        currentIndex: 0,
        totalFeatures: features.length,
      })

      // Highlight the first feature on the map
      highlightFeatureOnMap(firstFeature)
    },
    [mapManager, mapContainerRef, highlightFeatureOnMap]
  )

  const handleNextFeature = useCallback(() => {
    if (availableFeatures.length <= 1) return
    const nextIndex = (currentFeatureIndex + 1) % availableFeatures.length
    setCurrentFeatureIndex(nextIndex)

    const nextFeature = availableFeatures[nextIndex]
    setFeatureState((prev) => ({
      ...prev,
      feature: nextFeature,
      currentIndex: nextIndex,
    }))
  }, [availableFeatures, currentFeatureIndex, highlightFeatureOnMap])

  const handlePreviousFeature = useCallback(() => {
    if (availableFeatures.length <= 1) return

    const prevIndex =
      currentFeatureIndex === 0
        ? availableFeatures.length - 1
        : currentFeatureIndex - 1
    setCurrentFeatureIndex(prevIndex)

    const prevFeature = availableFeatures[prevIndex]
    setFeatureState((prev) => ({
      ...prev,
      feature: prevFeature,
      currentIndex: prevIndex,
    }))
  }, [availableFeatures, currentFeatureIndex, highlightFeatureOnMap])

  const handlePin = () => {
    setFeatureState((prev) => ({
      ...prev,
      isPinned: !prev.isPinned,
    }))
  }

  const handleUnpin = () => {
    // Clear highlight when closing
    const mapManagerInstance = MapManager.getInstance()
    if (mapManagerInstance && mapManagerInstance.clearHighlight) {
      mapManagerInstance.clearHighlight()
    }

    setFeatureState({
      feature: null,
      position: null,
      isPinned: false,
      hasMultiple: false,
      currentIndex: 0,
      totalFeatures: 0,
    })
    setAvailableFeatures([])
    setCurrentFeatureIndex(0)
    mapManager.updateOverlayPosition('featureInfo', undefined)
  }

  useEffect(() => {
    const events = {
      wmsFeatureLoadingStart: () => {
        setIsFeatureLoading(true)
        setFeatureState({} as any)
      },
      wmsFeatureLoadingEnd: () => setIsFeatureLoading(false),
      wmsFeatureClick: handleWmsFeatureClick as EventListener,
    }

    Object.entries(events).forEach(([event, handler]) => {
      window.addEventListener(event, handler)
    })

    return () => {
      Object.entries(events).forEach(([event, handler]) => {
        window.removeEventListener(event, handler)
      })
    }
  }, [handleWmsFeatureClick])

  useEffect(() => {
    const timeSinceLastClick = Date.now() - lastClickEventRef.current

    if (selectedWmsFeature && mapManager && timeSinceLastClick > 100) {
      const pixel = mapManager.getPixelFromCoordinate(clickCoordinate)
      setFeatureState({
        feature: selectedWmsFeature,
        position: { x: pixel[0], y: pixel[1] },
        isPinned: false,
        hasMultiple: false,
        currentIndex: 0,
        totalFeatures: 1,
      })

      // Highlight the selected feature from Redux
      highlightFeatureOnMap(selectedWmsFeature)
    }
  }, [selectedWmsFeature, clickCoordinate, mapManager, highlightFeatureOnMap])

  return {
    featureState,
    availableFeatures,
    isFeatureLoading,
    handlePin,
    handleUnpin,
    handleNextFeature,
    handlePreviousFeature,
  }
}
