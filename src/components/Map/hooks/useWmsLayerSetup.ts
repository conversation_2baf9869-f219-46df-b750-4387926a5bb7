import { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { useParams } from 'react-router-dom'
import MapManager from '../core/MapManager'

export const useWmsLayerSetup = (mapManager: MapManager) => {
  const [isMounted, setIsMounted] = useState(false)
  const {
    backendLayers,
    selectedMainLayer,
    isMapCollapsed,
    workspaceRefreshTrigger,
  } = useSelector((state: RootState) => state.map)
  const { workspaceId } = useParams()

  const setupVirtualWmsLayer = useCallback(() => {
    if (!mapManager || !backendLayers.length) return

    const layerKeys = backendLayers
      .map((layer) => layer.layerKey)
      .reverse()
      .join(',')
    mapManager.addOrUpdateWmsLayer({
      wmsLayerName: layerKeys,
      clickable: true,
      isVirtual: true,
    })
  }, [backendLayers, isMounted])

  const refreshWmsLayers = useCallback(() => {
    mapManager?.addOrUpdateWmsLayers(backendLayers)
  }, [backendLayers, mapManager])

  useEffect(() => {
    setIsMounted(true)
    return () => setIsMounted(false)
  }, [])

  useEffect(() => {
    window.addEventListener('wmsUpdate', refreshWmsLayers)
    return () => {
      window.removeEventListener('wmsUpdate', refreshWmsLayers)
    }
  }, [refreshWmsLayers])

  useEffect(() => {
    if (mapManager && isMounted) {
      setupVirtualWmsLayer()
      refreshWmsLayers()
    }
  }, [
    backendLayers.length,
    mapManager,
    workspaceId,
    isMounted,
    workspaceRefreshTrigger,
  ])

  useEffect(() => {
    if (mapManager && workspaceId) {
      mapManager.resetLayersExceptBaseLayers()
    }
  }, [workspaceId, mapManager])

  useEffect(() => {
    if (selectedMainLayer?.boundaries && selectedMainLayer.fromLayerClick) {
      if (!isMapCollapsed) {
        mapManager?.fitToBoundaries(selectedMainLayer.boundaries)
      }
    }
  }, [mapManager, selectedMainLayer])

  return {}
}
