import { useCallback, useRef } from 'react'
import { Map } from 'ol'
import Geolocation from 'ol/Geolocation'
import { Point } from 'ol/geom'
import { Feature } from 'ol'
import { MAP_STYLES } from '../constants/styles'

export const useGeolocation = (map: Map) => {
  const geolocationRef = useRef<Geolocation>()
  const positionFeature = useRef(new Feature())

  const startTracking = useCallback(() => {
    if (!geolocationRef.current) {
      geolocationRef.current = new Geolocation({
        trackingOptions: { enableHighAccuracy: true },
        projection: map.getView().getProjection(),
      })

      positionFeature.current.setStyle(MAP_STYLES.point)
    }

    geolocationRef.current.setTracking(true)
    geolocationRef.current.on('change:position', () => {
      const coordinates = geolocationRef.current?.getPosition()
      if (coordinates) {
        positionFeature.current.setGeometry(new Point(coordinates))
        map.getView().setCenter(coordinates)
      }
    })
  }, [map])

  const stopTracking = useCallback(() => {
    if (geolocationRef.current) {
      geolocationRef.current.setTracking(false)
    }
  }, [])

  return { startTracking, stopTracking }
}
