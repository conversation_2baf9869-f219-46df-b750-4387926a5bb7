import { store } from '@/shared/store'
import { setLayerLoading } from '@/shared/store/slices/mapSlice'

interface RequestConfig {
  maxRetries: number
  retryDelay: number
  timeout: number
}

interface RequestState {
  isLoading: boolean
  retryCount: number
  controller: AbortController | null
  lastError: string | null
}

class WmsRequestManager {
  private static instance: WmsRequestManager | null = null
  private requestStates: Map<string, RequestState> = new Map()
  private permanentlyFailedLayers: Set<string> = new Set()
  private globalConfig: RequestConfig = {
    maxRetries: 2,
    retryDelay: 10000,
    timeout: 60000,
  }
  private layerConfigs: Map<string, Partial<RequestConfig>> = new Map()

  private constructor() {}

  public static getInstance(): WmsRequestManager {
    if (!WmsRequestManager.instance) {
      WmsRequestManager.instance = new WmsRequestManager()
    }
    return WmsRequestManager.instance
  }

  public setGlobalConfig(config: Partial<RequestConfig>): void {
    this.globalConfig = { ...this.globalConfig, ...config }
  }

  public setLayerConfig(
    layerName: string,
    config: Partial<RequestConfig>
  ): void {
    this.layerConfigs.set(layerName, {
      ...this.layerConfigs.get(layerName),
      ...config,
    })
  }

  public getConfigForLayer(layerName: string): RequestConfig {
    const layerConfig = this.layerConfigs.get(layerName) || {}
    return { ...this.globalConfig, ...layerConfig }
  }

  public startRequest(layerName: string): AbortController | null {
    // Check if layer has permanently failed
    if (this.permanentlyFailedLayers.has(layerName)) {
      // Update Redux to ensure UI shows the layer as not loading
      store.dispatch(setLayerLoading({ layerName, isLoading: false }))
      return null
    }

    // Abort any existing request
    this.abortRequest(layerName)

    // Create new controller
    const controller = new AbortController()

    // Get existing state or create new one
    const existingState = this.requestStates.get(layerName)

    // Update state - preserve retry count if it exists
    this.requestStates.set(layerName, {
      isLoading: true,
      retryCount: existingState ? existingState.retryCount : 0, // Preserve retry count
      controller,
      lastError: existingState ? existingState.lastError : null,
    })

    // Update Redux
    store.dispatch(setLayerLoading({ layerName, isLoading: true }))

    return controller
  }

  public handleRetry(layerName: string, error: string): Promise<void> {
    const state = this.requestStates.get(layerName)
    if (!state) return Promise.reject('No request state found')

    const config = this.getConfigForLayer(layerName)

    // Create a new state object instead of modifying existing one
    const newState = {
      ...state,
      retryCount: state.retryCount + 1,
      lastError: error,
    }

    // Store the updated state
    this.requestStates.set(layerName, newState)

    if (newState.retryCount >= config.maxRetries) {
      // Mark as permanently failed
      this.permanentlyFailedLayers.add(layerName)
      return Promise.reject(`Max retries (${config.maxRetries}) exceeded`)
    }

    return new Promise((resolve) => {
      setTimeout(resolve, config.retryDelay)
    })
  }

  public completeRequest(
    layerName: string,
    success: boolean,
    error?: string
  ): void {
    const state = this.requestStates.get(layerName)
    if (state) {
      const newState = {
        ...state,
        isLoading: false,
        lastError: !success && error ? error : state.lastError,
      }
      this.requestStates.set(layerName, newState)
    }

    // If request was successful, remove from permanently failed list
    if (success) {
      this.resetPermanentFailure(layerName)
    }

    store.dispatch(setLayerLoading({ layerName, isLoading: false }))
  }

  public abortRequest(layerName: string): void {
    const state = this.requestStates.get(layerName)
    if (state && state.controller) {
      state.controller.abort()

      const newState = {
        ...state,
        controller: null,
        isLoading: false,
      }
      this.requestStates.set(layerName, newState)
    }

    store.dispatch(setLayerLoading({ layerName, isLoading: false }))
  }

  public abortAllRequests(): void {
    this.requestStates.forEach((_, layerName) => {
      this.abortRequest(layerName)
    })
  }

  public getRequestState(layerName: string): RequestState | undefined {
    return this.requestStates.get(layerName)
  }

  public getAllRequestStates(): Map<string, RequestState> {
    return new Map(this.requestStates)
  }

  public isPermanentlyFailed(layerName: string): boolean {
    return this.permanentlyFailedLayers.has(layerName)
  }

  public resetPermanentFailure(layerName: string): void {
    if (this.permanentlyFailedLayers.has(layerName)) {
      this.permanentlyFailedLayers.delete(layerName)
    }
  }

  public resetAllPermanentFailures(): void {
    this.permanentlyFailedLayers.clear()
  }

  public getPermanentlyFailedLayers(): string[] {
    return Array.from(this.permanentlyFailedLayers)
  }

  public resetRetryCount(layerName: string): void {
    const state = this.requestStates.get(layerName)
    if (state) {
      this.requestStates.set(layerName, {
        ...state,
        retryCount: 0,
        lastError: null,
      })
    }
  }
}

export default WmsRequestManager
