import { Draw } from 'ol/interaction'
import VectorSource from 'ol/source/Vector'
import { DrawingOptions } from '../../types/map.types'
import { Polygon } from 'ol/geom'
import { DrawEvent } from 'ol/interaction/Draw'
import BaseEvent from 'ol/events/Event'

export const createDrawing = (
  source: VectorSource,
  options: DrawingOptions
) => {
  const draw = new Draw({
    source,
    type: options.type === 'Rectangle' ? 'Circle' : options.type,
    geometryFunction:
      options.type === 'Rectangle' ? createBox() : (undefined as any),
  })

  if (options.onComplete) {
    draw.on('drawend', (event: BaseEvent) => {
      const drawEvent = event as DrawEvent
      options.onComplete!(drawEvent.feature)

      // Auto deactivate if specified
      if (options.autoDeactivate) {
        draw.setActive(false)
      }
    })
  }

  return draw
}

const createBox = () => {
  return (coordinates: number[][], geometry?: Polygon): Polygon => {
    const start = coordinates[0]
    const end = coordinates[1]

    const boxCoordinates = [
      [start, [start[0], end[1]], end, [end[0], start[1]], start],
    ]

    if (geometry) {
      geometry.setCoordinates(boxCoordinates)
      return geometry
    }

    return new Polygon(boxCoordinates)
  }
}
