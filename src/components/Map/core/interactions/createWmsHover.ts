import { ImageWMS } from 'ol/source'
import { Interaction } from 'ol/interaction'
import { Layer } from 'ol/layer'
import { MapBrowserEvent } from 'ol'

export const createWmsHoverInteraction = (
  source: ImageWMS,
  layer: Layer,
  options?: {
    hitTolerance?: number
    processError?: (error: Error) => void
    processData?: (data: any) => void
  }
): Interaction => {
  const { processError, processData } = options || {}

  const interaction = new Interaction({
    handleEvent: (evt: MapBrowserEvent<any>) => {
      if (evt.type !== 'pointermove') return true

      const map = evt.map
      const coordinate = evt.coordinate
      const resolution = map.getView().getResolution()
      const projection = map.getView().getProjection()

      if (!resolution || !projection) {
        processError?.(new Error('Map resolution or projection is undefined.'))
        return true
      }

      // Get the WMS GetFeatureInfo URL
      const url = source.getFeatureInfoUrl(coordinate, resolution, projection, {
        INFO_FORMAT: 'application/json',
        FEATURE_COUNT: 1,
      })

      if (!url) {
        processError?.(
          new Error('No URL available for WMS GetFeatureInfo request.')
        )
        return true
      }

      // Fetch feature information from the WMS layer
      fetch(url)
        .then((response) => {
          if (!response.ok) throw new Error('Failed to fetch WMS feature info.')
          return response.json()
        })
        .then((data) => {
          if (data.features && data.features.length > 0) {
            processData?.(data) // Callback for handling data
            // Optionally, dispatch a custom event with feature data
            const customEvent = new CustomEvent('wmsFeatureHover', {
              detail: {
                features: data.features,
                coordinate,
                layer: layer.get('layerKey'),
              },
            })
            window.dispatchEvent(customEvent)
          } else {
            processError?.(
              new Error('No features found at the hovered location.')
            )
          }
        })
        .catch((error) => {
          console.error('Error fetching WMS feature info:', error)
          processError?.(error)
        })

      return true // Allow other interactions to process the event
    },
  })

  return interaction
}
