import { Interaction } from 'ol/interaction'
import VectorSource from 'ol/source/Vector'
import ImageWMS from 'ol/source/ImageWMS'
import { createWmsClickInteraction } from './createWmsClick'
import { createDrawing } from './createDrawing'
import { createModify } from './createModify'
import { createSnap } from './createSnap'
import { createTranslate } from './createTranslate'
import VectorLayer from 'ol/layer/Vector'
import { CreateInteractionOptions } from '../../types/layer.types'
import { createSelect } from './createSelect'
import { createWmsHoverInteraction } from './createWmsHover'

export const createInteraction = ({
  interactionType,
  source,
  layer,
  drawType,
  onComplete,
}: CreateInteractionOptions): Interaction | null => {
  switch (interactionType) {
    case 'wmsClick':
      return source instanceof ImageWMS
        ? createWmsClickInteraction(source, layer)
        : null

    case 'wmsHover':
      return source instanceof ImageWMS
        ? createWmsHoverInteraction(source, layer)
        : null

    case 'draw':
      return source instanceof VectorSource && drawType && drawType !== 'none'
        ? createDrawing(source, { type: drawType, onComplete }) || null
        : null

    case 'modify':
      return source instanceof VectorSource ? createModify(source) : null

    case 'snap':
      return source instanceof VectorSource ? createSnap(source) : null

    case 'translate':
      return createTranslate(layer as VectorLayer)

    case 'select':
      return createSelect(layer ? [layer] : undefined)

    default:
      return null
  }
}
