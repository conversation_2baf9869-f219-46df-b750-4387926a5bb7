import { Select } from 'ol/interaction'
import { click } from 'ol/events/condition'
import { Style, Circle as CircleStyle, Fill, Stroke } from 'ol/style'
import { Layer } from 'ol/layer'

export const createSelect = (layers?: Layer[]) => {
  return new Select({
    condition: click,
    layers,
    style: new Style({
      fill: new Fill({
        color: 'rgba(0, 102, 255, 0.4)',
      }),
      stroke: new Stroke({
        color: '#0066ff',
        width: 3,
      }),
      image: new CircleStyle({
        radius: 8,
        fill: new Fill({
          color: '#0066ff',
        }),
      }),
    }),
  })
}
