import { ImageWMS } from 'ol/source'
import { Interaction } from 'ol/interaction'
import { Layer } from 'ol/layer'
import { MapBrowserEvent } from 'ol'
import MapManager from '../MapManager'
import { store } from '@/shared/store'
import { getFullToken } from '@/shared/utils/generals'
import { getUrlParams } from '../../utils/getUrlParams'
import { Routes } from '@/shared/utils/routes'

// import { showToast } from '@/shared/utils/toastConfig'

export const createWmsClickInteraction = (
  source: ImageWMS,
  layer: Layer
): Interaction => {
  const interaction = new Interaction({
    handleEvent: (evt: MapBrowserEvent<any>) => {
      if (evt.type !== 'singleclick') return true

      const map = evt.map
      const coordinate = evt.coordinate

      // Clear existing highlight first
      const mapManager = MapManager.getInstance()
      mapManager.clearHighlight()

      const resolution = map.getView().getResolution()
      const projection = map.getView().getProjection()

      if (!resolution || !projection) {
        return true
      }

      const { queryParams, pathParams } = getUrlParams(
        window.location.href,
        `/${Routes.mapWorkspace}`
      )
      const orgId = queryParams.orgId
      const workspaceId = pathParams.workspaceId

      // Get the WMS GetFeatureInfo URL
      const url = source.getFeatureInfoUrl(coordinate, resolution, projection, {
        INFO_FORMAT: 'application/json',
        FEATURE_COUNT: 50,
        workspace_id: workspaceId,
        organization: orgId,
        QUERY_LAYERS: source.getParams().LAYERS.filter((layerName: any) => {
          const state = store.getState()
          const layer = state.map.backendLayers.find(
            (l) => l.layerKey === layerName
          )
          return layer?.isVisible ?? false
        }),
      })

      if (!url) {
        return true
      }
      // Fetch feature information from the WMS layer
      window.dispatchEvent(new CustomEvent('wmsFeatureLoadingStart'))
      const token = getFullToken()
      fetch(url, {
        headers: token
          ? {
              Authorization: token,
            }
          : undefined,
      })
        .then((response) => {
          if (!response.ok) throw new Error('Failed to fetch WMS feature info.')
          return response.json()
        })
        .then((data) => {
          if (data.features && data.features.length > 0) {
            const mapManager = MapManager.getInstance()
            if (data.features.length === 1)
              mapManager.highlightFeature(data.features[0])

            window.dispatchEvent(
              new CustomEvent('wmsFeatureClick', {
                detail: {
                  features: data.features,
                  coordinate,
                  layer: layer.get('layerKey'),
                  isSingle: data.features.length === 1,
                },
              })
            )
          }
        })
        .catch((error) => {
          console.error('Error fetching WMS feature info:', error)
        })
        .finally(() => {
          window.dispatchEvent(new CustomEvent('wmsFeatureLoadingEnd'))
        })

      return true // Allow other interactions to process the event
    },
  })

  return interaction
}
