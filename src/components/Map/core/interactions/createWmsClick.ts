import { ImageWMS } from 'ol/source'
import { Interaction } from 'ol/interaction'
import { Layer } from 'ol/layer'
import { MapBrowserEvent } from 'ol'
import MapManager from '../MapManager'
import { RootState, store } from '@/shared/store'
import { getFullToken } from '@/shared/utils/generals'
import { getUrlParams } from '../../utils/getUrlParams'
import { Routes } from '@/shared/utils/routes'

// Helper to build CQL_FILTER string
const buildCqlFilterString = (visibleLayerKeys: string[], state: RootState) => {
  const cqlFilters = visibleLayerKeys.map((layerKey) => {
    const backendLayer = state.map.backendLayers.find(
      (l) => l.layerKey === layerKey
    ) as any
    const baseFilter = backendLayer?.filters?.cql_filter || 'INCLUDE'
    return `layer_id='${backendLayer?.id}' AND (${baseFilter})`
  })
  return cqlFilters.join(';')
}

export const createWmsClickInteraction = (
  source: ImageWMS,
  layer: Layer
): Interaction => {
  const interaction = new Interaction({
    handleEvent: (evt: MapBrowserEvent<any>) => {
      if (evt.type !== 'singleclick') return true

      const map = evt.map
      const coordinate = evt.coordinate

      const mapManager = MapManager.getInstance()
      mapManager.clearHighlight()

      const resolution = map.getView().getResolution()
      const projection = map.getView().getProjection()
      if (!resolution || !projection) return true

      const { queryParams, pathParams } = getUrlParams(
        window.location.href,
        `/${Routes.mapWorkspace}`
      )
      const orgId = queryParams.orgId
      const workspaceId = pathParams.workspaceId

      const state = store.getState()
      const visibleLayerKeys = state.map.backendLayers
        .filter((layer) => layer.isVisible)
        .map((layer) => layer.layerKey) as string[]
      if (visibleLayerKeys.length === 0) {
        console.warn('No visible layers to query.')
        return true
      }

      const cqlFilter = buildCqlFilterString(visibleLayerKeys, state)
      const queryLayers = visibleLayerKeys.map((l: any) => `${l}`).join(',')

      source.updateParams({
        LAYERS: queryLayers,
        STYLES: queryLayers,
      })

      const url = source.getFeatureInfoUrl(coordinate, resolution, projection, {
        INFO_FORMAT: 'application/json',
        FEATURE_COUNT: 50,
        workspace_id: workspaceId,
        organization: orgId,
        QUERY_LAYERS: queryLayers,
        CQL_FILTER: cqlFilter,
      })

      if (!url) return true

      window.dispatchEvent(new CustomEvent('wmsFeatureLoadingStart'))
      const token = getFullToken()

      fetch(url, {
        headers: token ? { Authorization: token } : undefined,
      })
        .then((response) => {
          if (!response.ok) throw new Error('Failed to fetch WMS feature info.')
          return response.json()
        })
        .then((data) => {
          if (data.features && data.features.length > 0) {
            const mapManager = MapManager.getInstance()
            if (data.features.length === 1)
              mapManager.highlightFeature(data.features[0])

            window.dispatchEvent(
              new CustomEvent('wmsFeatureClick', {
                detail: {
                  features: data.features,
                  coordinate,
                  layer: layer.get('layerKey'),
                  isSingle: data.features.length === 1,
                },
              })
            )
          }
        })
        .catch((error) => {
          console.error('Error fetching WMS feature info:', error)
        })
        .finally(() => {
          window.dispatchEvent(new CustomEvent('wmsFeatureLoadingEnd'))
        })

      return true
    },
  })

  return interaction
}
