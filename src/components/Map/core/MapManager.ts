import { Feature, Map, Overlay, View } from 'ol'
import {
  Tile as TileLayer,
  Vector as VectorLayer,
  Image as ImageLayer,
} from 'ol/layer'
import { createWmsLayer } from './layers/createWmsLayer'
import { getPolygonExtent } from '../utils/general'
import { createWmsSource } from '../utils/createWmsSoure'
import { ImageWMS } from 'ol/source'
import { Interaction } from 'ol/interaction'
import { createInteraction } from './interactions'
import { CreateInteractionOptions } from '../types/layer.types'
import VectorSource from 'ol/source/Vector'
import { createVectorLayer } from './layers/createVectorLayer'
import { createOverlay } from '../utils/createOverlay'
import {
  WMS_HIGHLIGHTED_FEATURE_LAYER,
  createHighlightedVectorLayer,
} from '../utils/createHighlightedLayer'
import {
  createMeasurementOverlay,
  updateMeasurementContent,
} from '../utils/createMeasurementOverlay'
import {
  SEARCH_MARKER_LAYER,
  createMarkerLayer,
} from '../utils/createMarkerLayer'
import { calculateDistance } from '../utils/calculateDistance'
import { LineString } from 'ol/geom'
import WmsRequestManager from '../services/WmsRequestManager'
import { waitForLayersToLoad } from '../utils/mapCapture'
import { extractOpacityFromLayer } from '../utils/extractOpacityFromLayer'

export enum LayerType {
  BASE = 'base',
  WMS = 'wms',
  DRAW = 'draw',
  HIGHLIGHT = 'highlight',
  Measure = 'measure',
  Search = 'search',
}

class MapManager {
  private static instance: MapManager | null = null
  private readonly map: Map
  private readonly layers: Record<
    string,
    TileLayer<any> | VectorLayer<any> | ImageLayer<any>
  > = {}
  private readonly interactions: Record<string, Interaction | null> = {}
  private activeInteractionKey: string | null = null
  private loadingLayers = new Set<string>()

  // Add to the class properties
  private drawingHistories: Record<string, Feature[]> = {}
  private currentHistoryIndexes: Record<string, number> = {}

  private overlays: Record<string, Overlay> = {}

  constructor(map: Map) {
    if (MapManager.instance) {
      throw new Error(
        'MapManager instance already exists. Use MapManager.getInstance() to access it.'
      )
    }
    this.map = map
    MapManager.instance = this
  }

  // Singleton: Get the instance
  public static getInstance(): MapManager {
    if (!MapManager.instance) {
      throw new Error(
        'MapManager instance is not set. Please call setInstance() first.'
      )
    }
    return MapManager.instance
  }

  // Safe getter that returns null if instance doesn't exist
  public static getInstanceSafe(): MapManager | null {
    return MapManager.instance
  }

  // Destroy the Singleton instance
  public static destroyInstance(): void {
    if (MapManager.instance) {
      MapManager.instance.map?.dispose()
      MapManager.instance = null
    }
  }

  // WMS Request Manager methods
  public configureWmsRequests(config: {
    maxRetries?: number
    retryDelay?: number
    timeout?: number
  }): void {
    WmsRequestManager.getInstance().setGlobalConfig(config)
  }

  public configureLayerRequests(
    layerName: string,
    config: { maxRetries?: number; retryDelay?: number; timeout?: number }
  ): void {
    WmsRequestManager.getInstance().setLayerConfig(layerName, config)
  }

  public abortLayerRequests(layerName: string): void {
    WmsRequestManager.getInstance().abortRequest(layerName)
  }

  public abortAllWmsRequests(): void {
    WmsRequestManager.getInstance().abortAllRequests()
  }

  // Get the map view
  getView(): View | null {
    return this.map.getView() || null
  }

  // Fit the map to given boundaries
  fitToBoundaries(geojsonObject: any): void {
    const view = this.getView()
    if (!view) return

    let extent = getPolygonExtent(geojsonObject)
    try {
      view.fit(extent, {
        duration: 3000,
        padding: [50, 50, 50, 50], // Add padding around the boundary
        nearest: false, // Allow fractional zoom levels for better fitting
      })
    } catch (error) {
      console.warn('Failed to fit to calculated extent, using fallback:', error)
      extent = [3895308.289707, 1823145.223876, 6122619.72089, 3895308.289707]
      view.fit(extent, {
        duration: 3000,
        padding: [50, 50, 50, 50],
        nearest: false,
      })
    }
  }

  // Fit the map to selected feature
  goToFeature(geometry: any): void {
    const view = this.getView()
    if (!view) return

    if (geometry.type === 'Point') {
      view.animate({
        center: geometry.coordinates,
        zoom: 10,
        duration: 2000,
      })
    } else {
      const extent = getPolygonExtent(geometry)
      view.fit(extent, {
        duration: 2000,
        padding: [150, 150, 150, 150],
      })
    }
  }

  // Add a layer to the map
  addLayer(
    name: string,
    layer: TileLayer<any> | VectorLayer<any> | ImageLayer<any>
  ): void {
    if (this.layers[name]) {
      this.map.removeLayer(this.layers[name])
    }
    this.layers[name] = layer
    this.map.addLayer(layer)
  }

  // Add or update a WMS layer
  addOrUpdateWmsLayer({
    wmsLayerName,
    clickable = false,
    isVirtual = false,
    customStyle = null,
    ...rest
  }: any): ImageLayer<ImageWMS> {
    const layerKey = isVirtual ? 'virtualWmsLayer' : wmsLayerName
    const existingLayer = this.layers[layerKey] as ImageLayer<ImageWMS>

    const wmsSource = createWmsSource({
      wmsLayerName,
      customStyle,
      ...(existingLayer
        ? { color: rest.color }
        : {
            onLoading: rest.onLoading,
            onLoaded: rest.onLoaded,
            color: '',
          }),
    }) as ImageWMS

    if (existingLayer) {
      existingLayer.setSource(wmsSource)
      if (!clickable) {
        this.removeWmsClickInteraction(layerKey)
      } else if (clickable) {
        this.removeWmsClickInteraction(layerKey)
        this.addWmsClickInteraction(layerKey, wmsSource, existingLayer)
      }
      if (rest?.opacity) existingLayer.setOpacity(rest.opacity)
      return existingLayer
    } else {
      const wmsLayer = createWmsLayer(wmsSource, {
        layerKey: `geocore:${layerKey}`,
      })
      wmsLayer.set('type', LayerType.WMS)
      wmsLayer.set('layerKey', layerKey)
      wmsLayer.setOpacity(rest.opacity ?? 1)
      wmsLayer.setZIndex(rest.zIndex ?? 1)
      if (isVirtual) {
        wmsLayer.setOpacity(0)
        wmsLayer.setVisible(false)
      }
      this.addLayer(layerKey, wmsLayer)
      if (clickable) {
        this.addWmsClickInteraction(layerKey, wmsSource, wmsLayer)
      }
      return wmsLayer
    }
  }

  addOrUpdateWmsLayers(layers: any[], clickable: boolean = false): void {
    layers.forEach((layer) => {
      const extractedOpacity = extractOpacityFromLayer(layer)
      this.addOrUpdateWmsLayer({
        wmsLayerName: layer.layerKey,
        clickable,
        onLoading: () => {
          this.loadingLayers.add(layer.layerKey)
        },
        onLoaded: () => {
          this.loadingLayers.delete(layer.layerKey)
        },
        opacity: extractedOpacity,
        zIndex: layer.zIndex,
      })
    })
  }

  // Remove a specific WMS layer by name
  removeWmsLayer(layerName: string): void {
    const layer = this.layers[layerName]
    if (layer && layer.get('type') === LayerType.WMS) {
      this.map.removeLayer(layer)
      delete this.layers[layerName]
      this.removeWmsClickInteraction(layerName)

      // Abort any ongoing requests for this layer
      this.abortLayerRequests(layerName)
    }
  }

  // Add WMS click interaction
  private addWmsClickInteraction(
    layerName: string,
    source: ImageWMS,
    layer: ImageLayer<ImageWMS>
  ): void {
    const interaction = createInteraction({
      interactionType: 'wmsClick',
      source,
      layer,
    })
    if (interaction) {
      this.interactions[`${layerName}-click`] = interaction
      this.map.addInteraction(interaction)
    }
  }

  // Remove WMS click interaction
  private removeWmsClickInteraction(layerName: string): void {
    const interactionKey = `${layerName}-click`
    const interaction = this.interactions[interactionKey]

    if (interaction) {
      this.map.removeInteraction(interaction)
      delete this.interactions[interactionKey]
    }
  }

  // Toggle Wms Click Interaction
  toggleWmsClickInteraction(layerName: string, enable: boolean) {
    const interactionKey = `${layerName}-click`
    const interaction = this.interactions[interactionKey]
    if (interaction) {
      interaction.setActive(enable)
    }
  }

  // Get or create a drawing layer
  getOrCreateDrawingLayer(layerId?: string): VectorLayer<VectorSource> {
    const drawingLayerName = layerId
      ? `${LayerType.DRAW}-${layerId}`
      : `${LayerType.DRAW}-main`

    if (!this.layers[drawingLayerName]) {
      const drawingLayer = createVectorLayer(undefined, {
        properties: {
          layerKey: drawingLayerName,
          type: LayerType.DRAW,
        },
      })

      this.addLayer(drawingLayerName, drawingLayer)

      // Initialize history for this layer
      this.drawingHistories[drawingLayerName] = []
      this.currentHistoryIndexes[drawingLayerName] = 0
    }

    return this.layers[drawingLayerName] as VectorLayer<VectorSource>
  }

  // Activate an interaction
  activateInteraction(options: CreateInteractionOptions): void {
    const interactionKey = `${options.layer.get('layerKey')}-${options.interactionType}`

    // Deactivate any existing interaction
    this.deactivateCurrentInteraction()

    // Create and store the new interaction
    const interaction = createInteraction(options)
    if (interaction) {
      this.interactions[interactionKey] = interaction
      this.map.addInteraction(interaction)
      this.activeInteractionKey = interactionKey
    }
  }

  // Deactivate the currently active interaction
  deactivateCurrentInteraction(): void {
    if (this.activeInteractionKey) {
      const interaction = this.interactions[this.activeInteractionKey]
      if (interaction) {
        this.map.removeInteraction(interaction)
      }
      this.interactions[this.activeInteractionKey] = null
      delete this.interactions[this.activeInteractionKey]
      this.activeInteractionKey = null
    }
  }

  // Toggle layer visibility
  toggleVisibility(name: string): boolean {
    const layer = this.layers[name]
    if (layer) {
      const newVisibility = !layer.getVisible()
      layer.setVisible(newVisibility)
      return newVisibility
    }
    return false
  }

  showLayerOnly(targetLayerName: string): void {
    Object.entries(this.layers).forEach(([name, layer]) => {
      if (layer.get('type') === LayerType.WMS) {
        const isVisible = name === targetLayerName
        layer.setVisible(isVisible)
      }
    })
  }

  showAllLayers(): void {
    Object.entries(this.layers).forEach(([, layer]) => {
      if (layer.get('type') === LayerType.WMS) {
        layer.setVisible(true)
      }
    })
  }

  // Remove all non-base layers
  resetLayersExceptBaseLayers(): void {
    Object.entries(this.layers).forEach(([name, layer]) => {
      if (layer.get('type') !== LayerType.BASE) {
        this.map.removeLayer(layer)
        this.removeWmsClickInteraction(name)
        delete this.layers[name]

        // Abort any ongoing requests for this layer
        if (layer.get('type') === LayerType.WMS) {
          this.abortLayerRequests(name)
        }
      }
    })

    // Abort all remaining requests to be safe
    this.abortAllWmsRequests()
  }

  // Zoom utility
  private zoom(delta: number): void {
    const view = this.getView()
    if (!view) return

    const currentZoom = view.getZoom() || 0
    view.animate({
      zoom: currentZoom + delta,
      duration: 250,
    })
  }

  // Zoom in
  zoomIn(): void {
    this.zoom(1)
  }

  // Zoom out
  zoomOut(): void {
    this.zoom(-1)
  }

  // Switch base layer visibility
  switchBaseLayer(layerName: string): void {
    const layers = this.map.getLayers().getArray()
    layers.forEach((layer) => {
      if (layer.get('type') === LayerType.BASE) {
        layer.setVisible(layer.get('name') === layerName)
      }
    })
  }

  // Get the current active base layer
  getCurrentBaseLayer(): string {
    const layers = this.map.getLayers().getArray()
    const activeBaseLayer = layers.find(
      (layer) => layer.get('type') === LayerType.BASE && layer.getVisible()
    )
    return activeBaseLayer ? activeBaseLayer.get('name') : 'Google Maps'
  }

  saveDrawingState(feature: Feature, layerId?: string): void {
    const drawingLayer = this.getOrCreateDrawingLayer(layerId)
    const source = drawingLayer.getSource()
    const layerName = drawingLayer.get('layerKey')

    // Create a deep clone of the feature
    const featureClone = feature.clone()

    if (source) {
      // Add feature to source
      source.addFeature(featureClone)

      // Initialize history array if it doesn't exist
      if (!this.drawingHistories[layerName]) {
        this.drawingHistories[layerName] = []
        this.currentHistoryIndexes[layerName] = 0
      }

      // Add to history
      this.drawingHistories[layerName].push(featureClone)
      this.currentHistoryIndexes[layerName] =
        this.drawingHistories[layerName].length
    }
  }

  // Undo the last action in a specific layer
  undo(layerId?: string): void {
    const drawingLayer = this.getOrCreateDrawingLayer(layerId)
    const source = drawingLayer.getSource()
    const layerName = drawingLayer.get('layerKey')

    if (
      !source ||
      !this.drawingHistories[layerName] ||
      this.currentHistoryIndexes[layerName] <= 0
    ) {
      return
    }

    // Clear the source
    source.clear()

    // Decrement history index
    this.currentHistoryIndexes[layerName]--

    // Redraw all features up to the current index
    for (let i = 0; i < this.currentHistoryIndexes[layerName]; i++) {
      const feature = this.drawingHistories[layerName][i].clone()
      source.addFeature(feature)
    }
  }

  // List all existing drawing layers
  getAllDrawingLayers(): VectorLayer<VectorSource>[] {
    return Object.values(this.layers).filter(
      (layer): layer is VectorLayer<VectorSource> =>
        layer.get('type') === LayerType.DRAW
    )
  }

  getPixelFromCoordinate(coordinate: number[]): number[] {
    return this.map.getPixelFromCoordinate(coordinate)
  }

  addOverlay(id: string, element: HTMLElement, coordinate: number[]): void {
    const overlay = createOverlay(element, coordinate)
    this.overlays[id] = overlay
    this.map.addOverlay(overlay)
  }

  removeOverlay(id: string): void {
    if (this.overlays[id]) {
      this.map.removeOverlay(this.overlays[id])
      delete this.overlays[id]
    }
  }

  updateOverlayPosition(id: string, coordinate: number[]): void {
    if (this.overlays[id]) {
      this.overlays[id].setPosition(coordinate)
    }
  }

  addRealTimeMoveListeners(callback: () => void): void {
    this.map.on('movestart', callback)
    this.map.on('pointerdrag', callback)
    this.map.on('moveend', callback)
    const view = this.getView()
    if (view) {
      view.on('change:center', callback)
      view.on('change:resolution', callback)
    }
  }

  removeRealTimeMoveListeners(callback: () => void): void {
    this.map.un('movestart', callback)
    this.map.on('pointerdrag', callback)
    this.map.un('moveend', callback)
    const view = this.getView()
    if (view) {
      view.un('change:center', callback)
      view.un('change:resolution', callback)
    }
  }

  updateAllOverlayPositions(): void {
    Object.entries(this.overlays).forEach(([id, overlay]) => {
      const position = overlay.getPosition()
      if (position) {
        const pixel = this.getPixelFromCoordinate(position)
        this.updateOverlayPosition(id, position)
        // Emit custom event with updated pixel coordinates
        window.dispatchEvent(
          new CustomEvent('overlayPositionUpdate', {
            detail: { id, pixel },
          })
        )
      }
    })
  }

  clearDrawingLayers(): void {
    Object.entries(this.layers).forEach(([, layer]) => {
      if (layer.get('type') === LayerType.DRAW) {
        const source = layer.getSource()
        source?.clear()
      }
    })
  }

  clearDrawingLayer(layerId: string): void {
    const drawingLayerName = `${LayerType.DRAW}-${layerId}`
    const layer = this.layers[drawingLayerName]

    if (layer && layer.get('type') === LayerType.DRAW) {
      // Clear the source
      const source = layer.getSource()
      source?.clear()
    }
  }

  highlightFeature(wmsFeature: any): void {
    // Remove existing highlight layer
    const existingLayer = this.layers[WMS_HIGHLIGHTED_FEATURE_LAYER]
    if (existingLayer) {
      this.map.removeLayer(existingLayer)
      delete this.layers[WMS_HIGHLIGHTED_FEATURE_LAYER]
    }

    // Create and add new highlight layer
    const highlightLayer = createHighlightedVectorLayer({
      geojsonObject: wmsFeature,
    })

    this.addLayer(WMS_HIGHLIGHTED_FEATURE_LAYER, highlightLayer)
  }

  clearHighlight(): void {
    const existingLayer = this.layers[WMS_HIGHLIGHTED_FEATURE_LAYER]
    if (existingLayer) {
      this.map.removeLayer(existingLayer)
      delete this.layers[WMS_HIGHLIGHTED_FEATURE_LAYER]
    }
  }

  reorderLayers(layerNames: string[]): void {
    layerNames.reverse().forEach((name, index) => {
      const layer = this.layers[name] as any
      if (layer && layer.get('type') === LayerType.WMS) {
        layer.setZIndex(index)
      }
    })
  }

  activateMeasurement(): void {
    const measureLayer = this.getOrCreateDrawingLayer('measure')
    measureLayer.set('type', LayerType.Measure)

    this.activateInteraction({
      interactionType: 'draw',
      source: measureLayer.getSource() as VectorSource,
      layer: measureLayer,
      drawType: 'LineString',
      onComplete: (feature) => {
        const geometry = feature.getGeometry() as LineString
        const coordinates = geometry.getCoordinates()
        const distance = calculateDistance(coordinates)

        // Get exact center point of the line
        const centerCoord = geometry.getCoordinateAt(0.5)
        const overlay = createMeasurementOverlay(centerCoord, () =>
          this.deleteMeasureFeature(feature)
        )

        updateMeasurementContent(overlay, distance)
        this.map.addOverlay(overlay)
        feature.set('measurementOverlay', overlay)
      },
    })
  }

  clearMeasurements(): void {
    const measureLayer = this.getOrCreateDrawingLayer('measure')
    const source = measureLayer.getSource()

    if (source) {
      // Remove all measurement overlays
      source.getFeatures().forEach((feature) => {
        const overlay = feature.get('measurementOverlay')
        if (overlay) {
          this.map.removeOverlay(overlay)
        }
      })

      // Clear features
      source.clear()
    }
  }

  deleteMeasureFeature(feature: Feature): void {
    const measureLayer = this.getOrCreateDrawingLayer('measure')
    const source = measureLayer.getSource()

    if (source) {
      // Remove overlay
      const overlay = feature.get('measurementOverlay')
      if (overlay) {
        this.map.removeOverlay(overlay)
      }

      // Remove feature
      source.removeFeature(feature)
    }
  }

  setLayerTransparency(layerName: string, transparency: number): void {
    const layer = this.layers[layerName]
    if (layer) {
      layer.setOpacity(transparency)
    }
  }

  removeLayer(layerName: string): void {
    const layer = this.layers[layerName]
    if (layer) {
      this.map.removeLayer(layer)
      delete this.layers[layerName]
      this.removeWmsClickInteraction(layerName)

      // Abort any ongoing requests if it's a WMS layer
      if (layer.get('type') === LayerType.WMS) {
        this.abortLayerRequests(layerName)
      }
    }
  }

  // Method to add a search marker
  addSearchMarker(coordinate: number[]): void {
    // Remove existing marker layer
    const existingLayer = this.layers[SEARCH_MARKER_LAYER]
    if (existingLayer) {
      this.map.removeLayer(existingLayer)
      delete this.layers[SEARCH_MARKER_LAYER]
    }

    // Create and add new marker layer
    const markerLayer = createMarkerLayer({
      coordinate: coordinate,
    })

    this.addLayer(SEARCH_MARKER_LAYER, markerLayer)
  }

  // Method to clear search markers
  clearSearchMarkers(): void {
    const existingLayer = this.layers[SEARCH_MARKER_LAYER]
    if (existingLayer) {
      this.map.removeLayer(existingLayer)
      delete this.layers[SEARCH_MARKER_LAYER]
    }
  }

  panMap(dx: number, dy: number): void {
    const view = this.getView()
    if (view) {
      const center = view.getCenter()
      const resolution = view.getResolution() || 1
      const mercatorDx = dx * resolution
      const mercatorDy = -dy * resolution
      if (center) {
        view.animate({
          center: [center[0] + mercatorDx, center[1] + mercatorDy],
          duration: 250,
        })
      }
    }
  }
  /**
   * Captures the current map view as a thumbnail including all visible layers
   * @param options Options for thumbnail generation
   * @returns Promise that resolves to a data URL of the thumbnail
   */
  async captureMapThumbnailLocal(
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'image/png' | 'image/jpeg'
      storageKey?: string
    } = {}
  ): Promise<string> {
    try {
      const {
        width = 600,
        height = 400,
        quality = 0.85,
        format = 'image/jpeg',
        storageKey = 'mapThumbnail',
      } = options

      // Wait for all layers to load
      await waitForLayersToLoad(this)

      // Force a render to ensure all layers are up-to-date
      this.map.renderSync()

      // Use OpenLayers built-in functionality to capture the map
      const mapCanvas = this.map.getTargetElement().querySelector('canvas')
      if (!mapCanvas) {
        throw new Error('Map canvas not found')
      }

      // Create a new canvas for the thumbnail with the specified dimensions
      const thumbnailCanvas = document.createElement('canvas')
      thumbnailCanvas.width = width
      thumbnailCanvas.height = height
      const thumbnailContext = thumbnailCanvas.getContext('2d')

      if (!thumbnailContext) {
        throw new Error('Could not get canvas context')
      }

      // Draw the map canvas onto the thumbnail canvas
      thumbnailContext.drawImage(
        mapCanvas,
        0,
        0,
        mapCanvas.width,
        mapCanvas.height,
        0,
        0,
        width,
        height
      )

      // Add a subtle border
      thumbnailContext.strokeStyle = '#e5e7eb'
      thumbnailContext.lineWidth = 2
      thumbnailContext.strokeRect(0, 0, width, height)

      // Convert to data URL
      const dataUrl = thumbnailCanvas.toDataURL(format, quality)

      // Only store in localStorage - no upload
      localStorage.setItem(storageKey, dataUrl)

      // Also save view state
      const viewState = this.saveCurrentViewState()
      localStorage.setItem(`${storageKey}_viewState`, JSON.stringify(viewState))

      return dataUrl
    } catch (error) {
      // Fall back to last saved thumbnail
      const { getLastSavedThumbnail } = await import('../utils/mapCapture')
      const fallbackThumbnail = getLastSavedThumbnail(options?.storageKey)
      if (fallbackThumbnail) {
        return fallbackThumbnail
      }
      throw error
    }
  }

  // Save the current view state
  saveCurrentViewState(): {
    center: number[]
    zoom: number
    rotation: number
  } {
    const view = this.getView()
    if (!view) {
      throw new Error('Map view not available')
    }

    return {
      center: view.getCenter() || [0, 0],
      zoom: view.getZoom() || 0,
      rotation: view.getRotation() || 0,
    }
  }

  addMoveEndListener(callback: () => void): void {
    this.map.on('moveend', callback)
  }

  removeMoveEndListener(callback: () => void): void {
    this.map.un('moveend', callback)
  }
}

export default MapManager
