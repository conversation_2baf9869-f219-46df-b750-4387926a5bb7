import VectorSource from 'ol/source/Vector'
import VectorLayer, { Options as VectorLayerOptions } from 'ol/layer/Vector'

export const createVectorLayer = (
  source?: VectorSource,
  options: VectorLayerOptions = {}
): VectorLayer => {
  const vectorSource = source || new VectorSource()
  const layer = new VectorLayer({
    source: vectorSource,
    ...options,
  })
  // To set any additional properties
  if (options.properties) {
    Object.entries(options.properties).forEach(([key, value]) => {
      layer.set(key, value)
    })
  }

  return layer
}
