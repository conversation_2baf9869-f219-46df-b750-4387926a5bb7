import TileLayer from 'ol/layer/Tile'
import XY<PERSON> from 'ol/source/XYZ'
import OSM from 'ol/source/OSM'
import LayerGroup from 'ol/layer/Group'

export const createBaseLayers = () => {
  const osmLayer = new TileLayer({
    source: new OSM({
      crossOrigin: 'anonymous',
    }),
    visible: false,
    properties: { name: 'OSM', type: 'base' as const },
  })

  const googleLayer = new TileLayer({
    source: new XYZ({
      url: 'https://mt0.google.com/vt/lyrs=m&hl=en&x={x}&y={y}&z={z}',
      crossOrigin: 'anonymous',
    }),
    visible: true,
    properties: { name: 'Google Maps', type: 'base' as const },
  })

  const satelliteLayer = new TileLayer({
    source: new XYZ({
      url: 'https://mt0.google.com/vt/lyrs=s&hl=en&x={x}&y={y}&z={z}',
      crossOrigin: 'anonymous',
    }),
    visible: false,
    properties: { name: 'Satellite', type: 'base' as const },
  })

  const darkModeLayer = new TileLayer({
    source: new XYZ({
      url: 'https://{a-c}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
      crossOrigin: 'anonymous',
    }),
    visible: false,
    properties: { name: 'Dark Map', type: 'base' as const },
  })

  return new LayerGroup({
    layers: [osmLayer, googleLayer, satelliteLayer, darkModeLayer],
  })
}
