import ImageLayer from 'ol/layer/Image'
import <PERSON>WMS from 'ol/source/ImageWMS'
import {
  WMS_BASE_URL,
  IMAGE_FORMAT,
  ORGANIZATION_ID,
} from '../../constants/defaults'

export const createWmsLayer = (source?: ImageWMS, config: any = {}) => {
  const configSoure = new ImageWMS({
    url: WMS_BASE_URL,
    params: {
      LAYERS: [`geocore:${config.layerKey}`],
      ORGANIZATION: ORGANIZATION_ID,
      FORMAT: IMAGE_FORMAT,
      layer_id: config.id,
    },
  })
  const imageSource = source || configSoure

  return new ImageLayer({ source: imageSource })
}
