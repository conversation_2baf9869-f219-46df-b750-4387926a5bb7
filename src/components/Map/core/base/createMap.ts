import { Map } from 'ol'
import { MapOptions } from 'ol/Map'
import { createView } from './createView'
import { createBaseLayers } from '../layers/createBaseLayer'
import { defaults as defaultInteractions } from 'ol/interaction'
import { defaults as defaultControls, Zoom } from 'ol/control'

export const createMap = (
  target: HTMLElement,
  mapOptions: MapOptions = {}
): Map => {
  return new Map({
    target,
    layers: createBaseLayers(),
    view: createView(),
    controls: defaultControls({ zoom: false }).extend([
      new Zoom({
        className: 'ol-zoom custom-ol-zoom',
        zoomInTipLabel: '',
        zoomOutTipLabel: '',
      }),
    ]),
    interactions: defaultInteractions({ pinchRotate: false }),
    ...mapOptions,
  })
}
