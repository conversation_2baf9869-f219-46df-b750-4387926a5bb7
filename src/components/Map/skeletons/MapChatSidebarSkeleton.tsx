import { Skeleton } from "@/components/Skeleton/Skeleton"

export const MapChatSidebarSkeleton = () => {
    return (
        <div className="fixed rtl:left-4 ltr:right-4 top-20 bottom-4 w-[300px] flex flex-col border rounded-lg bg-background">
            <Skeleton className="h-14 w-full rounded-t-lg" />
            <div className="flex-1 p-4 space-y-4">
                {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                ))}
            </div>
            <Skeleton className="h-16 w-full rounded-b-lg" />
        </div>
    )
}
