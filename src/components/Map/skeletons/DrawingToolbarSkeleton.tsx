import { Skeleton } from "@/components/Skeleton/Skeleton"

export const DrawingToolbarSkeleton = () => {
    return (
        <div className="fixed left-1/2 bottom-[1.25rem] -translate-x-1/2 rounded-lg bg-white p-1 shadow-sm">
            <div className="flex items-center gap-1">
                {[...Array(7)].map((_, i) => (
                    <div key={i} className="h-9 w-9 rounded-md">
                        <Skeleton className="h-full w-full" />
                    </div>
                ))}
            </div>
        </div>
    )
}
