import { Skeleton } from "@/components/Skeleton/Skeleton"

export const LayerSidebarSkeleton = () => {
    return (
        <div className="fixed right-4 top-15 bottom-[1.25rem] w-[250px] flex flex-col border rounded-lg bg-white h-[calc(100vh-100px)]">
            <Skeleton className="h-14 w-full rounded-t-lg" />
            <div className="p-4 space-y-2">
                {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                ))}
            </div>
        </div>
    )
}
