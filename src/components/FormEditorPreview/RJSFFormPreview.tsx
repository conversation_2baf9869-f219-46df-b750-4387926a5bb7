import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useTranslation } from 'react-i18next';
import { CustomThemedForm } from '../RJSF/theme/ThemedForm';

interface FormPreviewProps {
    jsonSchema: string;
    uiSchema: string;
    formData: string;
    onFormDataChange: (formData: any) => void;
    onSave: (formData: any) => void;
    onCancel: () => void;
    onSkip?: () => void;
    mutationLoading: boolean
}

export const FormPreview = ({
    jsonSchema,
    uiSchema,
    formData,
    onFormDataChange,
    onSave,
    onCancel,
    onSkip,
    mutationLoading
}: FormPreviewProps) => {
    const { t } = useTranslation();
    const [parsedJsonSchema, setParsedJsonSchema] = useState<any>(null);
    const [parsedUiSchema, setParsedUiSchema] = useState<any>({});
    const [parsedFormData, setParsedFormData] = useState<any>({});
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string>('');

    const { navigateWithOrg } = useOrganizationManager();

    // Validate if schema is a valid RJSF schema
    const isValidSchema = (schema: any): boolean => {
        return schema &&
            typeof schema === 'object' &&
            (schema.type || schema.properties || schema.$ref);
    };

    useEffect(() => {
        try {
            const jsonSchemaObj = JSON.parse(jsonSchema);
            const uiSchemaObj = JSON.parse(uiSchema);
            const formDataObj = JSON.parse(formData);

            // Validate the schema
            if (!isValidSchema(jsonSchemaObj)) {
                setHasError(true);
                setErrorMessage('Invalid JSON Schema: Schema must have a type, properties, or $ref');
                return;
            }

            setParsedJsonSchema(jsonSchemaObj);
            setParsedUiSchema(uiSchemaObj);
            setParsedFormData(formDataObj);
            setHasError(false);
            setErrorMessage('');
        } catch (error) {
            console.error('Error parsing JSON:', error);
            setHasError(true);
            setErrorMessage('Invalid JSON format in one or more fields');
        }
    }, [jsonSchema, uiSchema, formData]);

    const handleFormChange = ({ formData: newFormData }: { formData: any }) => {
        setParsedFormData(newFormData);
        onFormDataChange(newFormData);
    };

    const handleFormSubmit = ({ formData: submittedFormData }: { formData: any }) => {
        onSave({ formData: submittedFormData, jsonSchema: parsedJsonSchema });
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/2?resumed=true`);
    };

    const handleSaveClick = () => {
        handleFormSubmit({ formData: parsedFormData });
    };

    if (hasError || !parsedJsonSchema) {
        return (
            <div className="space-y-4">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                    <div className="text-gray-500 italic">
                        {errorMessage || 'Preview will appear here when valid JSON is entered in all fields'}
                    </div>
                </div>
                <div className="flex justify-between items-center mt-6">
                    <Button variant="outline" onClick={handlePrevious} className="text-gray-600 py-2 px-8 rounded-md">
                        {t('buttons.previous')}
                    </Button>
                    <div className="flex gap-4">
                        <Button
                            variant="outline"
                            onClick={onCancel}
                            className="text-gray-600 py-2 px-8 rounded-md"
                        >
                            {t('buttons.Cancel Changes')}
                        </Button>
                        {onSkip && (
                            <Button
                                variant="outline"
                                onClick={onSkip}
                                className="text-gray-600 py-2 px-8 rounded-md"
                            >
                                {t('buttons.skipToWorkspace')}
                            </Button>
                        )}
                        <Button
                            type="button"
                            onClick={handleSaveClick}
                            className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                            disabled
                        >
                            {mutationLoading ? t('loading') : t('common.save')}
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col h-full">
            {/* Form content with scroll */}
            <div className="flex-grow overflow-auto">
                {hasError || !parsedJsonSchema ? (
                    <div className="space-y-4">
                        <div className="bg-white p-6 rounded-lg shadow-sm">
                            <div className="text-gray-500 italic">
                                {errorMessage || 'Preview will appear here when valid JSON is entered in all fields'}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="bg-white p-6 rounded-lg shadow-sm">
                        <div>
                            <CustomThemedForm
                                schema={parsedJsonSchema}
                                uiSchema={parsedUiSchema}
                                formData={parsedFormData}
                                onChange={handleFormChange}
                                onSubmit={handleFormSubmit}
                                formContext={{
                                    preventEnterSubmit: true
                                }}
                            >
                                <></>
                            </CustomThemedForm>
                        </div>
                    </div>
                )}
            </div>

            {/* Fixed buttons at the bottom */}
            <div className="mt-auto pt-4 bg-white">
                <div className="flex justify-between items-center">
                    <div className="flex gap-4">
                        <Button
                            type="button"
                            onClick={handleSaveClick}
                            className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-md"
                            disabled={mutationLoading || hasError}
                        >
                            {mutationLoading ? t('loading') : t('buttons.next')}
                        </Button>
                        {onSkip && (
                            <Button
                                variant="outline"
                                onClick={onSkip}
                                className="text-gray-600 py-2 px-8 rounded-md"
                            >
                                {t('buttons.skipToWorkspace')}
                            </Button>
                        )}
                    </div>
                    <Button variant="outline" onClick={handlePrevious} className="text-gray-600 py-2 px-8 rounded-md">
                        {t('buttons.previous')}
                    </Button>
                </div>
            </div>
        </div>
    );
};