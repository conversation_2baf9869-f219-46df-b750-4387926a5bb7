/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Routes, WorkspaceMethods } from "@/shared/utils/routes";
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager';
import { useTranslation } from 'react-i18next';

interface FormPreviewProps {
    jsonSchema: string;
    uiSchema: string;
    formData: string;
    onFormDataChange: (formData: any) => void;
    onSave: (formData: any) => void;
    onCancel: () => void;
    mutationLoading: boolean
}

export const FormPreview = ({
    jsonSchema,
    uiSchema,
    formData,
    onFormDataChange,
    onSave,
    onCancel, mutationLoading
}: FormPreviewProps) => {
    const { t } = useTranslation();
    const [fields, setFields] = useState<React.ReactNode[]>([]);

    const { navigateWithOrg } = useOrganizationManager();

    const renderField = (key: string, schema: any, value: any) => {
        const handleChange = (newValue: any) => {
            const updatedFormData = JSON.parse(formData);
            updatedFormData[key] = newValue;
            onFormDataChange(updatedFormData);
        };

        switch (schema.type) {
            case 'string':
                return (
                    <div key={key} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            {schema.title || key}
                        </label>
                        <input
                            type={schema.format === 'email' ? 'email' : 'text'}
                            value={value || ''}
                            onChange={(e) => handleChange(e.target.value)}
                            placeholder={schema.title}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        />
                        {schema.format === 'email' && (
                            <p className="mt-1 text-sm text-gray-500">Please enter a valid email address</p>
                        )}
                    </div>
                );

            case 'integer':
            case 'number':
                return (
                    <div key={key} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            {schema.title || key}
                        </label>
                        <input
                            type="number"
                            value={value || ''}
                            min={schema.minimum}
                            max={schema.maximum}
                            onChange={(e) => handleChange(Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        />
                        {schema.minimum !== undefined && (
                            <p className="mt-1 text-sm text-gray-500">Minimum value: {schema.minimum}</p>
                        )}
                    </div>
                );

            case 'boolean':
                return (
                    <div key={key} className="mb-4">
                        <label className="flex items-center">
                            <input
                                type="checkbox"
                                checked={value || false}
                                onChange={(e) => handleChange(e.target.checked)}
                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{schema.title || key}</span>
                        </label>
                    </div>
                );

            default:
                return null;
        }
    };

    useEffect(() => {
        try {
            const parsedSchema = JSON.parse(jsonSchema);
            const parsedFormData = JSON.parse(formData);

            const renderedFields = Object.entries(parsedSchema.properties || {}).map(
                ([key, schema]: [string, any]) => renderField(key, schema, parsedFormData[key])
            );

            setFields(renderedFields);
        } catch (error) {
            console.error('Error parsing JSON:', error);
            setFields([
                <div key="error" className="text-gray-500 italic">
                    Preview will appear here when valid JSON is entered in all fields
                </div>
            ]);
        }
    }, [jsonSchema, uiSchema, formData]);

    const handleSave = () => {
        try {
            const parsedFormData = JSON.parse(formData);
            const parsedSchema = JSON.parse(jsonSchema);
            onSave({ formData: parsedFormData, jsonSchema: parsedSchema });
        } catch (error) {
            console.error('Error parsing form data:', error);
        }
    };

    const handlePrevious = () => {
        navigateWithOrg(`/${Routes.createWorkspace}/${WorkspaceMethods.UPLOAD_FILE}/2?resumed=true`);
    };

    return (
        <div className="space-y-4">
            <div className="bg-white p-6 rounded-lg shadow-sm">
                {fields}
            </div>
            <div className="flex justify-end">
                <div className="flex gap-4 mt-6">
                    <Button variant="outline" onClick={handlePrevious} className="text-gray-600 py-2 px-8 rounded-full">
                        {t('buttons.previous')}
                    </Button>
                    <Button
                        variant="outline"
                        onClick={onCancel}
                        className="text-gray-600 py-2 px-8 rounded-full"
                    >
                        {t('buttons.Cancel Changes')}
                    </Button>
                    <Button
                        type="button"
                        onClick={handleSave}
                        className="bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-bold py-2 px-8 rounded-full"
                    >
                        {mutationLoading ? t('loading') : t('common.save')}
                    </Button>
                </div>
            </div>
        </div>
    );
};