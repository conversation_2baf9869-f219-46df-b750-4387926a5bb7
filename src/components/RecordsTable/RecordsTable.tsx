import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { WorkspaceMethods } from '@/shared/utils/routes'
import { DataExplorer } from './DataExplorer'
import { SchemaExplorer } from './SchemaExplorer'
import { LayerSelector } from './LayerSelector'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'

export interface RecordData {
  sourceProperties: Record<string, any>
  data: Record<string, any>
}

export const RecordsTable = () => {
  const { t, i18n } = useTranslation()
  const [currentView, setCurrentView] = useState('data')
  const { backendLayers: mainLayers, mapMode, selectedTableLayer } = useSelector(
    (state: RootState) => state.map
  )

  const isDetailsMode = mapMode === 'details'
  const isExploratoryMode = mapMode === 'exploratory'

  return (
    <div className="flex flex-col h-full">
      {isDetailsMode ? (
        // Details mode - show only data explorer and layer selector
        <>
          <div className={`flex justify-end items-center p-2 border-b flex-wrap gap-1 ${i18n.dir()}`}>
            <LayerSelector layers={mainLayers} />
          </div>
          <DataExplorer />
        </>
      ) : isExploratoryMode ? (
        // Exploratory mode - show tabs without data explorer tab
        <Tabs
          defaultValue={currentView !== 'data' ? currentView : 'schema'}
          className="flex-1"
          onValueChange={setCurrentView}
        >
          <div className={`flex justify-between items-center p-2 border-b flex-wrap gap-1 ${i18n.dir()}`}>
            <TabsList>
              <TabsTrigger value="schema">{t('jsonSchema')}</TabsTrigger>
              <TabsTrigger value="diagram">{t('schemaDiagram')}</TabsTrigger>
              {selectedTableLayer?.dataset?.workspace?.workspaceType === WorkspaceMethods.UPLOAD_FILE && <TabsTrigger value="eda">{t('edaReport')}</TabsTrigger>}
            </TabsList>
            <LayerSelector layers={mainLayers} />
          </div>

          <SchemaExplorer />
        </Tabs>
      ) : ''}
    </div>
  )
}