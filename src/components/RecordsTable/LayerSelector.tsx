import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { RootState } from '@/shared/store'
import { setSelectedTableLayer, setSelectedMainLayer } from '@/shared/store/slices/mapSlice'
import {
    Select,
    SelectContent,
    SelectTrigger,
    SelectItem,
    SelectValue,
} from '../ui/select'

interface LayerSelectorProps {
    layers: any[]
}

export const LayerSelector = ({ layers }: LayerSelectorProps) => {
    const { t, i18n } = useTranslation()
    const dispatch = useDispatch()
    const { selectedTableLayer, selectedMainLayer, mapMode } = useSelector((state: RootState) => state.map)

    const isDetailsMode = mapMode === 'details'
    const currentLayer = isDetailsMode ? selectedMainLayer : selectedTableLayer

    const handleLayerChange = (layerId: string) => {
        const selectedLayer = layers.find(
            (layer) => layer.id.toString() === layerId
        )
        if (selectedLayer) {
            if (isDetailsMode) {
                dispatch(setSelectedMainLayer(selectedLayer))
            } else {
                dispatch(setSelectedTableLayer(selectedLayer))
            }
        }
    }

    return (
        <Select
            value={currentLayer?.id.toString()}
            onValueChange={handleLayerChange}
            dir={i18n.dir()}
        >
            <SelectTrigger className="w-[180px] no-border-focus">
                <SelectValue placeholder={t('selectLayer')} />
            </SelectTrigger>
            <SelectContent>
                {layers.map((layer) => (
                    <SelectItem key={layer.id} value={layer.id.toString()}>
                        {layer.title}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    )
}