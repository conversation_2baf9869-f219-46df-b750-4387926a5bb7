import { useState, useEffect } from 'react'
import { useQuery } from '@apollo/client'
import { useSelector } from 'react-redux'
import { useParams, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { GET_EDA_REPORTS } from '@/shared/graphQl/queries/layers'
import { RootState } from '@/shared/store'
import {
    Select,
    SelectContent,
    SelectTrigger,
    SelectItem,
    SelectValue,
} from '../ui/select'

export const EDAReporter = () => {
    const { t } = useTranslation()
    const { selectedTableLayer } = useSelector((state: RootState) => state.map)
    const { workspaceId } = useParams()
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get("orgId")

    const [iframeLoading, setIframeLoading] = useState(true)
    const [edaSource, setEdaSource] = useState('ydata')
    const [edaReportPath, setEdaReportPath] = useState('')

    const { data: edaReportsData, refetch: refetchEdaReports } = useQuery(GET_EDA_REPORTS, {
        variables: {
            layerId: parseInt(selectedTableLayer?.id.toString() || ''),
            orgId: parseInt(orgId || '0'),
            workspaceId: parseInt(workspaceId || '0'),
            filters: [
                {
                    field: "source",
                    value: edaSource,
                    clause: "iexact"
                }
            ],
            pageInfo: {
                limit: 1,
                orderBy: "-created"
            }
        },
        skip: !selectedTableLayer?.id || !orgId || !workspaceId,
        onCompleted: (data) => {
            if (data?.edaReports?.data?.length > 0) {
                setEdaReportPath(data.edaReports.data[0].file);
            } else {
                setEdaReportPath('');
            }
        }
    });

    // Handle EDA source change
    const handleEdaSourceChange = (source: string) => {
        setEdaSource(source);
        setIframeLoading(true);
    }

    useEffect(() => {
        if (selectedTableLayer) {
            refetchEdaReports()
        }
    }, [selectedTableLayer?.id, refetchEdaReports])

    return (
        <>
            <div className="mb-4 flex justify-end items-center">
                {edaReportsData?.edaReports?.data?.length > 0 && (
                    <div className="text-sm text-gray-600 mr-auto">
                        {t('lastUpdated')}:
                        {new Date(edaReportsData.edaReports.data[0].created).toLocaleDateString()}
                    </div>
                )}
                <Select
                    value={edaSource}
                    onValueChange={handleEdaSourceChange}
                >
                    <SelectTrigger className="w-[180px] no-border-focus">
                        <SelectValue placeholder={t('selectSource')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="ydata">YData</SelectItem>
                        <SelectItem value="sweetviz">Sweetviz</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            {edaReportPath ? (
                <div className="relative w-full h-[73vh]">
                    {iframeLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                            <div className="text-gray-500">{t('loading')}</div>
                        </div>
                    )}
                    <iframe
                        src={edaReportPath}
                        className="w-full h-full border-0 rounded-lg"
                        onLoad={() => setIframeLoading(false)}
                        title="EDA Report"
                    />
                </div>
            ) : (
                <div className="flex items-center justify-center h-[80vh] bg-gray-50 rounded-lg">
                    <div className="text-gray-500">
                        {t('workspace.advancedFields.noData')}
                    </div>
                </div>
            )}
        </>
    )
}