import { useState } from 'react';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { addCacheBusting } from './displayUtils';
import { getSizeClasses } from './ImagePreview';

interface ImageGalleryProps {
    images: string[];
    displayMode?: 'single' | 'horizontal'; // New prop to control display mode
    size?: 'small' | 'medium' | 'large'; // Size for horizontal mode
}

export const ImageGallery = ({
    images,
    displayMode = 'single',
    size = 'medium'
}: ImageGalleryProps) => {
    const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);

    // Process images with cache-busting
    const processedImages = images.map(img => addCacheBusting(img));

    const openGallery = (index: number = 0) => {
        setSelectedImageIndex(index);
        setIsOpen(true);
    };

    const closeGallery = () => {
        setIsOpen(false);
        setSelectedImageIndex(null);
    };

    const goToPrevious = () => {
        if (selectedImageIndex !== null && selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
        }
    };

    const goToNext = () => {
        if (selectedImageIndex !== null && selectedImageIndex < images.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'ArrowLeft') goToPrevious();
        if (e.key === 'ArrowRight') goToNext();
        if (e.key === 'Escape') closeGallery();
    };

    const refreshImages = () => {
        setRefreshKey(prev => prev + 1);
    };

    if (!images || images.length === 0) {
        return (
            <div className="w-64 h-64 bg-gray-200 rounded-md flex items-center justify-center">
                <span className="text-gray-500">No images available</span>
            </div>
        );
    }

    return (
        <div className="space-y-2">
            {/* Conditional rendering based on display mode */}
            {displayMode === 'single' ? (
                // Original single image preview with +X badge
                <div className="relative w-fit">
                    <img
                        src={processedImages[0]}
                        alt={`Image 1 of ${images.length}`}
                        className="w-64 h-64 object-cover rounded-md cursor-pointer hover:opacity-90 transition-opacity border"
                        onClick={() => openGallery(0)}
                        key={`img-${refreshKey}`}
                    />
                    {images.length > 1 && (
                        <div
                            className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white rounded-full w-8 h-8 flex items-center justify-center cursor-pointer hover:bg-opacity-90"
                            onClick={() => openGallery(0)}
                        >
                            +{images.length - 1}
                        </div>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 bg-black bg-opacity-50 text-white hover:bg-opacity-70 text-xs p-1 h-6"
                        onClick={refreshImages}
                        title="Refresh images"
                    >
                        ↻
                    </Button>
                </div>
            ) : (
                // New horizontal scrollable preview row
                <div className="w-full">
                    <div className="flex justify-between mb-2">
                        <div className="text-sm font-medium text-gray-700">
                            Images ({images.length})
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs p-1 h-6"
                            onClick={refreshImages}
                            title="Refresh images"
                        >
                            ↻
                        </Button>
                    </div>
                    <div className="flex gap-3 overflow-x-auto pb-2">
                        {images.map((_, index) => (
                            <div key={`preview-${index}`} className="flex-shrink-0">
                                <img
                                    src={processedImages[index]}
                                    alt={`Image ${index + 1}`}
                                    className={`${getSizeClasses(size)} object-cover rounded-md cursor-pointer hover:opacity-80 transition-opacity border`}
                                    onClick={() => openGallery(index)}
                                    key={`img-${index}-${refreshKey}`}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Full Screen Gallery Dialog - Same for both modes */}
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent
                    className="max-w-6xl max-h-[90vh] p-0 overflow-hidden [&>button]:hidden"
                    onKeyDown={handleKeyDown}
                >
                    <div className="relative w-full h-full">
                        {/* Close Button */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white hover:bg-opacity-70"
                            onClick={closeGallery}
                        >
                            <X className="h-4 w-4" />
                        </Button>

                        {/* Image Counter */}
                        <div className="absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
                            {selectedImageIndex !== null ? selectedImageIndex + 1 : 0} / {images.length}
                        </div>

                        {/* Main Image */}
                        <div className="w-full h-[80vh] flex items-center justify-center p-4">
                            {selectedImageIndex !== null && (
                                <img
                                    src={processedImages[selectedImageIndex]}
                                    alt={`Image ${selectedImageIndex + 1} of ${images.length}`}
                                    className="max-w-full max-h-full object-contain"
                                    key={`main-${selectedImageIndex}-${refreshKey}`}
                                />
                            )}
                        </div>

                        {/* Navigation Buttons */}
                        {selectedImageIndex !== null && selectedImageIndex > 0 && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white hover:bg-opacity-70"
                                onClick={goToPrevious}
                            >
                                <ChevronLeft className="h-6 w-6" />
                            </Button>
                        )}

                        {selectedImageIndex !== null && selectedImageIndex < images.length - 1 && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white hover:bg-opacity-70"
                                onClick={goToNext}
                            >
                                <ChevronRight className="h-6 w-6" />
                            </Button>
                        )}

                        {/* Thumbnail Strip */}
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 rounded-lg p-2">
                            <div className="flex space-x-2 max-w-md overflow-x-auto">
                                {images.map((_, index) => (
                                    <img
                                        key={`thumb-${index}-${refreshKey}`}
                                        src={processedImages[index]}
                                        alt={`Thumbnail ${index + 1}`}
                                        className={`w-12 h-12 object-cover rounded cursor-pointer transition-opacity ${index === selectedImageIndex
                                            ? 'opacity-100 ring-2 ring-white'
                                            : 'opacity-60 hover:opacity-80'
                                            }`}
                                        onClick={() => setSelectedImageIndex(index)}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};
