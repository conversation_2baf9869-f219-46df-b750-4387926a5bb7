import { isImageUrl } from "@/shared/utils/tabelrecordHelper";
import { ImagePreview } from "./ImagePreview";
import { ImageGallery } from "./ImageGallery";

export const NestedTable = ({ data }: { data: Record<string, any> }) => {
    const entries = Object.entries(data);

    const renderValue = (value: any) => {
        // Handle arrays
        if (Array.isArray(value)) {
            // Check if all items in array are image URLs
            const allImagesUrls = value.every(item =>
                typeof item === 'string' && isImageUrl(item)
            );

            if (allImagesUrls && value.length > 0) {
                return <ImageGallery images={value} />;
            }

            // Handle regular arrays
            return (
                <div className="bg-gray-50 p-2 rounded border max-w-[350px] overflow-auto">
                    <pre className="whitespace-pre-wrap break-words text-sm">
                        {JSON.stringify(value, null, 2)}
                    </pre>
                </div>
            );
        }

        // Handle objects
        if (typeof value === 'object' && value !== null) {
            return (
                <div className="bg-gray-50 p-2 rounded border max-w-[350px] overflow-auto">
                    <pre className="whitespace-pre-wrap break-words text-sm">
                        {JSON.stringify(value, null, 2)}
                    </pre>
                </div>
            );
        }

        // Handle strings
        if (typeof value === 'string' && isImageUrl(value)) {
            return <ImagePreview src={value} />;
        }

        // Handle other values
        return <div className="break-words">{value?.toString() ?? '-'}</div>;
    };

    return (
        <table className="w-full border-collapse">
            <tbody>
                {entries.map(([key, value]) => (
                    <tr key={key} className="border-0">
                        <td className="p-2 font-medium">{key}</td>
                        <td className="p-2">
                            {renderValue(value)}
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
};