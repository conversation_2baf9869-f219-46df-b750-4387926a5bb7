import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ImagePreviewProps {
    src: string
    size?: 'small' | 'medium' | 'large'
}

export const ImagePreview = ({ src, size = 'small' }: ImagePreviewProps) => {
    return (
        <Dialog>
            <TooltipProvider>
                <Tooltip>
                    <DialogTrigger asChild>
                        <TooltipTrigger asChild>
                            <div className="flex items-center justify-between">
                                <img
                                    src={src}
                                    alt="Preview"
                                    loading="lazy"
                                    className={`${getSizeClasses(size)} object-cover rounded-md cursor-pointer hover:opacity-80 transition-opacity`}
                                    onError={(e) => {
                                        // Fallback to original src if cached version fails
                                        if (e.currentTarget.src !== src) {
                                            e.currentTarget.src = src;
                                        }
                                    }}
                                />
                            </div>
                        </TooltipTrigger>
                    </DialogTrigger>
                    <TooltipContent side="top">
                        Click to view full image
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
            <DialogContent className="max-w-4xl max-h-[80vh] p-0 overflow-hidden">
                <div className="w-full h-full flex items-center justify-center p-4">
                    <img
                        src={src}
                        alt="Full size"
                        className="max-w-full max-h-[calc(80vh-2rem)] object-contain"
                        onError={(e) => {
                            // Fallback to original src if cached version fails
                            if (e.currentTarget.src !== src) {
                                e.currentTarget.src = src;
                            }
                        }}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
};

export const getSizeClasses = (size: 'small' | 'medium' | 'large') => {
    switch (size) {
        case 'small':
            return 'h-16 w-16'
        case 'medium':
            return 'h-24 w-24'
        case 'large':
            return 'h-32 w-64'
        default:
            return 'h-16 w-16'
    }
}