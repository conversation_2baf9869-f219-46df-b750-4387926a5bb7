import { TabsContent } from '@/components/ui/tabs'
import { WorkspaceMethods } from '@/shared/utils/routes'
import { SchemaEditor } from './SchemaEditor'
import { EDAReporter } from './EDAReporter'
import { SchemaVisualizer } from '../SchemaVisualizer/SchemaVisualizer'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { useTranslation } from 'react-i18next'

export const SchemaExplorer = () => {
    const { t } = useTranslation()
    const { selectedTableLayer } = useSelector((state: RootState) => state.map)
    const jsonSchema = selectedTableLayer?.jsonSchema

    return (
        <>
            <TabsContent value="schema" className="flex-grow p-4">
                <SchemaEditor />
            </TabsContent>

            {selectedTableLayer?.dataset?.workspace?.workspaceType === WorkspaceMethods.UPLOAD_FILE && (
                <TabsContent value="eda" className="flex-grow p-4">
                    <EDAReporter />
                </TabsContent>
            )}

            <TabsContent value="diagram" className="flex-grow p-4">
                {jsonSchema ? (
                    <SchemaVisualizer jsonSchema={jsonSchema} />
                ) : (
                    <div className="flex items-center justify-center h-[80vh] bg-gray-50 rounded-lg">
                        <div className="text-gray-500">
                            {t('workspace.advancedFields.noData')}
                        </div>
                    </div>
                )}
            </TabsContent>
        </>
    )
}