import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { RootState } from '@/shared/store'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import MapManager from '@/components/Map/core/MapManager'
import { displayValue } from './displayUtils'

interface DataTableProps {
    records: any[]
    onViewRecord: (record: any) => void
}

export const DataTable = ({ records, onViewRecord }: DataTableProps) => {
    const { t, i18n } = useTranslation()
    const { selectedTableLayer } = useSelector((state: RootState) => state.map)

    const getOrderedColumns = () => {
        if (!records.length) return []

        // Get all available columns
        const allColumns = Object.keys(
            records.find((record: any) => Object.keys(record?.data || {}).length > 0)
                ?.data || {}
        )

        // Get summary fields that exist in the columns
        const summaryColumns =
            selectedTableLayer?.summaryFields?.filter((field) =>
                allColumns.includes(field)
            ) || []

        // Add "Actions" column to whichever set of columns we're using
        const baseColumns =
            summaryColumns.length > 0 ? summaryColumns : allColumns.slice(0, 6)
        return [...baseColumns, 'Actions']
    }

    const columns = getOrderedColumns()

    const handleRowClick = (e: React.MouseEvent, record: any) => {
        // Only handle map interactions, don't open record details
        if ((e.target as HTMLElement).closest('button')) {
            return
        }

        const mapManager = MapManager.getInstance()

        if (record.geometry) {
            const geojsonFeature = {
                type: "Feature",
                geometry: record.geometry,
                properties: record.mapData
            }

            mapManager.highlightFeature(geojsonFeature)
            mapManager.goToFeature(record.geometry)
        }
    }

    if (records.length === 0) {
        return <div className='text-center p-3'>{t('workspace.advancedFields.noData')}</div>
    }

    return (
        <div className="flex-grow overflow-auto border rounded-lg m-2" dir={i18n.dir()}>
            <Table>
                <TableHeader>
                    <TableRow>
                        {columns.map((column) => (
                            <TableHead
                                key={column}
                                className={
                                    selectedTableLayer?.summaryFields?.includes(column)
                                        ? 'font-bold text-primary'
                                        : ''
                                }
                            >
                                {column}
                            </TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {records.map((record: any, index: number) => (
                        <TableRow
                            key={index}
                            onClick={(e) => handleRowClick(e, record)}
                            className={`cursor-pointer hover:bg-gray-100 `}
                        >
                            {columns.map((column) => (
                                <TableCell key={column}
                                    className={`text-left`}
                                >
                                    {column === 'Actions' ? (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => onViewRecord(record)}
                                        >
                                            {t('view')}
                                        </Button>
                                    ) : (
                                        displayValue(record?.data[column] ?? record?.data)
                                    )}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    )
}