import { Button } from '@/components/ui/button'
import {
    ChevronLeft,
    ChevronRight,
    ChevronsLeft,
    ChevronsRight,
} from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface PaginationControlsProps {
    currentPage: number
    totalPages: number
    totalCount: number
    itemsPerPage: number
    onPageChange: (page: number) => void
    loading: boolean
}

export const PaginationControls = ({
    currentPage,
    totalPages,
    totalCount,
    itemsPerPage,
    onPageChange,
    loading
}: PaginationControlsProps) => {
    const { t, i18n } = useTranslation()

    if (totalPages <= 1) {
        return null
    }

    return (
        <div className="flex items-center justify-between px-2 py-4 border-t">
            <div className={`flex items-center space-x-2 ${i18n.dir() === 'rtl' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(1)}
                    disabled={currentPage === 1 || loading}
                >
                    {i18n.dir() === 'rtl' ? (
                        <ChevronsRight className="h-4 w-4" />
                    ) : (
                        <ChevronsLeft className="h-4 w-4" />
                    )}
                </Button>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                >
                    {i18n.dir() === 'rtl' ? (
                        <ChevronRight className="h-4 w-4" />
                    ) : (
                        <ChevronLeft className="h-4 w-4" />
                    )}
                </Button>
            </div>

            <div className="text-sm text-gray-600">
                {t('page')} {currentPage} {t('of')} {totalPages}
                ({((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalCount)} {t('of')} {totalCount})
            </div>

            <div className={`flex items-center space-x-2 ${i18n.dir() === 'rtl' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || loading}
                >
                    {i18n.dir() === 'rtl' ? (
                        <ChevronLeft className="h-4 w-4" />
                    ) : (
                        <ChevronRight className="h-4 w-4" />
                    )}
                </Button>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(totalPages)}
                    disabled={currentPage === totalPages || loading}
                >
                    {i18n.dir() === 'rtl' ? (
                        <ChevronsLeft className="h-4 w-4" />
                    ) : (
                        <ChevronsRight className="h-4 w-4" />
                    )}
                </Button>
            </div>
        </div>
    )
}