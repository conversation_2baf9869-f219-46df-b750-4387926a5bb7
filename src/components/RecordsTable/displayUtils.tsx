import { ImagePreview } from '../../components/RecordsTable/ImagePreview';
import { NestedTable } from '@/components/RecordsTable/NestedTable';
import { cleanAndParseJson, isImageUrl } from '@/shared/utils/tabelrecordHelper';
import i18next from 'i18next'

export const displayValue = (value: any, inDetailView = false) => {
    if (value === null || value === undefined || value === '') {
        return '_'
    }
    if (!isNaN(value)) {
        return value.toString()
    }

    const viewInDetails = <span className="text-gray-500 italic">{i18next.t('viewInDetails')}</span>

    if (typeof value === 'string') {
        if (isImageUrl(value)) {
            return <ImagePreview src={value} />
        }
        const parsedJson = cleanAndParseJson(value)
        if (parsedJson) {
            return inDetailView ?
                <NestedTable data={parsedJson} /> : viewInDetails
        }
        return value
    }
    if (typeof value === 'object') {
        return inDetailView ?
            <NestedTable data={value} /> : viewInDetails
    }
    return value
}

/**
 * Adds cache-busting parameter to a URL to prevent browser caching
 * 
 * @param {string} url - The URL to add cache-busting to
 * @returns {string} URL with cache-busting parameter appended
 * @example
 * // Returns "https://example.com/image.jpg?cb=1625097600000"
 * addCacheBusting("https://example.com/image.jpg")
 * 
 * // Returns "https://example.com/image.jpg?param=value&cb=1625097600000"
 * addCacheBusting("https://example.com/image.jpg?param=value")
 * 
 * // Returns original URL for special URL types
 * addCacheBusting("data:image/png;base64,...")
 */
export const addCacheBusting = (url: string) => {
    if (!url || typeof url !== 'string') return url;

    // Skip if already has cache-busting or is a special URL
    if (url.includes('?cb=') ||
        url.startsWith('data:') ||
        url.startsWith('blob:')) {
        return url;
    }

    // Simple cache-busting with timestamp
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}cb=${Date.now()}`;
};

/**
 * Processes record data to add cache-busting parameters to image URLs
 * 
 * This function traverses the record data object and applies cache-busting
 * to any string values that are identified as image URLs. It creates a new
 * object with the processed values, leaving the original data unchanged.
 * 
 * @param {Record<string, any>} data - The record data object to process
 * @returns {Record<string, any>} A new object with cache-busting applied to image URLs
 * 
 * @example
 * // Returns { id: 1, name: "Test", image: "https://example.com/image.jpg?cb=1625097600000" }
 * processRecordData({ id: 1, name: "Test", image: "https://example.com/image.jpg" })
 */
export const processRecordData = (data: Record<string, any>): Record<string, any> => {
    const processedData = { ...data }

    Object.keys(processedData).forEach(key => {
        const value = processedData[key]
        if (typeof value === 'string') {
            if (isImageUrl(value)) {
                processedData[key] = addCacheBusting(value)
            }
            // Add other URL types here if needed
        }
    })

    return processedData
}