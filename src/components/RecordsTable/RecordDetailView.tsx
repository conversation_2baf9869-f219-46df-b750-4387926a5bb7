import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { CustomThemedForm } from '@/components/RJSF/theme/ThemedForm'
import { displayValue, processRecordData } from './displayUtils'
import { useEffect, useState, useMemo } from 'react'

interface RecordDetailViewProps {
    record: any
    currentRecordIndex: number
    currentPage: number
    totalCount: number
    totalPages: number
    itemsPerPage: number
    records: any[]
    onBack: () => void
    onNavigate: (direction: number) => void
    loading: boolean
}

export const RecordDetailView = ({
    record,
    currentRecordIndex,
    currentPage,
    totalCount,
    totalPages,
    itemsPerPage,
    records,
    onBack,
    onNavigate,
    loading
}: RecordDetailViewProps) => {
    const { t, i18n } = useTranslation()
    const { selectedTableLayer } = useSelector((state: RootState) => state.map)
    const [refreshKey, setRefreshKey] = useState(0)
    const direction = i18n.dir()

    // Check if we have schema for structured form view
    const hasSchema = selectedTableLayer?.jsonSchema

    // Process record with cache-busting
    const processedRecord = useMemo(() => {
        if (!record?.data) return record

        return {
            ...record,
            data: processRecordData(record.data)
        }
    }, [record, refreshKey])

    // Force refresh when record changes
    useEffect(() => setRefreshKey(prev => prev + 1), [record])

    const renderStructuredView = () => (
        <div key={`structured-${refreshKey}`} className="space-y-4 p-4 border rounded-lg max-h-[calc(80vh-140px)] overflow-y-auto">
            <CustomThemedForm
                schema={selectedTableLayer?.jsonSchema}
                uiSchema={selectedTableLayer?.webUiJsonSchema ?? {}}
                formData={processedRecord.data}
                disabled={true}
                onSubmit={() => { }}
            >
                <div />
            </CustomThemedForm>
        </div>
    )

    const renderFallbackView = () => (
        <div key={`fallback-${refreshKey}`} className="space-y-4 p-4 border rounded-lg max-h-[calc(80vh-140px)] overflow-y-auto">
            {Object.entries(processedRecord.data).map(([key, value]) => (
                <div key={`${key}-${refreshKey}`} className="flex flex-col sm:flex-row items-start gap-4">
                    <div className="min-w-[25%] font-medium text-gray-700 break-words">
                        {key}:
                    </div>
                    <div className="flex-1 w-full sm:w-3/4 bg-gray-50 p-2 rounded">
                        {displayValue(value, true)}
                    </div>
                </div>
            ))}
        </div>
    )

    const navigationButtonClass = "h-4 w-4" + (direction === 'rtl' ? " ml-1" : " mr-1")

    return (
        <div className="flex-grow p-4" dir={direction}>
            <div className="mb-4 flex justify-between items-center">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onBack}
                    className="flex items-center gap-2"
                >
                    {direction === 'rtl' ? (
                        <ChevronRight className={navigationButtonClass} />
                    ) : (
                        <ChevronLeft className={navigationButtonClass} />
                    )}
                    {t('backToTable')}
                </Button>

                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onNavigate(-1)}
                        disabled={(currentRecordIndex === 0 && currentPage === 1) || loading}
                    >
                        {direction === 'rtl' ? (
                            <ChevronRight className={navigationButtonClass} />
                        ) : (
                            <ChevronLeft className={navigationButtonClass} />
                        )}
                        {t('buttons.previous')}
                    </Button>

                    <div className="flex items-center px-3 text-sm text-gray-600">
                        {((currentPage - 1) * itemsPerPage) + currentRecordIndex + 1} / {totalCount}
                    </div>

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onNavigate(1)}
                        disabled={(currentRecordIndex === records.length - 1 && currentPage === totalPages) || loading}
                    >
                        {t('buttons.next')}
                        {direction === 'rtl' ? (
                            <ChevronLeft className={navigationButtonClass} />
                        ) : (
                            <ChevronRight className={navigationButtonClass} />
                        )}
                    </Button>
                </div>
            </div>

            {hasSchema ? renderStructuredView() : renderFallbackView()}
        </div>
    )
}