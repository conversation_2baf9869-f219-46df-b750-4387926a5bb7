import { useEffect, useState } from 'react'
import { useQuery } from '@apollo/client'
import { useSelector, useDispatch } from 'react-redux'
import { useParams, useSearchParams } from 'react-router-dom'
import { GET_LAYER_RECORDS } from '@/shared/graphQl/queries/layers'
import { RootState } from '@/shared/store'
import { DataTable } from './DataTable'
import { RecordDetailView } from './RecordDetailView'
import { PaginationControls } from './PaginationControls'
import { transformFiltersToFilterGroups } from '@/shared/utils/filters'
import { updateWorkspaceRecordsCount } from '@/shared/store/slices/workspaceSlice'
import { updateLayer } from '@/shared/store/slices/mapSlice'

const ITEMS_PER_PAGE = 20

export const DataExplorer = () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [selectedRecord, setSelectedRecord] = useState<any>(null)
    const [isInDetailView, setIsInDetailView] = useState(false)
    const [currentRecordIndex, setCurrentRecordIndex] = useState(0)

    const dispatch = useDispatch()
    const { selectedTableLayer, selectedMainLayer, mapMode } = useSelector((state: RootState) => state.map)
    const { workspaceId } = useParams()
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get("orgId")

    const isDetailsMode = mapMode === 'details'
    const currentLayer = isDetailsMode ? selectedMainLayer : selectedTableLayer

    const { loading, data, refetch } = useQuery(GET_LAYER_RECORDS, {
        variables: {
            layerId: parseInt(currentLayer?.id.toString() || ''),
            orgId: orgId,
            limit: ITEMS_PER_PAGE,
            offset: (currentPage - 1) * ITEMS_PER_PAGE,
            filterGroups: transformFiltersToFilterGroups(selectedMainLayer?.filters?.graphql_filters) || null,
        },
        skip: !currentLayer?.id || !orgId,
    })

    const totalCount = data?.records?.count || 0
    const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE)
    const records = data?.records?.data || []

    const handleNavigateRecord = async (direction: number) => {
        const newIndex = currentRecordIndex + direction;

        // If moving forward and at the end of current page
        if (direction === 1 && newIndex >= records.length && currentPage < totalPages) {
            try {
                setCurrentPage(currentPage + 1);
                setCurrentRecordIndex(0); // Start at beginning of new page
            } catch (error) {
                console.error("Error fetching next page:", error);
            }
            return;
        }

        // If moving backward and at the start of current page
        if (direction === -1 && newIndex < 0 && currentPage > 1) {
            try {
                setCurrentPage(currentPage - 1);
                setCurrentRecordIndex(ITEMS_PER_PAGE - 1); // Start at end of previous page
            } catch (error) {
                console.error("Error fetching previous page:", error);
            }
            return;
        }

        // Normal navigation within current page
        if (newIndex >= 0 && newIndex < records.length) {
            setCurrentRecordIndex(newIndex);
            setSelectedRecord(records[newIndex]);
        }
    };

    const handleViewRecord = (record: any) => {
        const index = records.findIndex((r: any) => r === record);
        setCurrentRecordIndex(index);
        setSelectedRecord(record);
        setIsInDetailView(true);
    };

    const handleBackToTable = () => {
        setSelectedRecord(null);
        setIsInDetailView(false);
    };

    const goToPage = (page: number) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)))
    }

    useEffect(() => {
        setCurrentPage(1)
        if (selectedTableLayer) {
            refetch()
        }
    }, [workspaceId, selectedTableLayer?.id, refetch])

    useEffect(() => {
        const handleRecordsUpdate = () => {
            refetch();
            dispatch(updateWorkspaceRecordsCount());
        };

        window.addEventListener('wmsUpdate', handleRecordsUpdate);
        return () => {
            window.removeEventListener('wmsUpdate', handleRecordsUpdate);
        };
    }, [refetch]);

    // Only update selectedRecord when we're in detail view AND records change
    useEffect(() => {
        if (isInDetailView && records.length > 0 && currentRecordIndex >= 0 && currentRecordIndex < records.length) {
            setSelectedRecord(records[currentRecordIndex]);
        }
    }, [records, currentRecordIndex, isInDetailView]);

    useEffect(() => {
        // Update selectedMainLayer's recordsCount when totalCount changes
        if (selectedMainLayer?.id && totalCount !== selectedMainLayer?.recordsCount) {
            dispatch(updateLayer({
                id: selectedMainLayer.id,
                updates: {
                    recordsCount: totalCount
                }
            }));
        }
    }, [totalCount, selectedMainLayer, dispatch]);

    return (
        <div className="flex-grow flex flex-col max-h-[74vh]">
            {selectedRecord ? (
                <RecordDetailView
                    record={selectedRecord}
                    currentRecordIndex={currentRecordIndex}
                    currentPage={currentPage}
                    totalCount={totalCount}
                    totalPages={totalPages}
                    itemsPerPage={ITEMS_PER_PAGE}
                    records={records}
                    onBack={handleBackToTable}
                    onNavigate={handleNavigateRecord}
                    loading={loading}
                />
            ) : (
                <>
                    <DataTable
                        records={records}
                        onViewRecord={handleViewRecord}
                    />
                    <PaginationControls
                        currentPage={currentPage}
                        totalPages={totalPages}
                        totalCount={totalCount}
                        itemsPerPage={ITEMS_PER_PAGE}
                        onPageChange={goToPage}
                        loading={loading}
                    />
                </>
            )}
        </div>
    )
}