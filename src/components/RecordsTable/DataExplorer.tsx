import { useEffect, useState, useRef } from 'react'
import { useQuery } from '@apollo/client'
import { useSelector, useDispatch } from 'react-redux'
import { useParams, useSearchParams } from 'react-router-dom'
import { GET_LAYER_RECORDS } from '@/shared/graphQl/queries/layers'
import { RootState } from '@/shared/store'
import { DataTable } from './DataTable'
import { RecordDetailView } from './RecordDetailView'
import { PaginationControls } from './PaginationControls'
import { updateWorkspace } from '@/shared/store/slices/workspaceSlice'

const ITEMS_PER_PAGE = 20

export const DataExplorer = () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [selectedRecord, setSelectedRecord] = useState<any>(null)
    const [isInDetailView, setIsInDetailView] = useState(false)
    const [currentRecordIndex, setCurrentRecordIndex] = useState(0)
    const dispatch = useDispatch()

    // Add a ref to track the previous totalCount
    const prevTotalCountRef = useRef<number | null>(null)

    const { selectedTableLayer } = useSelector((state: RootState) => state.map)
    const selectedWorkspace = useSelector((state: RootState) => state.workspace.selectedWorkspace)
    const { workspaceId } = useParams()
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get("orgId")

    const { loading, data, refetch } = useQuery(GET_LAYER_RECORDS, {
        variables: {
            layerId: parseInt(selectedTableLayer?.id.toString() || ''),
            orgId: orgId,
            limit: ITEMS_PER_PAGE,
            offset: (currentPage - 1) * ITEMS_PER_PAGE,
        },
        skip: !selectedTableLayer?.id || !orgId,
    })

    const totalCount = data?.records?.count || 0
    const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE)
    const records = data?.records?.data || []

    // Update the selectedWorkspace's recordsCount when totalCount changes
    useEffect(() => {
        // Only update if totalCount has changed and is different from the previous value
        if (
            selectedWorkspace &&
            totalCount > 0 &&
            prevTotalCountRef.current !== totalCount &&
            selectedWorkspace.layersData?.recordsCount !== totalCount
        ) {
            prevTotalCountRef.current = totalCount;

            dispatch(updateWorkspace({
                id: selectedWorkspace.id,
                updates: {
                    layersData: {
                        ...selectedWorkspace.layersData,
                        recordsCount: totalCount
                    }
                }
            }));
        }
    }, [totalCount, selectedWorkspace]);

    const handleNavigateRecord = async (direction: number) => {
        const newIndex = currentRecordIndex + direction;

        // If moving forward and at the end of current page
        if (direction === 1 && newIndex >= records.length && currentPage < totalPages) {
            try {
                setCurrentPage(currentPage + 1);
                setCurrentRecordIndex(0); // Start at beginning of new page
            } catch (error) {
                console.error("Error fetching next page:", error);
            }
            return;
        }

        // If moving backward and at the start of current page
        if (direction === -1 && newIndex < 0 && currentPage > 1) {
            try {
                setCurrentPage(currentPage - 1);
                setCurrentRecordIndex(ITEMS_PER_PAGE - 1); // Start at end of previous page
            } catch (error) {
                console.error("Error fetching previous page:", error);
            }
            return;
        }

        // Normal navigation within current page
        if (newIndex >= 0 && newIndex < records.length) {
            setCurrentRecordIndex(newIndex);
            setSelectedRecord(records[newIndex]);
        }
    };

    const handleViewRecord = (record: any) => {
        const index = records.findIndex((r: any) => r === record);
        setCurrentRecordIndex(index);
        setSelectedRecord(record);
        setIsInDetailView(true);
    };

    const handleBackToTable = () => {
        setSelectedRecord(null);
        setIsInDetailView(false);
    };

    const goToPage = (page: number) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)))
    }

    useEffect(() => {
        setCurrentPage(1)
        if (selectedTableLayer) {
            refetch()
        }
    }, [workspaceId, selectedTableLayer?.id, refetch])

    useEffect(() => {
        const handleRecordsUpdate = () => {
            refetch();
        };

        window.addEventListener('wmsUpdate', handleRecordsUpdate);
        return () => {
            window.removeEventListener('wmsUpdate', handleRecordsUpdate);
        };
    }, [refetch]);

    // Only update selectedRecord when we're in detail view AND records change
    useEffect(() => {
        if (isInDetailView && records.length > 0 && currentRecordIndex >= 0 && currentRecordIndex < records.length) {
            setSelectedRecord(records[currentRecordIndex]);
        }
    }, [records, currentRecordIndex, isInDetailView]);

    return (
        <div className="flex-grow flex flex-col max-h-[74vh]">
            {selectedRecord ? (
                <RecordDetailView
                    record={selectedRecord}
                    currentRecordIndex={currentRecordIndex}
                    currentPage={currentPage}
                    totalCount={totalCount}
                    totalPages={totalPages}
                    itemsPerPage={ITEMS_PER_PAGE}
                    records={records}
                    onBack={handleBackToTable}
                    onNavigate={handleNavigateRecord}
                    loading={loading}
                />
            ) : (
                <>
                    <DataTable
                        records={records}
                        onViewRecord={handleViewRecord}
                    />
                    <PaginationControls
                        currentPage={currentPage}
                        totalPages={totalPages}
                        totalCount={totalCount}
                        itemsPerPage={ITEMS_PER_PAGE}
                        onPageChange={goToPage}
                        loading={loading}
                    />
                </>
            )}
        </div>
    )
}