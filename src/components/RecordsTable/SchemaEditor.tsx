import { useState, useEffect } from 'react'
import { useMutation } from '@apollo/client'
import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { useSearchParams } from 'react-router-dom'
import React<PERSON><PERSON> from 'react-json-view'
import { Button } from '@/components/ui/button'
import { Edit2, Save, X, Copy } from 'lucide-react'
import { UPDATE_LAYER } from '@/shared/graphQl/mutations/layers'
import { RootState } from '@/shared/store'
import { updateLayer } from '@/shared/store/slices/mapSlice'
import { showToast } from '@/shared/utils/toastConfig'

export const SchemaEditor = () => {
    const { t } = useTranslation()
    const dispatch = useDispatch()
    const [searchParams] = useSearchParams()
    const orgId = searchParams.get("orgId")

    const { selectedTableLayer } = useSelector((state: RootState) => state.map)
    const jsonSchema = selectedTableLayer?.jsonSchema

    const [isEditing, setIsEditing] = useState(false)
    const [editedSchema, setEditedSchema] = useState('')
    const [copySuccess, setCopySuccess] = useState<string | null>(null)

    const [updateLayerBackend, { loading: updating }] = useMutation(UPDATE_LAYER)

    useEffect(() => {
        // Clear copy success message after 2 seconds
        if (copySuccess) {
            const timer = setTimeout(() => {
                setCopySuccess(null);
            }, 2000);
            return () => clearTimeout(timer);
        }
    }, [copySuccess]);

    const handleSchemaEdit = async () => {
        if (!isEditing) {
            setEditedSchema(JSON.stringify(jsonSchema, null, 2))
            setIsEditing(true)
            return
        }

        if (!selectedTableLayer) return

        try {
            const parsedSchema = JSON.parse(editedSchema)

            await updateLayerBackend({
                variables: {
                    dataInput: {
                        layerId: selectedTableLayer.id,
                        jsonSchema: JSON.stringify(parsedSchema),
                        orgId
                    },
                },
            })

            dispatch(
                updateLayer({
                    id: selectedTableLayer.id,
                    updates: { jsonSchema: parsedSchema },
                })
            )
            setIsEditing(false)
            showToast.success(t('alerts.success.recordUpdated'))
        } catch (error) {
            console.error(error)
            showToast.error(t('alerts.error.recordUpdate'))
        }
    }

    const handleCancelEdit = () => {
        setIsEditing(false)
        setEditedSchema('')
    }

    const handleCopyToClipboard = () => {
        const textToCopy = isEditing
            ? editedSchema
            : JSON.stringify(jsonSchema, null, 2);

        navigator.clipboard.writeText(textToCopy)
            .then(() => {
                setCopySuccess('Schema copied!');
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
                setCopySuccess('Copy failed');
            });
    };

    return (
        <>
            <div className="flex justify-between items-center mb-2">
                <div className="flex gap-2">
                    {isEditing && (
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={handleCancelEdit}
                            className="h-8 w-8"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    )}
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleSchemaEdit}
                        disabled={updating}
                        className="h-8 w-8"
                    >
                        {isEditing ? (
                            <Save className="h-4 w-4" />
                        ) : (
                            <Edit2 className="h-4 w-4" />
                        )}
                    </Button>
                </div>
                <div className="flex items-center">
                    {copySuccess && (
                        <span className="text-green-600 text-sm mr-2">{copySuccess}</span>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopyToClipboard}
                        className="flex items-center gap-1 px-3 py-1.5 text-sm font-medium rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                        title="Copy JSON Schema"
                    >
                        <Copy className="h-5 w-5 text-gray-700" />
                    </Button>
                </div>
            </div>

            {isEditing ? (
                <textarea
                    className="w-full h-[75vh] p-4 font-mono text-sm bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 border"
                    value={editedSchema}
                    onChange={(e) => setEditedSchema(e.target.value)}
                    placeholder="Enter valid JSON schema..."
                />
            ) : (
                <div className="bg-white border rounded-lg overflow-auto max-h-[70.5vh]">
                    <div className="p-4">
                        {jsonSchema ? (
                            <ReactJson
                                src={jsonSchema}
                                theme="rjv-default"
                                displayDataTypes={false}
                                displayObjectSize={false}
                                enableClipboard={false}
                                collapsed={2}
                                name={false}
                            />
                        ) : (
                            <div className="text-gray-500 italic">No schema available</div>
                        )}
                    </div>
                </div>
            )}
        </>
    )
}