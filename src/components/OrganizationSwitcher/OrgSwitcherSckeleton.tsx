import { Skeleton } from '@/components/Skeleton/Skeleton'

export const OrgSwitcherSkeleton = () => {
    return (
        <div className="grid grid-cols-3 gap-4 p-2">
            {Array(6).fill(null).map((_, index) => (
                <div
                    key={index}
                    className="relative p-2 rounded-lg bg-gray-50 border"
                >
                    <div className="flex items-center justify-center aspect-[4/3] mb-4 bg-[#E9E9E9] rounded-md">
                        <Skeleton className="w-12 h-12" />
                    </div>
                    <Skeleton className="h-5 w-24 mx-auto" />
                </div>
            ))}
        </div>
    )
}
