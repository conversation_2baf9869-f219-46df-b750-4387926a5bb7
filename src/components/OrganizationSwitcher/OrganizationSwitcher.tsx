import { useTranslation } from 'react-i18next'
import { useOrganizationManager } from '@/shared/hooks/useOrganizationManager'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '../ui/button'
import { OrgSwitcherSkeleton } from './OrgSwitcherSckeleton'
import { StepProgressManager } from '@/shared/utils/stepProgress'
import { WorkspaceMethods } from '@/shared/utils/routes'

interface OrganizationSwitcherProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

export const OrganizationSwitcher = ({ open, onOpenChange }: OrganizationSwitcherProps) => {
    const { selectedOrg, organizations, selectOrganization, count, loading } = useOrganizationManager()
    const { i18n, t } = useTranslation()
    const direction = i18n.dir()

    const handleSelect = (org: any) => {
        StepProgressManager.clearProgress(WorkspaceMethods.UPLOAD_FILE)
        StepProgressManager.clearProgress(WorkspaceMethods.DESIGN_LAYER)
        selectOrganization(org)
        onOpenChange(false)
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className={`${i18n.language === "ar" ? "rtl" : "ltr"} [&>button]:hidden max-w-4xl`}>
                <DialogHeader className={direction}>
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                            <DialogTitle> {t('switchOrganization')}</DialogTitle>
                        </div>
                        <Button variant="ghost" size="icon" className='no-border-focus' onClick={() => onOpenChange(false)}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </DialogHeader>

                {loading ? (
                    <OrgSwitcherSkeleton />
                ) : count === 0 ? (
                    <div className="flex flex-col items-center justify-center p-8 text-center">
                        <h3 className="text-xl font-semibold mb-2">{t('noOrganizations')}</h3>
                        <p className="text-gray-500">{t('pleaseContactAdmin')}</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-3 gap-4 p-2">
                        {organizations?.map((org: any) => (
                            <button
                                key={org.id}
                                onClick={() => handleSelect(org)}
                                className={cn(
                                    "relative p-2 rounded-lg bg-gray-50 transition-all",
                                    selectedOrg?.id === org.id
                                        ? "border border-purple-950"
                                        : "bg-white border"
                                )}
                            >
                                <div className="flex flex-col items-center justify-center">
                                    <div className="w-full aspect-square flex justify-center bg-white rounded-lg mb-2">
                                        <img
                                            src={org?.settings?.full_logo ?? org.settings.fullLogo}
                                            alt={org.settings.name}
                                            className="max-w-[95%] max-h-[95%] object-contain"
                                        />
                                    </div>
                                    <span className="block text-sm font-medium text-gray-900 text-center line-clamp-2">
                                        {org.settings.name}
                                    </span>
                                </div>
                            </button>
                        ))}
                    </div>
                )}
            </DialogContent>
        </Dialog>
    )
}
