import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { X } from 'lucide-react';

interface CreateWorkspaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (workspaceName: string, description: string) => Promise<void> | void;
  isLoading?: boolean;
}

export const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const { t, i18n } = useTranslation();
  const direction = i18n.dir();
  const [workspaceName, setWorkspaceName] = useState('');
  const [description, setDescription] = useState('');
  const [nameError, setNameError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset error
    setNameError('');

    // Validate workspace name
    if (!workspaceName.trim()) {
      setNameError(t('createWorkspaceModal.workspaceNameRequired'));
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(workspaceName.trim(), description.trim());
      // Reset form on successful submission
      setWorkspaceName('');
      setDescription('');
      onClose();
    } catch (error) {
      console.error('Error creating workspace:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !isLoading) {
      setWorkspaceName('');
      setDescription('');
      setNameError('');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className={`sm:max-w-md w-[400px] p-6 ${direction} bg-white rounded-lg shadow-lg border-0 [&>button]:hidden`}
        dir={i18n.dir()}
      >
        <DialogHeader className={direction}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <DialogTitle className="text-lg font-medium text-gray-900 m-0">
                {t('createWorkspaceModal.title')}
              </DialogTitle>
            </div>
            <Button variant="ghost" size="icon" className='no-border-focus' onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="workspaceName"
              className={`text-sm text-gray-700 ${direction} block`}
            >
              {t('createWorkspaceModal.workspaceName')}
            </Label>
            <Input
              id="workspaceName"
              type="text"
              value={workspaceName}
              onChange={(e) => {
                setWorkspaceName(e.target.value);
                if (nameError) setNameError('');
              }}
              placeholder={t('createWorkspaceModal.workspaceNamePlaceholder')}
              className={`w-full h-10 px-3 no-border-focus rounded-md bg-white ${direction} ${nameError ? 'border-red-500' : ''}`}
              disabled={isSubmitting || isLoading}
              dir={direction}
            />
            {nameError && (
              <p className={`text-sm text-red-500 ${direction}`}>
                {nameError}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="description"
              className={`text-sm text-gray-700 ${direction} block`}
            >
              {t('createWorkspaceModal.workspaceDescription')}
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t('createWorkspaceModal.workspaceDescriptionPlaceholder')}
              className={`w-full h-20 px-3 py-2 no-border-focus rounded-md resize-none bg-white ${direction}`}
              disabled={isSubmitting || isLoading}
              dir={direction}
            />
          </div>

          <div className="pt-2">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading || !workspaceName.trim()}
              className="h-10 bg-[#5E58EE] hover:bg-[#5E58EE] text-white font-medium rounded-md transition-colors duration-200 shadow-none border-0"
            >
              {isSubmitting || isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {t('createWorkspaceModal.creating')}
                </div>
              ) : (
                t('createWorkspaceModal.create')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

