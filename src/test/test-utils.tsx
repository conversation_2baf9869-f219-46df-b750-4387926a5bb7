import { render as rtlRender, RenderOptions, RenderResult } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '@/shared/store';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n';
import { BrowserRouter } from 'react-router-dom';
import { FC, ReactElement, ReactNode } from 'react';

interface WrapperProps {
  children: ReactNode;
}

const AllTheProviders: FC<WrapperProps> = ({ children }) => {
  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </I18nextProvider>
    </Provider>
  );
};

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  route?: string;
}

function customRender(
  ui: ReactElement,
  { route = '/', ...options }: CustomRenderOptions = {}
): RenderResult {
  window.history.pushState({}, 'Test page', route);
  
  return rtlRender(ui, { 
    wrapper: AllTheProviders,
    ...options,
  });
}

export { customRender as render };
export * from '@testing-library/react';