* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*:focus {
  outline: none;
  box-shadow: none;
}

/* For Chrome, Safari */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #bfbdbd;
  border-radius: 6px;
  border: 3px solid #f1f1f1;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #bfbdbd #f1f1f1;
}

body {
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  height: 100%;
}

.rtl {
  text-align: right;
  direction: rtl;
}

.ltr {
  text-align: left;
  direction: ltr;
}

.no-border-focus {
  @apply focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0;
}

.customRow fieldset {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
}

.ol-tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  opacity: 0.7;
  white-space: nowrap;
  font-size: 14px;
  cursor: default;
  user-select: none;
}
.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
}
.ol-tooltip-static {
  background-color: #ffcc33;
  color: black;
  border: 1px solid white;
}
.ol-tooltip-measure:before,
.ol-tooltip-static:before {
  border-top: 6px solid rgba(0, 0, 0, 0.5);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: '';
  position: absolute;
  bottom: -6px;
  margin-left: -7px;
  left: 50%;
}
.ol-tooltip-static:before {
  border-top-color: #ffcc33;
}
.ol-overlaycontainer-stopevent {
  direction: ltr;
}

.delete-measure {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

.ol-tooltip:hover .delete-measure {
  opacity: 1;
}

/* BarLoader animation */
@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.Toastify__toast--success {
  --toastify-icon-color-success: #238636; /* White icon */
  --toastify-color-progress-success: #238636; /* Loading bar */
}

.custom-ol-zoom {
  position: absolute;
  top: 18px;
  left: 10px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  z-index: 1000;
}

.custom-ol-zoom button {
  all: unset;
  border: none !important;
  outline: none !important;
  font-size: 22px;
  padding: 5px 10px;
  text-align: center;
  color: #222;
  cursor: pointer;
}
.custom-ol-zoom button:hover {
  border: none;
}

.rtl-scrollbar [data-radix-scroll-area-viewport] + div {
  left: 0 !important;
  right: auto !important;
}

.ltr-scrollbar [data-radix-scroll-area-viewport] + div {
  right: 0 !important;
  left: auto !important;
}

.custom-scroll-area [data-radix-scroll-area-viewport] > div {
  display: block !important;
  min-width: auto !important;
}

[data-radix-scroll-area-viewport] > div[style*='display: table'] {
  height: 100% !important;
  min-height: 100% !important;
  display: table !important;
}
