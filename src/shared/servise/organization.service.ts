import { graphQlAppClient } from '@/shared/graphQl'
import {
  GET_ROLES,
  GET_USERS_QUERY,
  GET_ALL_USERS,
  GET_WORKSPACE_PERMISSIONS,
  GET_PERMISSIONS_BY_ORG_ID,
  WORKSPACE_USERS_QUERY,
} from '@/shared/graphQl/queries/organization'
import {
  ADD_USER_MUTATION,
  CHANGE_USER_ACTIVE_STATUS_MUTATION,
  CHANGE_USER_ROLE_MUTATION,
  CREATE_ROLE,
  UPDATE_ROLE,
  DELETE_ROLE,
  UPDATE_ORGANIZATION_MUTATION,
  ASSIGN_WORKSPACE_USER_PERMISSIONS,
} from '@/shared/graphQl/mutations/organization'
import { UserStatus } from '@/shared/types/userStatus'

export class OrganizationService {
  // Roles
  static async fetchRoles(orgId: string | number) {
    const { data } = await graphQlAppClient.query({
      query: GET_ROLES,
      variables: { orgId: Number(orgId) },
      fetchPolicy: 'network-only',
    })

    return data.roles.data.map((role: any) => ({
      id: role.id,
      title: role.title,
      users: role.usersCount || 0,
      usersCount: role.usersCount || 0,
      codename: role.codename,
      description: role.description || '',
      permissions: role.permissions.map((p: any) => p.id),
    }))
  }

  static async createRole(
    orgId: string | number,
    title: string,
    permissions: string[]
  ) {
    return graphQlAppClient.mutate({
      mutation: CREATE_ROLE,
      variables: {
        dataInput: {
          orgId: Number(orgId),
          title,
          permissionsList: permissions.map((id) => Number(id)),
        },
      },
    })
  }

  static async updateRole(
    orgId: string | number,
    roleId: string,
    title: string,
    permissions: string[]
  ) {
    return graphQlAppClient.mutate({
      mutation: UPDATE_ROLE,
      variables: {
        dataInput: {
          orgId: Number(orgId),
          roleId: Number(roleId),
          title,
          permissionsList: permissions.map((id) => Number(id)),
        },
      },
    })
  }

  static async deleteRole(orgId: string | number, roleId: string) {
    return graphQlAppClient.mutate({
      mutation: DELETE_ROLE,
      variables: {
        dataInput: {
          orgId: Number(orgId),
          roleId: Number(roleId),
        },
      },
    })
  }

  // Users
  static async fetchUsers(
    orgId: string | number,
    page: number,
    limit: number,
    searchTerm: string
  ) {
    const filters = searchTerm
      ? [{ field: 'first_name', value: searchTerm, clause: 'icontains' }]
      : []

    const { data } = await graphQlAppClient.query({
      query: GET_USERS_QUERY,
      variables: {
        orgId,
        limit,
        offset: (page - 1) * limit,
        filters,
      },
      fetchPolicy: 'network-only',
    })

    return {
      users: data.users.data,
      totalCount: data.users.count,
    }
  }

  static async fetchAllUsers(orgId: string | number) {
    const { data } = await graphQlAppClient.query({
      query: GET_ALL_USERS,
      variables: { orgId },
      fetchPolicy: 'network-only',
    })

    return {
      users: data.users.data,
      totalCount: data.users.count,
    }
  }

  static async addUser(
    orgId: string | number,
    firstName: string,
    lastName: string,
    email: string,
    roleId: string,
    phone: string
  ) {
    return graphQlAppClient.mutate({
      mutation: ADD_USER_MUTATION,
      variables: {
        dataInput: {
          orgId,
          firstName,
          lastName,
          email,
          roleId,
          phone,
        },
      },
    })
  }

  static async changeUserRole(
    orgId: string | number,
    userId: string,
    roleId: string
  ) {
    return graphQlAppClient.mutate({
      mutation: CHANGE_USER_ROLE_MUTATION,
      variables: {
        dataInput: {
          orgId,
          userId,
          roleId,
        },
      },
    })
  }

  static async changeUserStatus(
    orgId: string | number,
    userId: string,
    activeStatus: UserStatus
  ) {
    return graphQlAppClient.mutate({
      mutation: CHANGE_USER_ACTIVE_STATUS_MUTATION,
      variables: {
        dataInput: {
          orgId,
          userId,
          activeStatus,
        },
      },
    })
  }

  // Permissions
  static async fetchPermissions(orgId: string | number) {
    const { data } = await graphQlAppClient.query({
      query: GET_PERMISSIONS_BY_ORG_ID,
      variables: { orgId: Number(orgId) },
      fetchPolicy: 'network-only',
    })

    return data.permissions
  }

  static async fetchWorkspacePermissions(orgId: string | number) {
    const { data } = await graphQlAppClient.query({
      query: GET_WORKSPACE_PERMISSIONS,
      variables: { orgId: Number(orgId) },
      fetchPolicy: 'network-only',
    })

    return data.workspacePermissions
  }

  // Workspace Users
  static async fetchWorkspaceUsers(
    orgId: string | number,
    workspaceId: string | number,
    excludeIndividuals = false
  ) {
    const { data } = await graphQlAppClient.query({
      query: WORKSPACE_USERS_QUERY,
      variables: {
        orgId,
        workspaceId: Number(workspaceId),
        excludeIndividuals,
      },
      fetchPolicy: 'network-only',
    })

    return data.users.data
  }

  static async assignWorkspacePermissions(
    orgId: string | number,
    workspaceId: string | number,
    userIds: string[],
    permissionIds: string[]
  ) {
    return graphQlAppClient.mutate({
      mutation: ASSIGN_WORKSPACE_USER_PERMISSIONS,
      variables: {
        dataInput: {
          orgId,
          workspaceId: Number(workspaceId),
          usersIds: userIds.map((id) => Number(id)),
          permissionsIds: permissionIds.map((id) => Number(id)),
        },
      },
    })
  }

  // Organization
  static async updateOrganization(orgId: string | number, settings: any) {
    return graphQlAppClient.mutate({
      mutation: UPDATE_ORGANIZATION_MUTATION,
      variables: {
        dataInput: {
          orgId: Number(orgId),
          settings,
        },
      },
    })
  }
}
