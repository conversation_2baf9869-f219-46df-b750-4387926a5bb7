import { LoginPage } from "@/pages/Login/LoginPage";
import { createBrowserRouter, Navigate } from "react-router-dom";
import { Routes } from "./utils/routes";
import Layout from "@/components/Layout/Layout";
// import { LandingPage } from "@/pages/Landing/LandingPge";
import { MarketPage } from "@/pages/MarketPage/MarketPage";
import { DashboardPage } from "@/pages/Dashboard/DashboardPage";
import { MapPage } from "@/pages/Map/MapPage";
import { ProtectedRoute } from "./ProtectedRoute";
import { App } from "@/App";
import { CreateWorkspace } from "@/pages/CreateWorkspace/CreateWorkspace";
import { ErrorPage } from "@/pages/ErrorPage/Error";
import { OrganizationSettingsPage } from "@/pages/OrganizationSettingsPage/OrganizationSettingsPage";

export const router = createBrowserRouter([
  {
    path: '',
    element: <App />,
    errorElement: <div><ErrorPage /></div>,
    children: [
      {
        path: Routes.login,
        element: <LoginPage />,
      },
      {
        path: Routes.landing,
        element: <Navigate to={Routes.login} replace />,
      },
      {
        path: '*',
        element: <ProtectedRoute />,
        children: [
          {
            element: <Layout />,
            children: [
              {
                path: Routes.dashboard,
                children: [
                  {
                    index: true,
                    element: <Navigate to="workspaces" replace />
                  },
                  {
                    path: 'market',
                    element: <MarketPage />
                  },
                  {
                    path: 'workspaces',
                    element: <DashboardPage />
                  }
                ]
              },
              {
                path: Routes.map,
                element: <MapPage />
              }
            ],
          },
          {
            path: Routes.createWorkspace,
            element: <CreateWorkspace />,
            children: [
              {
                path: ':method/:step',
                element: <CreateWorkspace />
              },
            ]
          }, {
            path: Routes.mapWorkspace,
            element: <MapPage />
          }, {
            path: Routes.organizationSettings,
            element: <OrganizationSettingsPage />
          }
        ]
      }
    ],
  },
]);