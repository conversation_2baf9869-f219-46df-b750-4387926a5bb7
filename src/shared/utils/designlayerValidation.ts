import { FormField } from '@/components/MultiStepForm/steps/designLayerSteps/LayerFormStep/types'
import i18next from 'i18next'

export const validateForm = (formData: FormField) => {
  const errors: Record<string, string> = {}

  if (!formData.name) {
    errors.name = i18next.t(
      'workspace.layerForm.validation.identifier.required'
    )
  } else if (!/^[A-Za-z0-9]+$/.test(formData.name)) {
    errors.name = i18next.t('workspace.layerForm.validation.identifier.format')
  }

  if (!formData.title) {
    errors.title = i18next.t(
      'workspace.layerForm.validation.fieldTitle.required'
    )
  }

  if (!formData.type) {
    errors.type = i18next.t('workspace.layerForm.validation.fieldType.required')
  }

  return errors
}
