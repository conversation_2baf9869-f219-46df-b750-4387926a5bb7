import { Dispatch } from 'redux'
import {
  addBackendLayer,
  resetBackendLayers,
  setSelectedMainLayer,
  setSelectedTableLayer,
  type LayerMetadata,
} from '@/shared/store/slices/mapSlice'

export interface Layer extends Omit<LayerMetadata, 'id'> {
  id: number
  status: string
  isVisible?: boolean
  zIndex?: number
}

export interface LayersData {
  layers?: {
    data?: Layer[]
  }
}

export interface WorkspaceData {
  workspaces?: {
    data?: Array<{
      layersSortedIds?: string[]
    }>
  }
}

export const processLayers = (
  layersData: LayersData,
  workspaceData: WorkspaceData,
  isFromCreation: boolean,
  dispatch: Dispatch
): void => {
  if (!layersData.layers?.data) return

  dispatch(resetBackendLayers())
  const sortedIds = workspaceData.workspaces?.data?.[0]?.layersSortedIds

  const filteredLayers = isFromCreation
    ? layersData.layers.data
    : layersData.layers.data.filter(
        (layer) => layer.status === 'PUBLISHED'
      )

  let processedLayers: Layer[]
  if (sortedIds) {
    processedLayers = [...filteredLayers]
      .sort((a, b) => {
        const aIndex = sortedIds.indexOf(String(a.id))
        const bIndex = sortedIds.indexOf(String(b.id))
        return bIndex - aIndex
      })
      .map((layer, index) => ({
        ...layer,
        isVisible: true,
        zIndex: index,
      }))
      .reverse()
  } else {
    processedLayers = filteredLayers.map((layer, index) => ({
      ...layer,
      isVisible: true,
      zIndex: index,
    })).reverse()
  }

  processedLayers.forEach((layer) => {
    dispatch(addBackendLayer(layer))
  })

  if (processedLayers.length > 0) {
    dispatch(
      setSelectedMainLayer({ ...processedLayers[0], fromLayerClick: true })
    )
    dispatch(setSelectedTableLayer(processedLayers[0]))
  }
}
