import { Dispatch } from 'redux'
import {
  addBackendLayer,
  resetBackendLayers,
  setSelectedMainLayer,
  setSelectedTableLayer,
} from '@/shared/store/slices/mapSlice'

export const processLayers = (
  layersData: any,
  workspaceData: any,
  isFromCreation: boolean,
  dispatch: Dispatch
) => {
  if (!layersData?.layers?.data) return

  dispatch(resetBackendLayers())
  const sortedIds = workspaceData?.workspaces?.data?.[0]?.layersSortedIds

  const filteredLayers = isFromCreation
    ? layersData.layers.data
    : layersData.layers.data.filter(
        (layer: any) => layer.status === 'PUBLISHED'
      )

  let processedLayers
  if (sortedIds) {
    processedLayers = [...filteredLayers]
      .sort((a, b) => {
        const aIndex = sortedIds.indexOf(a.id)
        const bIndex = sortedIds.indexOf(b.id)
        return bIndex - aIndex
      })
      .map((layer, index) => ({
        ...layer,
        isVisible: true,
        zIndex: index,
      }))
      .reverse()
  } else {
    processedLayers = filteredLayers.map((layer: any, index: number) => ({
      ...layer,
      isVisible: true,
      zIndex: index,
    }))
  }

  processedLayers.forEach((layer: any) => {
    dispatch(addBackendLayer(layer))
  })

  if (processedLayers.length > 0) {
    dispatch(
      setSelectedMainLayer({ ...processedLayers[0], fromLayerClick: true })
    )
    dispatch(setSelectedTableLayer(processedLayers[0]))
  }
}
