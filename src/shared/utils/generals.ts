import jsCookies from 'js-cookie'
import { StepProgressManager } from './stepProgress'
import { WorkspaceMethods } from './routes'

export const getFullToken = (): string | null => {
  const token = jsCookies.get('access_token')
  return token ? `Bearer ${token}` : null
}

export function processFile(file: File) {
  const { name, size, type } = file
  return new Promise((resolve, reject) => {
    const reader = new window.FileReader()
    reader.onerror = reject
    reader.onload = () => {
      resolve({
        // dataURL: addNameToDataURL(event.target?.result, name),
        name,
        size,
        file,
        type,
      })
    }
    reader.readAsDataURL(file)
  })
}

export function processFiles(files: FileList): any {
  return Promise.all([].map.call(files, processFile))
}

export const determineCurrentStep = (
  method: string = WorkspaceMethods.UPLOAD_FILE
) => {
  const maxSteps = method === WorkspaceMethods.UPLOAD_FILE ? 4 : 1
  for (let step = 0; step <= maxSteps; step++) {
    const progress = StepProgressManager.getProgress(step, method).status
    if (progress === 'incomplete') {
      if (method === WorkspaceMethods.UPLOAD_FILE && step == 2) continue
      return step
    }
  }
  return maxSteps
}

// Filter out empty properties
export const filterEmptyProperties = (data: Record<string, any>) => {
  return Object.fromEntries(
    Object.entries(data).filter(
      ([, value]) => value !== undefined && value !== null && value !== ''
    )
  )
}
