export const getPageNumbers = (currentPage: number, totalPages: number) => {
  let pages: (number | string)[] = []

  if (totalPages <= 5) {
    pages = Array.from({ length: totalPages }, (_, i) => i + 1)
  } else {
    pages.push(1)

    if (currentPage <= 3) {
      pages.push(2, 3, 4, '...', totalPages)
    } else if (currentPage >= totalPages - 2) {
      pages.push(
        '...',
        totalPages - 3,
        totalPages - 2,
        totalPages - 1,
        totalPages
      )
    } else {
      pages.push(
        '...',
        currentPage - 1,
        currentPage,
        currentPage + 1,
        '...',
        totalPages
      )
    }
  }

  return pages
}
