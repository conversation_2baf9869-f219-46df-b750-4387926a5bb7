/**
 * Checks if there's an existing dataset request with a workspace ID
 * @param workspaceExistenceData - Data returned from the CHECK_WORKSPACE_EXISTENCE query
 * @returns The workspace ID if found, otherwise null
 */
export const checkForExistingWorkspaceId = (
  workspaceExistenceData: any
): string | null => {
  if (workspaceExistenceData?.workspaceRequests?.data) {
    // Find the first dataset with a workspace ID
    const datasetWithWorkspace =
      workspaceExistenceData.workspaceRequests.data.find(
        (item: any) => item?.dataset?.workspace?.id
      )
    return datasetWithWorkspace?.dataset?.workspace?.id || null
  }
  return null
}
