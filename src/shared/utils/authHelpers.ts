export const handleUnauthorizedError = (error: any) => {
  // Handle GraphQL errors array
  if (
    error?.errors?.some(
      (err: any) =>
        err.extensions?.http?.status === 401 || err.message === 'Unauthorized'
    )
  ) {
    window.location.replace('/login')
    return true
  }

  // Handle single error object
  if (
    error?.message?.includes('401') ||
    error?.message?.includes('Unauthorized') ||
    error?.graphQLErrors?.[0]?.extensions?.http?.status === 401
  ) {
    window.location.replace('/login')
    return true
  }

  return false
}
