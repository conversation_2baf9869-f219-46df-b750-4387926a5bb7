import { UserStatus, getUserStatusLabel } from "@/shared/types/userStatus";
import { TFunction } from "i18next";

export const renderStatusBadge = (status: UserStatus, t: TFunction) => {
    const statusLabel = getUserStatusLabel(status, t);

    switch (status) {
        case UserStatus.ACTIVE:
            return (
                <span className="text-green-600 bg-green-100 px-3 py-1 rounded text-sm">
                    {statusLabel}
                </span>
            );
        case UserStatus.INACTIVE:
            return (
                <span className="text-red-600 bg-red-100 px-3 py-1 rounded text-sm">
                    {statusLabel}
                </span>
            );
        case UserStatus.WAITING:
            return (
                <span className="text-gray-600 bg-gray-100 px-3 py-1 rounded text-sm">
                    {statusLabel}
                </span>
            );
        case UserStatus.DELETED:
            return (
                <span className="text-orange-600 bg-orange-100 px-3 py-1 rounded text-sm">
                    {statusLabel}
                </span>
            );
        default:
            return (
                <span className="text-[#6366F1] bg-[#6366F1]/10 px-3 py-1 rounded text-sm">
                    {statusLabel}
                </span>
            );
    }
};


export const mapActiveStatusToEnum = (activeStatus: string): UserStatus => {
    if (activeStatus === UserStatus.ACTIVE) return UserStatus.ACTIVE;
    if (activeStatus === UserStatus.WAITING) return UserStatus.WAITING;
    if (activeStatus === UserStatus.INACTIVE) return UserStatus.INACTIVE;
    return UserStatus.DELETED;
};