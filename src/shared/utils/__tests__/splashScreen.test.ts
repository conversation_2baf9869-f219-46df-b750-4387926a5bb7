import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { showSplashScreen, hideSplashScreen } from '../splashScreen'

describe('splashScreen', () => {
  beforeEach(() => {
    // Create a mock body element
    document.body.innerHTML = ''
  })

  afterEach(() => {
    // Clean up after each test
    document.body.innerHTML = ''
    vi.clearAllMocks()
    vi.useRealTimers()
  })

  describe('showSplashScreen', () => {
    it('should create and return a new splash screen iframe', () => {
      const iframe = showSplashScreen()

      expect(iframe).toBeInstanceOf(HTMLIFrameElement)
      expect(iframe.id).toBe('splash-screen')
      expect(iframe.src).toContain('/splash-screen.html')
      // Check individual style properties
      expect(iframe.style.position).toBe('fixed')
      expect(iframe.style.top).toBe('0px')
      expect(iframe.style.left).toBe('0px')
      expect(iframe.style.width).toBe('100%')
      expect(iframe.style.height).toBe('100%')
      expect(iframe.style.border).toBe('')  // Empty string because it's not set directly
      expect(iframe.style.zIndex).toBe('9999')
      expect(document.body.contains(iframe)).toBe(true)
    })

    it('should return existing splash screen if one already exists', () => {
      const firstIframe = showSplashScreen()
      const secondIframe = showSplashScreen()

      expect(secondIframe).toBe(firstIframe)
      expect(document.querySelectorAll('#splash-screen')).toHaveLength(1)
    })
  })

  describe('hideSplashScreen', () => {
    it('should hide and remove the splash screen after timeout', () => {
      vi.useFakeTimers()

      const iframe = showSplashScreen()
      hideSplashScreen(iframe)

      expect(iframe.style.opacity).toBe('0')
      expect(document.body.contains(iframe)).toBe(true)

      vi.advanceTimersByTime(500)

      expect(document.body.contains(iframe)).toBe(false)
    })

    it('should handle null iframe gracefully', () => {
      expect(() => {
        hideSplashScreen(null)
      }).not.toThrow()
    })

    it('should handle iframe without parent node', () => {
      vi.useFakeTimers()

      const iframe = showSplashScreen()
      document.body.removeChild(iframe)
      hideSplashScreen(iframe)

      expect(iframe.style.opacity).toBe('0')
      vi.advanceTimersByTime(500)
      // No error should be thrown
    })
  })
})