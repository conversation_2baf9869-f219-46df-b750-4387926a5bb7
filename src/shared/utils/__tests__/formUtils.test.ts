import { describe, it, expect } from 'vitest'
import {
  createColumnSamples,
  createColumnMappings,
  createColumnEnumOptions,
  updateSchemaWithAvailableColumns,
  transformData,
  getSelectedColumnNames,
} from '../formUtils'

describe('formUtils', () => {
  describe('createColumnSamples', () => {
    it('should create column samples from dataset sample data', () => {
      const samplesData = {
        datasetSampleData: {
          data: [
            { column: 'lat', data: ['40.7128', '34.0522'] },
            { column: 'lng', data: ['-74.0060', '-118.2437'] },
          ],
        },
      }

      const result = createColumnSamples(samplesData)
      expect(result).toEqual({
        lat: ['40.7128', '34.0522'],
        lng: ['-74.0060', '-118.2437'],
      })
    })
  })

  describe('createColumnMappings', () => {
    it('should create column mappings with titles', () => {
      const samplesData = {
        datasetSampleData: {
          data: [
            { column: 'lat', title: 'Latitude', data: ['40.7128', '34.0522'] },
            { column: 'lng', title: 'Longitude', data: ['-74.0060', '-118.2437'] },
          ],
        },
      }

      const result = createColumnMappings(samplesData)
      expect(result).toEqual([
        { column: 'lat', title: 'Latitude', displayName: 'Latitude' },
        { column: 'lng', title: 'Longitude', displayName: 'Longitude' },
      ])
    })

    it('should use column name as title and displayName when title is not provided', () => {
      const samplesData = {
        datasetSampleData: {
          data: [
            { column: 'lat', data: ['40.7128', '34.0522'] },
            { column: 'lng', data: ['-74.0060', '-118.2437'] },
          ],
        },
      }

      const result = createColumnMappings(samplesData)
      expect(result).toEqual([
        { column: 'lat', title: 'lat', displayName: 'lat' },
        { column: 'lng', title: 'lng', displayName: 'lng' },
      ])
    })
  })

  describe('createColumnEnumOptions', () => {
    it('should create enum options from column mappings', () => {
      const columnMappings = [
        { column: 'lat', title: 'Latitude', displayName: 'Latitude' },
        { column: 'lng', title: 'Longitude', displayName: 'Longitude' },
      ]

      const result = createColumnEnumOptions(columnMappings)
      expect(result).toEqual([
        { value: 'lat', label: 'Latitude' },
        { value: 'lng', label: 'Longitude' },
      ])
    })
  })

  describe('updateSchemaWithAvailableColumns', () => {
    const baseSchema = {
      type: 'object',
      properties: {
        latColumn: {
          type: 'string',
          title: 'Latitude Column',
        },
        lngColumn: {
          type: 'string',
          title: 'Longitude Column',
        },
      },
      dependencies: {},
    }

    const columnMappings = [
      { column: 'lat', title: 'Latitude', displayName: 'Latitude' },
      { column: 'lng', title: 'Longitude', displayName: 'Longitude' },
      { column: 'latLng', title: 'LatLng', displayName: 'LatLng' },
    ]

    const columnSamples = {
      lat: ['40.7128', '34.0522'],
      lng: ['-74.0060', '-118.2437'],
      latLng: ['40.7128,-74.0060', '34.0522,-118.2437'],
    }

    it('should update schema for columns type with single column layout', () => {
      const currentFormData = {
        columnLayout: 'عمود',
        latLngColumn: 'latLng',
      }

      const result = updateSchemaWithAvailableColumns(
        baseSchema,
        currentFormData,
        columnMappings,
        columnSamples,
        'columns'
      )

      expect(result.dependencies.columnLayout.oneOf[0].properties.columnLayout.const).toBe('عمود')
      expect(result.dependencies.columnLayout.oneOf[0].properties.latLngColumn.samples).toBe(columnSamples)
      expect(result.dependencies.columnLayout.oneOf[0].required).toEqual(['latLngColumn'])
    })

    it('should update schema for columns type with two columns layout', () => {
      const currentFormData = {
        columnLayout: 'عمودين',
        latColumn: 'lat',
        lngColumn: 'lng',
      }

      const result = updateSchemaWithAvailableColumns(
        baseSchema,
        currentFormData,
        columnMappings,
        columnSamples,
        'columns'
      )

      expect(result.dependencies.columnLayout.oneOf[1].properties.columnLayout.const).toBe('عمودين')
      expect(result.dependencies.columnLayout.oneOf[1].properties.latColumn.samples).toBe(columnSamples)
      expect(result.dependencies.columnLayout.oneOf[1].properties.lngColumn.samples).toBe(columnSamples)
      expect(result.dependencies.columnLayout.oneOf[1].required).toEqual(['latColumn', 'lngColumn'])
    })

    it('should update schema for advanced type', () => {
      const currentFormData = {
        latColumn: 'lat',
        lngColumn: 'lng',
      }

      const result = updateSchemaWithAvailableColumns(
        baseSchema,
        currentFormData,
        columnMappings,
        columnSamples,
        'advanced'
      )

      expect(result.properties.latColumn.samples).toBe(columnSamples)
      expect(result.properties.lngColumn.samples).toBe(columnSamples)
      expect(result.properties.latColumn.enum).toEqual(['lat', 'latLng'])
      expect(result.properties.lngColumn.enum).toEqual(['lng', 'latLng'])
    })
  })

  describe('transformData', () => {
    it('should transform object keys to label-value pairs', () => {
      const data = {
        lat: 'Latitude',
        lng: 'Longitude',
      }

      const result = transformData(data)
      expect(result).toEqual([
        { label: 'lat', value: 'lat' },
        { label: 'lng', value: 'lng' },
      ])
    })
  })

  describe('getSelectedColumnNames', () => {
    it('should return array of selected column names', () => {
      const formData = {
        latColumn: 'lat',
        lngColumn: 'lng',
        emptyColumn: '',
        nullColumn: null,
      }

      const result = getSelectedColumnNames(formData)
      expect(result).toEqual(['lat', 'lng'])
    })
  })
})