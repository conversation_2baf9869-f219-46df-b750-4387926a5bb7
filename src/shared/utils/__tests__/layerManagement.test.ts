import { describe, it, expect, vi, beforeEach } from 'vitest'
import { processLayers, type LayersData, type WorkspaceData } from '../layerManagement'
import {
  addBackendLayer,
  resetBackendLayers,
  setSelectedMainLayer,
  setSelectedTableLayer,
} from '@/shared/store/slices/mapSlice'

// Mock Redux store actions
vi.mock('@/shared/store/slices/mapSlice', () => ({
  addBackendLayer: vi.fn(),
  resetBackendLayers: vi.fn(),
  setSelectedMainLayer: vi.fn(),
  setSelectedTableLayer: vi.fn(),
}))

describe('layerManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('processLayers', () => {
    const mockDispatch = vi.fn()

    it('should handle empty or invalid input', () => {
      processLayers({} as LayersData, {} as WorkspaceData, false, mockDispatch)
      expect(mockDispatch).not.toHaveBeenCalled()

      processLayers({} as LayersData, {} as WorkspaceData, false, mockDispatch)
      expect(mockDispatch).not.toHaveBeenCalled()

      processLayers({ layers: {} } as LayersData, {} as WorkspaceData, false, mockDispatch)
      expect(mockDispatch).not.toHaveBeenCalled()
    })

    it('should process layers without sorting when no sortedIds', () => {
      const layersData = {
        layers: {
          data: [
            {
              id: 1,
              title: 'Layer 1',
              description: 'Layer 1 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 1, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
            {
              id: 2,
              title: 'Layer 2',
              description: 'Layer 2 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 2, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
          ],
        },
      }
      const workspaceData = {}

      processLayers(layersData, workspaceData, false, mockDispatch)

      expect(resetBackendLayers).toHaveBeenCalledTimes(1)
      expect(addBackendLayer).toHaveBeenCalledTimes(2)
      expect(addBackendLayer).toHaveBeenNthCalledWith(1, {
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
      })
      expect(addBackendLayer).toHaveBeenNthCalledWith(2, {
        id: 1,
        title: 'Layer 1',
        description: 'Layer 1 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 1, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 0,
      })
      expect(setSelectedMainLayer).toHaveBeenCalledWith({
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
        fromLayerClick: true,
      })
      expect(setSelectedTableLayer).toHaveBeenCalledWith({
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
      })
    })

    it('should process layers with sorting when sortedIds provided', () => {
      const layersData = {
        layers: {
          data: [
            {
              id: 1,
              title: 'Layer 1',
              description: 'Layer 1 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 1, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
            {
              id: 2,
              title: 'Layer 2',
              description: 'Layer 2 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 2, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
          ],
        },
      }
      const workspaceData = {
        workspaces: {
          data: [
            {
              layersSortedIds: ['2', '1'], // String IDs in sortedIds
            },
          ],
        },
      }

      processLayers(layersData, workspaceData, false, mockDispatch)

      expect(resetBackendLayers).toHaveBeenCalledTimes(1)
      expect(addBackendLayer).toHaveBeenCalledTimes(2)
      expect(addBackendLayer).toHaveBeenNthCalledWith(1, {
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
      })
      expect(addBackendLayer).toHaveBeenNthCalledWith(2, {
        id: 1,
        title: 'Layer 1',
        description: 'Layer 1 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 1, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 0,
      })
      expect(setSelectedMainLayer).toHaveBeenCalledWith({
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
        fromLayerClick: true,
      })
      expect(setSelectedTableLayer).toHaveBeenCalledWith({
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 1,
      })
    })

    it('should include all layers when isFromCreation is true', () => {
      const layersData = {
        layers: {
          data: [
            {
              id: 1,
              title: 'Layer 1',
              description: 'Layer 1 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 1, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
            {
              id: 2,
              title: 'Layer 2',
              description: 'Layer 2 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 2, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'DRAFT',
            },
          ],
        },
      }
      const workspaceData = {}

      processLayers(layersData, workspaceData, true, mockDispatch)

      expect(resetBackendLayers).toHaveBeenCalledTimes(1)
      expect(addBackendLayer).toHaveBeenCalledTimes(2)
      expect(addBackendLayer).toHaveBeenNthCalledWith(1, {
        id: 2,
        title: 'Layer 2',
        description: 'Layer 2 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 2, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'DRAFT',
        isVisible: true,
        zIndex: 1,
      })
      expect(addBackendLayer).toHaveBeenNthCalledWith(2, {
        id: 1,
        title: 'Layer 1',
        description: 'Layer 1 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 1, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 0,
      })
    })

    it('should filter out non-published layers when isFromCreation is false', () => {
      const layersData = {
        layers: {
          data: [
            {
              id: 1,
              title: 'Layer 1',
              description: 'Layer 1 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 1, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'PUBLISHED',
            },
            {
              id: 2,
              title: 'Layer 2',
              description: 'Layer 2 description',
              boundaries: { type: 'Polygon', coordinates: [] },
              color: '#000000',
              jsonSchema: {},
              webUiJsonSchema: {},
              locationFieldMapping: {},
              readOnly: false,
              dataset: { id: 2, workspace: { workspaceType: 'default' } },
              summaryFields: [],
              slds: [],
              status: 'DRAFT',
            },
          ],
        },
      }
      const workspaceData = {}

      processLayers(layersData, workspaceData, false, mockDispatch)

      expect(resetBackendLayers).toHaveBeenCalledTimes(1)
      expect(addBackendLayer).toHaveBeenCalledTimes(1)
      expect(addBackendLayer).toHaveBeenCalledWith({
        id: 1,
        title: 'Layer 1',
        description: 'Layer 1 description',
        boundaries: { type: 'Polygon', coordinates: [] },
        color: '#000000',
        jsonSchema: {},
        webUiJsonSchema: {},
        locationFieldMapping: {},
        readOnly: false,
        dataset: { id: 1, workspace: { workspaceType: 'default' } },
        summaryFields: [],
        slds: [],
        status: 'PUBLISHED',
        isVisible: true,
        zIndex: 0,
      })
    })
  })
})