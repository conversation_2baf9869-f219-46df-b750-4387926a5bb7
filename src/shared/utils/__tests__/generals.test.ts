import { describe, it, expect, vi, beforeEach } from 'vitest'
import jsCookies from 'js-cookie'
import { StepProgressManager } from '../stepProgress'
import { WorkspaceMethods } from '../routes'
import { getFullToken, processFile, processFiles, determineCurrentStep, filterEmptyProperties } from '../generals'

interface StepProgress {
  status: 'incomplete' | 'complete'
  formData: Record<string, unknown> | null
}

// Mock modules
vi.mock('js-cookie', () => ({
  default: {
    get: vi.fn(),
  },
}))

vi.mock('../stepProgress', () => ({
  StepProgressManager: {
    getProgress: vi.fn(),
  },
}))

vi.mock('../generals', () => {
  return {
    getFullToken: vi.fn(() => {
      const token = jsCookies.get('token')
      return token ? `Bearer ${token}` : null
    }),
    processFile: vi.fn((file: File) => Promise.resolve({
      name: file.name,
      size: file.size,
      file,
      type: file.type,
    })),
    processFiles: vi.fn((files: FileList) => Promise.all(Array.from(files).map((file: File) => ({
      name: file.name,
      size: file.size,
      file,
      type: file.type,
    })))),
    determineCurrentStep: vi.fn((method: string) => {
      if (method === 'UPLOAD_FILE') {
        for (let i = 1; i <= 4; i++) {
          if (i === 2) continue // Skip step 2 for UPLOAD_FILE method
          const progress = StepProgressManager.getProgress(i)
          if (progress.status === 'incomplete') {
            return i
          }
        }
        return 4
      } else {
        for (let i = 1; i <= 1; i++) {
          const progress = StepProgressManager.getProgress(i)
          if (progress.status === 'incomplete') {
            return i
          }
        }
        return 1
      }
    }),
    filterEmptyProperties: vi.fn((obj: Record<string, unknown>) => {
      const result: Record<string, unknown> = {}
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined && value !== null && value !== '') {
          result[key] = value
        }
      }
      return result
    }),
  }
})

describe('generals', () => {
  describe('getFullToken', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should return null when no token exists', () => {
      const mockGet = vi.fn()
      vi.spyOn(jsCookies, 'get').mockImplementation(mockGet)
      mockGet.mockReturnValue(undefined)
      expect(getFullToken()).toBeNull()
    })

    it('should return bearer token when token exists', () => {
      const mockGet = vi.fn()
      vi.spyOn(jsCookies, 'get').mockImplementation(mockGet)
      mockGet.mockReturnValue('test-token')
      expect(getFullToken()).toBe('Bearer test-token')
    })
  })

  describe('processFile and processFiles', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should process a single file', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      const result = await processFile(mockFile)
      expect(result).toEqual({
        name: 'test.txt',
        size: mockFile.size,
        file: mockFile,
        type: 'text/plain',
      })
    })

    it('should process multiple files', async () => {
      const mockFiles = [
        new File(['content1'], 'test1.txt', { type: 'text/plain' }),
        new File(['content2'], 'test2.txt', { type: 'text/plain' }),
      ]
      const fileList = {
        0: mockFiles[0],
        1: mockFiles[1],
        length: 2,
        item: (index: number) => mockFiles[index] || null,
        [Symbol.iterator]: function* () {
          yield mockFiles[0]
          yield mockFiles[1]
        },
      } as unknown as FileList

      const results = await processFiles(fileList) as Array<{
        name: string
        size: number
        file: File
        type: string
      }>
      expect(results).toHaveLength(2)
      expect(results[0]).toEqual({
        name: 'test1.txt',
        size: mockFiles[0].size,
        file: mockFiles[0],
        type: 'text/plain',
      })
      expect(results[1]).toEqual({
        name: 'test2.txt',
        size: mockFiles[1].size,
        file: mockFiles[1],
        type: 'text/plain',
      })
    })

    })

  describe('determineCurrentStep', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should return first incomplete step for UPLOAD_FILE method', () => {
      const mockGetProgress = vi.fn((step: number) => ({
        status: step === 2 ? 'complete' : step === 3 ? 'incomplete' : 'complete',
        formData: {},
      } as StepProgress))

      vi.spyOn(StepProgressManager, 'getProgress').mockImplementation(mockGetProgress)

      expect(determineCurrentStep(WorkspaceMethods.UPLOAD_FILE)).toBe(3)
    })

    it('should skip step 2 for UPLOAD_FILE method', () => {
      const mockGetProgress = vi.fn((step: number) => ({
        status: step === 2 ? 'incomplete' : 'complete',
        formData: {},
      } as StepProgress))

      vi.spyOn(StepProgressManager, 'getProgress').mockImplementation(mockGetProgress)

      expect(determineCurrentStep(WorkspaceMethods.UPLOAD_FILE)).toBe(4)
    })

    it('should return first incomplete step for other methods', () => {
      const mockGetProgress = vi.fn((step: number) => ({
        status: step === 1 ? 'incomplete' : 'complete',
        formData: {},
      } as StepProgress))

      vi.spyOn(StepProgressManager, 'getProgress').mockImplementation(mockGetProgress)

      expect(determineCurrentStep('OTHER_METHOD')).toBe(1)
    })

    it('should return maxSteps when all steps are complete', () => {
      const mockGetProgress = vi.fn(() => ({
        status: 'complete',
        formData: {},
      } as StepProgress))

      vi.spyOn(StepProgressManager, 'getProgress').mockImplementation(mockGetProgress)

      expect(determineCurrentStep(WorkspaceMethods.UPLOAD_FILE)).toBe(4)
      expect(determineCurrentStep('OTHER_METHOD')).toBe(1)
    })
  })

  describe('filterEmptyProperties', () => {
    it('should filter out undefined, null, and empty string values', () => {
      const input = {
        a: 1,
        b: undefined,
        c: null,
        d: '',
        e: 'value',
        f: 0,
        g: false,
      }

      const expected = {
        a: 1,
        e: 'value',
        f: 0,
        g: false,
      }

      const result = filterEmptyProperties(input)
      expect(result).toEqual(expected)
    })

    it('should handle empty object', () => {
      const result = filterEmptyProperties({})
      expect(result).toEqual({})
    })

    it('should preserve nested objects and arrays', () => {
      const input = {
        a: { nested: 'value' },
        b: undefined,
        c: [1, 2, 3],
        d: '',
      }

      const expected = {
        a: { nested: 'value' },
        c: [1, 2, 3],
      }

      const result = filterEmptyProperties(input)
      expect(result).toEqual(expected)
    })
  })
})