import { describe, it, expect } from 'vitest';
import { formatNumber, roundNumber } from '../formatters';

describe('formatters', () => {
  describe('formatNumber', () => {
    it('should format number with default decimal places (0)', () => {
      expect(formatNumber(123.456)).toBe('123');
      expect(formatNumber(123.789)).toBe('124');
    });

    it('should format number with specified decimal places', () => {
      expect(formatNumber(123.456, 2)).toBe('123.46');
      expect(formatNumber(123.454, 2)).toBe('123.45');
      expect(formatNumber(123.456, 1)).toBe('123.5');
    });

    it('should handle negative numbers', () => {
      expect(formatNumber(-123.456, 2)).toBe('-123.46');
      expect(formatNumber(-123.454, 2)).toBe('-123.45');
    });

    it('should handle zero', () => {
      expect(formatNumber(0)).toBe('0');
      expect(formatNumber(0, 2)).toBe('0.00');
    });
  });

  describe('roundNumber', () => {
    it('should round number with default decimal places (0)', () => {
      expect(roundNumber(123.456)).toBe(123);
      expect(roundNumber(123.789)).toBe(124);
    });

    it('should round number with specified decimal places', () => {
      expect(roundNumber(123.456, 2)).toBe(123.46);
      expect(roundNumber(123.454, 2)).toBe(123.45);
      expect(roundNumber(123.456, 1)).toBe(123.5);
    });

    it('should handle negative numbers', () => {
      expect(roundNumber(-123.456, 2)).toBe(-123.46);
      expect(roundNumber(-123.454, 2)).toBe(-123.45);
    });

    it('should handle zero', () => {
      expect(roundNumber(0)).toBe(0);
      expect(roundNumber(0, 2)).toBe(0);
    });

    it('should handle large numbers', () => {
      expect(roundNumber(1234567.89, 2)).toBe(1234567.89);
      expect(roundNumber(9999999.99999, 2)).toBe(10000000);
    });
  });
});