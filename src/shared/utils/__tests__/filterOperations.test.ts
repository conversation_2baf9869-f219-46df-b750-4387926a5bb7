import { describe, it, expect, vi } from 'vitest'
import { getOperationsForFieldType, OPERATOR_MAP } from '../filterOperations'

// Mock i18next
vi.mock('i18next', () => ({
  default: {
    t: (key: string) => key,
  },
}))

describe('filterOperations', () => {
  describe('getOperationsForFieldType', () => {
    it('should return string operations for string type', () => {
      const operations = getOperationsForFieldType('string')
      expect(operations).toHaveLength(6)
      expect(operations).toContainEqual({
        value: 'iexact',
        label: 'layerSettings.filters.filterOperators.equals',
      })
      expect(operations).toContainEqual({
        value: 'iexact',
        label: 'layerSettings.filters.filterOperators.notEquals',
        isNot: true,
      })
      expect(operations).toContainEqual({
        value: 'icontains',
        label: 'layerSettings.filters.filterOperators.contains',
      })
    })

    it('should return number operations for number type', () => {
      const operations = getOperationsForFieldType('number')
      expect(operations).toHaveLength(7)
      expect(operations).toContainEqual({
        value: 'iexact',
        label: 'layerSettings.filters.filterOperators.equals',
      })
      expect(operations).toContainEqual({
        value: 'gt',
        label: 'layerSettings.filters.filterOperators.greaterThan',
      })
      expect(operations).toContainEqual({
        value: 'range',
        label: 'layerSettings.filters.filterOperators.range',
      })
    })

    it('should return integer operations for integer type', () => {
      const operations = getOperationsForFieldType('integer')
      expect(operations).toHaveLength(7)
      expect(operations).toContainEqual({
        value: 'iexact',
        label: 'layerSettings.filters.filterOperators.equals',
      })
      expect(operations).toContainEqual({
        value: 'gt',
        label: 'layerSettings.filters.filterOperators.greaterThan',
      })
      expect(operations).toContainEqual({
        value: 'range',
        label: 'layerSettings.filters.filterOperators.range',
      })
    })

    it('should return date operations for date type', () => {
      const operations = getOperationsForFieldType('date')
      expect(operations).toHaveLength(6)
      expect(operations).toContainEqual({
        value: 'exact',
        label: 'layerSettings.filters.filterOperators.equals',
      })
      expect(operations).toContainEqual({
        value: 'date__gt',
        label: 'layerSettings.filters.filterOperators.after',
      })
      expect(operations).toContainEqual({
        value: 'range',
        label: 'layerSettings.filters.filterOperators.range',
      })
    })

    it('should return boolean operations for boolean type', () => {
      const operations = getOperationsForFieldType('boolean')
      expect(operations).toHaveLength(1)
      expect(operations).toContainEqual({
        value: 'iexact',
        label: 'layerSettings.filters.filterOperators.equals',
      })
    })

    it('should return string operations for unknown type', () => {
      const operations = getOperationsForFieldType('unknown')
      expect(operations).toEqual(getOperationsForFieldType('string'))
    })
  })

  describe('OPERATOR_MAP', () => {
    it('should have correct operator mappings', () => {
      expect(OPERATOR_MAP.exact).toBe('=')
      expect(OPERATOR_MAP.iexact).toBe('ILIKE')
      expect(OPERATOR_MAP.contains).toBe('LIKE')
      expect(OPERATOR_MAP.icontains).toBe('ILIKE')
      expect(OPERATOR_MAP.startswith).toBe('LIKE')
      expect(OPERATOR_MAP.endswith).toBe('LIKE')
      expect(OPERATOR_MAP.gt).toBe('>')
      expect(OPERATOR_MAP.lt).toBe('<')
      expect(OPERATOR_MAP.gte).toBe('>=')
      expect(OPERATOR_MAP.lte).toBe('<=')
      expect(OPERATOR_MAP.range).toBe('BETWEEN')
      expect(OPERATOR_MAP.date__gt).toBe('>')
      expect(OPERATOR_MAP.date__lt).toBe('<')
      expect(OPERATOR_MAP.date__gte).toBe('>=')
      expect(OPERATOR_MAP.date__lte).toBe('<=')
      expect(OPERATOR_MAP.isnull).toBe('IS NULL')
      expect(OPERATOR_MAP.isempty).toBe('IS EMPTY')
    })
  })
})