import { describe, it, expect } from 'vitest'
import { Routes, WorkspaceMethods } from '../routes'

describe('routes', () => {
  describe('Routes', () => {
    it('should have correct route values', () => {
      expect(Routes.landing).toBe('')
      expect(Routes.login).toBe('login')
      expect(Routes.dashboard).toBe('dashboard')
      expect(Routes.market).toBe('dashboard/market')
      expect(Routes.marketSpaces).toBe('dashboard/workspaces')
      expect(Routes.createWorkspace).toBe('dashboard/workspaces/create-workspace')
      expect(Routes.map).toBe('dashboard/workspaces/map')
      expect(Routes.mapWorkspace).toBe('dashboard/workspaces/map/:workspaceId')
      expect(Routes.organizationSettings).toBe('dashboard/organization/settings')
    })

    it('should be type-safe', () => {
      // This test is for type checking only
      // @ts-expect-error - Routes should be read-only
      Routes.landing = 'new-value'
      expect(true).toBe(true)
    })

    it('should have all required routes', () => {
      const expectedRoutes = [
        'landing',
        'login',
        'dashboard',
        'market',
        'marketSpaces',
        'createWorkspace',
        'map',
        'mapWorkspace',
        'organizationSettings',
      ]

      expectedRoutes.forEach((route) => {
        expect(Routes).toHaveProperty(route)
      })
    })
  })

  describe('WorkspaceMethods', () => {
    it('should have correct enum values', () => {
      expect(WorkspaceMethods.UPLOAD_FILE).toBe('UPLOAD_FILE')
      expect(WorkspaceMethods.DESIGN_LAYER).toBe('DESIGN_LAYER')
      expect(WorkspaceMethods.CONNECT_DB).toBe('CONNECT_DB')
    })

    it('should have all required methods', () => {
      const expectedMethods = [
        'UPLOAD_FILE',
        'DESIGN_LAYER',
        'CONNECT_DB',
      ]

      expectedMethods.forEach((method) => {
        expect(WorkspaceMethods).toHaveProperty(method)
      })
    })

    it('should have correct number of methods', () => {
      const methodCount = Object.keys(WorkspaceMethods).length
      expect(methodCount).toBe(3)
    })

    it('should be type-safe', () => {
      // This test is for type checking only
      // @ts-expect-error - WorkspaceMethods should be read-only
      WorkspaceMethods.UPLOAD_FILE = 'new-value'
      expect(true).toBe(true)
    })
  })
})