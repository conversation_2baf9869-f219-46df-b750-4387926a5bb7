import { describe, it, expect, vi, beforeEach } from 'vitest'
import { StepProgressManager } from '../stepProgress'
import { WorkspaceMethods } from '../routes'

describe('StepProgressManager', () => {
  const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
    })
  })

  describe('saveProgress', () => {
    it('should save new progress when no previous progress exists', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const formData = { field: 'value' }
      StepProgressManager.saveProgress(1, formData)

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('stepsProgress_UPLOAD_FILE')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'stepsProgress_UPLOAD_FILE',
        JSON.stringify({
          step1: {
            status: 'complete',
            formData,
          },
        })
      )
    })

    it('should merge with existing progress when saving', () => {
      const existingProgress = {
        step1: {
          status: 'complete',
          formData: { field1: 'value1' },
        },
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingProgress))

      const newFormData = { field2: 'value2' }
      StepProgressManager.saveProgress(2, newFormData)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'stepsProgress_UPLOAD_FILE',
        JSON.stringify({
          ...existingProgress,
          step2: {
            status: 'complete',
            formData: newFormData,
          },
        })
      )
    })

    it('should save progress with custom method', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const formData = { field: 'value' }
      StepProgressManager.saveProgress(1, formData, 'CUSTOM_METHOD')

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('stepsProgress_CUSTOM_METHOD')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'stepsProgress_CUSTOM_METHOD',
        JSON.stringify({
          step1: {
            status: 'complete',
            formData,
          },
        })
      )
    })

    it('should overwrite existing step progress', () => {
      const existingProgress = {
        step1: {
          status: 'complete',
          formData: { oldField: 'oldValue' },
        },
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingProgress))

      const newFormData = { newField: 'newValue' }
      StepProgressManager.saveProgress(1, newFormData)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'stepsProgress_UPLOAD_FILE',
        JSON.stringify({
          step1: {
            status: 'complete',
            formData: newFormData,
          },
        })
      )
    })
  })

  describe('getProgress', () => {
    it('should return incomplete status when no progress exists', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const result = StepProgressManager.getProgress(1)
      expect(result).toEqual({
        status: 'incomplete',
        formData: null,
      })
    })

    it('should return existing progress for a step', () => {
      const existingProgress = {
        step1: {
          status: 'complete',
          formData: { field: 'value' },
        },
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingProgress))

      const result = StepProgressManager.getProgress(1)
      expect(result).toEqual(existingProgress.step1)
    })

    it('should return incomplete status for non-existent step', () => {
      const existingProgress = {
        step1: {
          status: 'complete',
          formData: { field: 'value' },
        },
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingProgress))

      const result = StepProgressManager.getProgress(2)
      expect(result).toEqual({
        status: 'incomplete',
        formData: null,
      })
    })

    it('should get progress with custom method', () => {
      const existingProgress = {
        step1: {
          status: 'complete',
          formData: { field: 'value' },
        },
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingProgress))

      StepProgressManager.getProgress(1, 'CUSTOM_METHOD')
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('stepsProgress_CUSTOM_METHOD')
    })

    it('should handle invalid JSON in localStorage', () => {
      // Mock console.error to prevent error output in test
      const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockLocalStorage.getItem.mockReturnValue('invalid-json')

      const result = StepProgressManager.getProgress(1)
      expect(result).toEqual({
        status: 'incomplete',
        formData: null,
      })

      // Verify that the error was logged
      expect(mockConsoleError).toHaveBeenCalled()
      mockConsoleError.mockRestore()
    })
  })

  describe('clearProgress', () => {
    it('should clear progress for default method', () => {
      StepProgressManager.clearProgress()
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('stepsProgress_UPLOAD_FILE')
    })

    it('should clear progress for custom method', () => {
      StepProgressManager.clearProgress('CUSTOM_METHOD')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('stepsProgress_CUSTOM_METHOD')
    })
  })

  describe('default method behavior', () => {
    it('should use UPLOAD_FILE as default method for saveProgress', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      StepProgressManager.saveProgress(1, { field: 'value' })
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith(`stepsProgress_${WorkspaceMethods.UPLOAD_FILE}`)
    })

    it('should use UPLOAD_FILE as default method for getProgress', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      StepProgressManager.getProgress(1)
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith(`stepsProgress_${WorkspaceMethods.UPLOAD_FILE}`)
    })

    it('should use UPLOAD_FILE as default method for clearProgress', () => {
      StepProgressManager.clearProgress()
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(`stepsProgress_${WorkspaceMethods.UPLOAD_FILE}`)
    })
  })
})