import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  cleanStringData,
  formatFieldsForModal,
  formatInitialData,
  handleGraphQLError,
  processFieldValue,
  formatMapData,
  getPosition,
  type FieldType,
  type JsonSchema,
} from '../recordHelpers';

describe('recordHelpers', () => {
  // Silence console messages during tests
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })
  describe('cleanStringData', () => {
    it('should clean and parse string data correctly', () => {
      const testCases = [
        {
          input: "{'name': 'John', 'age': None, 'active': True}",
          expected: '{"name": "John", "age": null, "active": true}',
        },
        {
          input: '{"value": "123.45", "status": "undefined", "score": "NaN"}',
          expected: '{"value": 123.45, "status": null, "score": null}',
        },
        {
          input: "{'numbers': ['1', '2', '3'], 'flag': False}",
          expected: '{"numbers": [1, 2, 3], "flag": false}',
        },
      ];

      testCases.forEach(({ input, expected }) => {
        const result = cleanStringData(input);
        // Parse both strings to compare their actual content
        expect(JSON.parse(result)).toEqual(JSON.parse(expected));
      });
    });
  });

  describe('formatFieldsForModal', () => {
    it('should return empty array for invalid schema', () => {
      expect(formatFieldsForModal(null)).toEqual([]);
      const emptySchema = { properties: {} } satisfies JsonSchema;
      expect(formatFieldsForModal(emptySchema)).toEqual([]);
    });

    it('should format basic schema fields correctly', () => {
      const schema: JsonSchema = {
        properties: {
          name: { type: 'string', title: 'Full Name' },
          age: { type: 'integer' },
          active: { type: 'boolean' },
        },
        required: ['name'],
      };

      const result = formatFieldsForModal(schema);
      expect(result).toEqual([
        {
          key: 'name',
          label: 'Full Name',
          type: 'text',
          required: true,
          enumValues: [],
          isArray: false,
        },
        {
          key: 'age',
          label: 'age',
          type: 'integer',
          required: false,
          enumValues: [],
          isArray: false,
        },
        {
          key: 'active',
          label: 'active',
          type: 'boolean',
          required: false,
          enumValues: [],
          isArray: false,
        },
      ]);
    });

    it('should handle array types correctly', () => {
      const schema: JsonSchema = {
        properties: {
          tags: {
            type: 'array',
            items: { type: 'string' },
            title: 'Tags',
          },
          scores: {
            type: 'array',
            items: { type: 'integer' },
            title: 'Scores',
          },
        },
      };

      const result = formatFieldsForModal(schema);
      expect(result).toEqual([
        {
          key: 'tags',
          label: 'Tags',
          type: 'text',
          required: false,
          enumValues: [],
          isArray: true,
        },
        {
          key: 'scores',
          label: 'Scores',
          type: 'integer',
          required: false,
          enumValues: [],
          isArray: true,
        },
      ]);
    });

    it('should handle enum types correctly', () => {
      const schema: JsonSchema = {
        properties: {
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'pending'],
            title: 'Status',
          },
          role: {
            type: 'string',
            $ref: '#/definitions/Role',
          },
        },
        definitions: {
          Role: {
            enum: ['admin', 'user', 'guest'],
          },
        },
      };

      const result = formatFieldsForModal(schema);
      expect(result).toEqual([
        {
          key: 'status',
          label: 'Status',
          type: 'enum',
          required: false,
          enumValues: ['active', 'inactive', 'pending'],
          isArray: false,
        },
        {
          key: 'role',
          label: 'role',
          type: 'enum',
          required: false,
          enumValues: ['admin', 'user', 'guest'],
          isArray: false,
        },
      ]);
    });
  });

  describe('formatInitialData', () => {
    it('should handle empty or invalid input', () => {
      expect(formatInitialData(null)).toEqual({});
      expect(formatInitialData({})).toEqual({});
      expect(formatInitialData({ records: { data: [] } })).toEqual({});
    });

    it('should parse string data correctly', () => {
      const input = {
        records: {
          data: ["{'name': 'John', 'age': 30}"],
        },
      };
      expect(formatInitialData(input)).toEqual({ name: 'John', age: 30 });
    });

    it('should handle nested data property', () => {
      const input = {
        records: {
          data: [
            {
              data: "{'name': 'John', 'age': 30}",
            },
          ],
        },
      };
      expect(formatInitialData(input)).toEqual({ name: 'John', age: 30 });
    });

    it('should handle object data', () => {
      const input = {
        records: {
          data: [
            {
              data: { name: 'John', age: 30 },
            },
          ],
        },
      };
      expect(formatInitialData(input)).toEqual({ name: 'John', age: 30 });
    });
  });

  describe('handleGraphQLError', () => {
    it('should handle form data errors', () => {
      const error = {
        graphQLErrors: [
          {
            extensions: {
              http: {
                reason: {
                  formData: ['Field name is required', 'Invalid email format'],
                },
              },
            },
          },
        ],
      };
      expect(handleGraphQLError(error)).toBe('Field name is required\nInvalid email format');
    });

    it('should handle layerId error', () => {
      const error = {
        graphQLErrors: [
          {
            extensions: {
              http: {
                reason: {
                  layerId: 'Layer not found',
                },
              },
            },
          },
        ],
      };
      expect(handleGraphQLError(error)).toBe('Layer not found');
    });

    it('should handle validation error', () => {
      const error = {
        graphQLErrors: [
          {
            extensions: {
              http: {
                reason: 'Invalid schema format',
              },
            },
          },
        ],
      };
      const result = handleGraphQLError(error);
      expect(result).toBe('Invalid schema format');
    });

    it('should handle generic error message', () => {
      const error = {
        graphQLErrors: [
          {
            message: 'Network error',
          },
        ],
      };
      expect(handleGraphQLError(error)).toBe('Network error');
    });

    it('should return default error message when no specific error is found', () => {
      const error = {};
      expect(handleGraphQLError(error)).toBe('حدث خطأ أثناء تحديث السجل');
    });
  });

  describe('processFieldValue', () => {
    const testCases: Array<{
      value: string;
      type: FieldType;
      expected: string | number | boolean | null;
    }> = [
      { value: '', type: 'text', expected: null },
      { value: '123', type: 'integer', expected: 123 },
      { value: '123.45', type: 'float', expected: 123.45 },
      { value: 'true', type: 'boolean', expected: true },
      { value: 'false', type: 'boolean', expected: false },
      { value: '2024-03-20', type: 'date', expected: '2024-03-20' },
      { value: 'hello', type: 'text', expected: 'hello' },
      { value: '', type: 'integer', expected: null },
      { value: '', type: 'float', expected: null },
      { value: 'yes', type: 'boolean', expected: true },
      { value: 'no', type: 'boolean', expected: false },
    ];

    testCases.forEach(({ value, type, expected }) => {
      it(`should process ${type} field with value "${value}" correctly`, () => {
        expect(processFieldValue(value, type)).toBe(expected);
      });
    });
  });

  describe('formatMapData', () => {
    it('should handle basic JSON data', () => {
      const input = JSON.stringify({
        name: 'John',
        age: 30,
        city: 'New York',
      });
      expect(formatMapData(input)).toEqual([
        { key: 'name', value: 'John' },
        { key: 'age', value: '30' },
        { key: 'city', value: 'New York' },
      ]);
    });

    it('should handle nested data structure', () => {
      const input = JSON.stringify({
        parsedData: {
          data: {
            name: 'John',
            age: 30,
            city: 'New York',
          },
        },
      });
      expect(formatMapData(input)).toEqual([
        { key: 'name', value: 'John' },
        { key: 'age', value: '30' },
        { key: 'city', value: 'New York' },
      ]);
    });

    it('should handle stringified nested data', () => {
      const input = JSON.stringify({
        data: "{'name': 'John', 'age': 30, 'city': 'New York'}",
      });
      expect(formatMapData(input)).toEqual([
        { key: 'name', value: 'John' },
        { key: 'age', value: '30' },
        { key: 'city', value: 'New York' },
      ]);
    });

    it('should handle invalid data gracefully', () => {
      expect(formatMapData('invalid json')).toEqual([]);
    });

    it('should limit to 4 entries', () => {
      const input = JSON.stringify({
        a: 1,
        b: 2,
        c: 3,
        d: 4,
        e: 5,
      });
      const result = formatMapData(input);
      expect(result).toHaveLength(4);
      expect(result).toEqual([
        { key: 'a', value: '1' },
        { key: 'b', value: '2' },
        { key: 'c', value: '3' },
        { key: 'd', value: '4' },
      ]);
    });
  });

  describe('getPosition', () => {
    beforeEach(() => {
      vi.stubGlobal('window', {
        innerWidth: 1024,
        innerHeight: 768,
      });
    });

    it('should position element normally when enough space', () => {
      const position = getPosition(100, 100);
      expect(position).toEqual({
        left: '102px',
        top: '165px',
      });
    });

    it('should adjust position when close to right edge', () => {
      const position = getPosition(900, 100);
      expect(position).toEqual({
        left: '580px',
        top: '165px',
      });
    });

    it('should adjust position when close to bottom edge', () => {
      const position = getPosition(100, 600);
      expect(position).toEqual({
        left: '102px',
        top: '280px',
      });
    });

    it('should adjust both positions when close to bottom-right corner', () => {
      const position = getPosition(900, 600);
      expect(position).toEqual({
        left: '580px',
        top: '280px',
      });
    });
  });
});