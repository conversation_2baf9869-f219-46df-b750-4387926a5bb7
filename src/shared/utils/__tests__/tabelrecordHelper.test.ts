import { describe, it, expect, vi, beforeEach } from 'vitest'
import { isImageUrl, isImageField, cleanAndParseJson, isImageUri } from '../tabelrecordHelper'
import { cleanStringData } from '../recordHelpers'

// Mock recordHelpers
vi.mock('../recordHelpers', () => ({
  cleanStringData: vi.fn((value: string) => value),
}))

describe('tabelrecordHelper', () => {
  describe('isImageUrl', () => {
    it('should return true for valid image URLs', () => {
      const validUrls = [
        'https://example.com/image.jpg',
        'http://example.com/path/to/image.jpeg',
        'https://example.com/image.png',
        'https://example.com/image.gif',
        'https://example.com/image.webp',
        'https://example.com/image.svg',
      ]

      validUrls.forEach((url) => {
        expect(isImageUrl(url)).toBe(true)
      })
    })

    it('should return false for non-image URLs', () => {
      const invalidUrls = [
        'https://example.com/document.pdf',
        'https://example.com/file.txt',
        'https://example.com/path/without/extension',
        'not-a-url',
        '',
      ]

      invalidUrls.forEach((url) => {
        expect(isImageUrl(url)).toBe(false)
      })
    })

    it('should handle URLs with query parameters', () => {
      const url = 'https://example.com/image.jpg?size=large&format=original'
      expect(isImageUrl(url)).toBe(true)
    })

    it('should handle URLs with fragments', () => {
      const url = 'https://example.com/image.png#preview'
      expect(isImageUrl(url)).toBe(true)
    })
  })

  describe('isImageField', () => {
    it('should return true for field names containing default image keywords', () => {
      const imageFields = [
        'userImage',
        'profile_img',
        'avatar_image',
        'IMAGE_URL',
        'thumbnail_img',
      ]

      imageFields.forEach((field) => {
        expect(isImageField(field)).toBe(true)
      })
    })

    it('should return false for non-image field names', () => {
      const nonImageFields = [
        'username',
        'description',
        'title',
        'content',
        'data',
      ]

      nonImageFields.forEach((field) => {
        expect(isImageField(field)).toBe(false)
      })
    })

    it('should handle custom keywords', () => {
      const customKeywords = ['photo', 'avatar']
      expect(isImageField('userPhoto', customKeywords)).toBe(true)
      expect(isImageField('userAvatar', customKeywords)).toBe(true)
      expect(isImageField('userImage', customKeywords)).toBe(false)
    })

    it('should be case-insensitive', () => {
      expect(isImageField('USER_IMAGE')).toBe(true)
      expect(isImageField('userIMG')).toBe(true)
      expect(isImageField('ImageField')).toBe(true)
    })
  })

  describe('cleanAndParseJson', () => {
    beforeEach(() => {
      vi.mocked(cleanStringData).mockImplementation((value) => value)
    })

    it('should parse valid JSON strings', () => {
      const validJson = '{"name": "John", "age": 30}'
      const expected = { name: 'John', age: 30 }
      expect(cleanAndParseJson(validJson)).toEqual(expected)
      expect(cleanStringData).toHaveBeenCalledWith(validJson)
    })

    it('should handle nested JSON objects', () => {
      const nestedJson = '{"user": {"name": "John", "address": {"city": "NY"}}}'
      const expected = { user: { name: 'John', address: { city: 'NY' } } }
      expect(cleanAndParseJson(nestedJson)).toEqual(expected)
    })

    it('should handle JSON arrays', () => {
      const jsonArray = '[1, 2, 3, "four", {"five": 5}]'
      const expected = [1, 2, 3, 'four', { five: 5 }]
      expect(cleanAndParseJson(jsonArray)).toEqual(expected)
    })

    it('should return null for invalid JSON strings', () => {
      const invalidJsons = [
        '{invalid: json}',
        'not json at all',
        '{missing: "closing bracket"',
        '',
      ]

      invalidJsons.forEach((json) => {
        expect(cleanAndParseJson(json)).toBeNull()
      })
    })
  })

  describe('isImageUri', () => {
    it('should return true for URLs with image extensions', () => {
      const imageUrls = [
        'https://example.com/image.jpg',
        'http://example.com/image.jpeg',
        'https://example.com/image.png',
        'https://example.com/image.gif',
        'https://example.com/image.bmp',
        'https://example.com/image.webp',
        'https://example.com/image.svg',
      ]

      imageUrls.forEach((url) => {
        expect(isImageUri(url)).toBe(true)
      })
    })

    it('should return true for data URLs', () => {
      const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=='
      expect(isImageUri(dataUrl)).toBe(true)
    })

    it('should return true for URLs containing "image"', () => {
      const urls = [
        'https://example.com/images/photo',
        'https://example.com/image/123',
        'https://images.example.com/photo',
      ]

      urls.forEach((url) => {
        expect(isImageUri(url)).toBe(true)
      })
    })

    it('should return false for non-image URLs', () => {
      const nonImageUrls = [
        'https://example.com/document.pdf',
        'https://example.com/file.txt',
        'https://example.com/video.mp4',
        'not-a-url',
        '',
        null,
        undefined,
      ]

      nonImageUrls.forEach((url) => {
        expect(isImageUri(url as string)).toBe(false)
      })
    })

    it('should be case-insensitive', () => {
      const urls = [
        'https://example.com/IMAGE.JPG',
        'https://example.com/Photo.PNG',
        'https://example.com/image.SVG',
      ]

      urls.forEach((url) => {
        expect(isImageUri(url)).toBe(true)
      })
    })
  })
})