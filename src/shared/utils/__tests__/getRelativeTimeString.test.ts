import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { formatNumber, formatDate, getRelativeTimeString, getFormattedSince } from '../getRelativeTimeString';
import i18next from 'i18next';

// Mock i18next
vi.mock('i18next', () => ({
  default: {
    language: 'en',
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'workspace.card.minutesAgo': 'minutes ago',
        'workspace.card.hoursAgo': 'hours ago',
        'workspace.card.daysAgo': 'days ago',
        'workspace.card.monthsAgo': 'months ago',
        'workspace.card.yearsAgo': 'years ago',
      };
      return translations[key] || key;
    },
  },
}));

describe('Time and Date Formatting Utils', () => {
  beforeEach(() => {
    // Reset i18next language to English before each test
    vi.spyOn(i18next, 'language', 'get').mockReturnValue('en');
  });

  describe('formatNumber', () => {
    it('should format numbers according to locale', () => {
      expect(formatNumber(1234)).toBe('1,234');
      
      // Test Arabic locale
      vi.spyOn(i18next, 'language', 'get').mockReturnValue('ar');
      expect(formatNumber(1234)).toMatch(/[١٬٢٣٤]/); // Using regex to match Arabic numerals
    });
  });

  describe('formatDate', () => {
    it('should format date according to locale', () => {
      const date = '2024-03-20';
      expect(formatDate(date)).toBe('March 20, 2024');

      // Test Arabic locale
      vi.spyOn(i18next, 'language', 'get').mockReturnValue('ar');
      const arabicDate = formatDate(date);
      // Check that it contains Arabic numerals and formatting
      expect(arabicDate).toMatch(/٢٠/); // Day in Arabic
      expect(arabicDate).toMatch(/٢٠٢٤/); // Year in Arabic
    });
  });

  describe('getRelativeTimeString', () => {
    beforeEach(() => {
      // Mock current date to 2024-03-20 12:00:00
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2024-03-20T12:00:00'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return minutes for differences less than an hour', () => {
      const date = new Date('2024-03-20T11:30:00').toISOString();
      expect(getRelativeTimeString(date)).toEqual({
        count: 30,
        unit: 'minutes ago',
      });
    });

    it('should return hours for differences less than a day', () => {
      const date = new Date('2024-03-20T08:00:00').toISOString();
      expect(getRelativeTimeString(date)).toEqual({
        count: 4,
        unit: 'hours ago',
      });
    });

    it('should return days for differences less than a month', () => {
      const date = new Date('2024-03-10T12:00:00').toISOString();
      expect(getRelativeTimeString(date)).toEqual({
        count: 10,
        unit: 'days ago',
      });
    });

    it('should return months for differences less than a year', () => {
      const date = new Date('2023-12-20T12:00:00').toISOString();
      expect(getRelativeTimeString(date)).toEqual({
        count: 3,
        unit: 'months ago',
      });
    });

    it('should return years for differences more than a year', () => {
      const date = new Date('2022-03-20T12:00:00').toISOString();
      expect(getRelativeTimeString(date)).toEqual({
        count: 2,
        unit: 'years ago',
      });
    });
  });

  describe('getFormattedSince', () => {
    beforeEach(() => {
      // Reset i18next language to English before each test
      vi.spyOn(i18next, 'language', 'get').mockReturnValue('en');
    });

    it('should format numbers in the since string', () => {
      expect(getFormattedSince('4 days ago')).toBe('4 days ago');
      
      // Test with Arabic numbers
      vi.spyOn(i18next, 'language', 'get').mockReturnValue('ar');
      expect(getFormattedSince('4 أيام')).toMatch(/٤ أيام/);
    });

    it('should handle empty or invalid input', () => {
      expect(getFormattedSince('')).toBe('');
      expect(getFormattedSince('no numbers here')).toBe('no numbers here');
    });
  });
});