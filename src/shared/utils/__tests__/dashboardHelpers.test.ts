import { describe, it, expect } from 'vitest'
import { getPageNumbers } from '../dashboardHelpers'

describe('dashboardHelpers', () => {
  describe('getPageNumbers', () => {
    it('should return all page numbers when total pages is 5 or less', () => {
      // Test with 1 page
      expect(getPageNumbers(1, 1)).toEqual([1])

      // Test with 3 pages
      expect(getPageNumbers(2, 3)).toEqual([1, 2, 3])

      // Test with 5 pages
      expect(getPageNumbers(3, 5)).toEqual([1, 2, 3, 4, 5])
    })

    it('should handle current page near the start', () => {
      // Test with current page 1
      expect(getPageNumbers(1, 10)).toEqual([1, 2, 3, 4, '...', 10])

      // Test with current page 2
      expect(getPageNumbers(2, 10)).toEqual([1, 2, 3, 4, '...', 10])

      // Test with current page 3
      expect(getPageNumbers(3, 10)).toEqual([1, 2, 3, 4, '...', 10])
    })

    it('should handle current page near the end', () => {
      // Test with current page at last position
      expect(getPageNumbers(10, 10)).toEqual([1, '...', 7, 8, 9, 10])

      // Test with current page at second to last position
      expect(getPageNumbers(9, 10)).toEqual([1, '...', 7, 8, 9, 10])

      // Test with current page at third to last position
      expect(getPageNumbers(8, 10)).toEqual([1, '...', 7, 8, 9, 10])
    })

    it('should handle current page in the middle', () => {
      // Test with current page in middle of 10 pages
      expect(getPageNumbers(5, 10)).toEqual([1, '...', 4, 5, 6, '...', 10])

      // Test with current page 4 of 10 pages
      expect(getPageNumbers(4, 10)).toEqual([1, '...', 3, 4, 5, '...', 10])

      // Test with current page 7 of 10 pages
      expect(getPageNumbers(7, 10)).toEqual([1, '...', 6, 7, 8, '...', 10])
    })

    it('should handle edge cases', () => {
      // Test with 0 total pages
      expect(getPageNumbers(1, 0)).toEqual([])

      // Test with negative total pages
      expect(getPageNumbers(1, -1)).toEqual([])

      // Test with current page greater than total pages
      expect(getPageNumbers(10, 5)).toEqual([1, 2, 3, 4, 5])

      // Test with negative current page
      expect(getPageNumbers(-1, 10)).toEqual([1, 2, 3, 4, '...', 10])
    })

    it('should handle large number of pages', () => {
      // Test with 100 pages, current page at start
      expect(getPageNumbers(1, 100)).toEqual([1, 2, 3, 4, '...', 100])

      // Test with 100 pages, current page in middle
      expect(getPageNumbers(50, 100)).toEqual([1, '...', 49, 50, 51, '...', 100])

      // Test with 100 pages, current page at end
      expect(getPageNumbers(100, 100)).toEqual([1, '...', 97, 98, 99, 100])
    })

    it('should handle boundary conditions', () => {
      // Test with 6 pages (just above the threshold)
      expect(getPageNumbers(1, 6)).toEqual([1, 2, 3, 4, '...', 6])
      expect(getPageNumbers(3, 6)).toEqual([1, 2, 3, 4, '...', 6])
      expect(getPageNumbers(4, 6)).toEqual([1, '...', 3, 4, 5, 6])
      expect(getPageNumbers(6, 6)).toEqual([1, '...', 3, 4, 5, 6])

      // Test with 7 pages
      expect(getPageNumbers(1, 7)).toEqual([1, 2, 3, 4, '...', 7])
      expect(getPageNumbers(4, 7)).toEqual([1, '...', 3, 4, 5, '...', 7])
      expect(getPageNumbers(7, 7)).toEqual([1, '...', 4, 5, 6, 7])
    })
  })
})