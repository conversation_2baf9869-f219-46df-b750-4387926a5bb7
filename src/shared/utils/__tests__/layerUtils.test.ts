import { describe, it, expect } from 'vitest'
import {
  getColorBySldType,
  getOpacityBySldType,
  getFeatureStyleBySldType,
  getSldByType,
} from '../layerUtils'
import { SldType, type Sld } from '@/shared/store/slices/mapSlice'

describe('layerUtils', () => {
  const mockSlds: Sld[] = [
    {
      id: '1',
      sldType: SldType.POINTS,
      title: 'Points Style',
      featureStyle: {
        color: '#FF0000',
        opacity: 0.8,
        stroke_color: '#000000',
        stroke_width: 1,
        stroke_opacity: 1,
      },
    },
    {
      id: '2',
      sldType: SldType.HEATMAP,
      title: 'Heatmap Style',
      featureStyle: {
        color: '#00FF00',
        opacity: 0.6,
        stroke_color: '#FFFFFF',
        stroke_width: 2,
        stroke_opacity: 0.8,
      },
    },
  ]

  describe('getColorBySldType', () => {
    it('should return default color when slds is undefined or empty', () => {
      expect(getColorBySldType(undefined)).toBe('#000000')
      expect(getColorBySldType([])).toBe('#000000')
    })

    it('should return custom default color when provided', () => {
      expect(getColorBySldType(undefined, SldType.POINTS, '#FFFFFF')).toBe('#FFFFFF')
    })

    it('should return color for matching sld type', () => {
      expect(getColorBySldType(mockSlds, SldType.POINTS)).toBe('#FF0000')
      expect(getColorBySldType(mockSlds, SldType.HEATMAP)).toBe('#00FF00')
    })

    it('should return default color when sld type not found', () => {
      const defaultColor = '#CCCCCC'
      expect(getColorBySldType(mockSlds, SldType.POINTS, defaultColor)).toBe('#FF0000')
      expect(getColorBySldType([{ ...mockSlds[0], sldType: 'UNKNOWN' as SldType }], SldType.POINTS, defaultColor)).toBe(defaultColor)
    })

    it('should return default color when feature style is undefined', () => {
      const sldsWithoutStyle = [{ ...mockSlds[0], featureStyle: undefined }]
      expect(getColorBySldType(sldsWithoutStyle, SldType.POINTS)).toBe('#000000')
    })
  })

  describe('getOpacityBySldType', () => {
    it('should return default opacity when slds is undefined or empty', () => {
      expect(getOpacityBySldType(undefined)).toBe(1)
      expect(getOpacityBySldType([])).toBe(1)
    })

    it('should return custom default opacity when provided', () => {
      expect(getOpacityBySldType(undefined, SldType.POINTS, 0.5)).toBe(0.5)
    })

    it('should return opacity for matching sld type', () => {
      expect(getOpacityBySldType(mockSlds, SldType.POINTS)).toBe(0.8)
      expect(getOpacityBySldType(mockSlds, SldType.HEATMAP)).toBe(0.6)
    })

    it('should return default opacity when sld type not found', () => {
      const defaultOpacity = 0.7
      expect(getOpacityBySldType(mockSlds, SldType.POINTS, defaultOpacity)).toBe(0.8)
      expect(getOpacityBySldType([{ ...mockSlds[0], sldType: 'UNKNOWN' as SldType }], SldType.POINTS, defaultOpacity)).toBe(defaultOpacity)
    })

    it('should return default opacity when feature style is undefined', () => {
      const sldsWithoutStyle = [{ ...mockSlds[0], featureStyle: undefined }]
      expect(getOpacityBySldType(sldsWithoutStyle, SldType.POINTS)).toBe(1)
    })
  })

  describe('getFeatureStyleBySldType', () => {
    it('should return undefined when slds is undefined or empty', () => {
      expect(getFeatureStyleBySldType(undefined)).toBeUndefined()
      expect(getFeatureStyleBySldType([])).toBeUndefined()
    })

    it('should return feature style for matching sld type', () => {
      expect(getFeatureStyleBySldType(mockSlds, SldType.POINTS)).toEqual({
        color: '#FF0000',
        opacity: 0.8,
        stroke_color: '#000000',
        stroke_width: 1,
        stroke_opacity: 1,
      })
      expect(getFeatureStyleBySldType(mockSlds, SldType.HEATMAP)).toEqual({
        color: '#00FF00',
        opacity: 0.6,
        stroke_color: '#FFFFFF',
        stroke_width: 2,
        stroke_opacity: 0.8,
      })
    })

    it('should return undefined when sld type not found', () => {
      expect(getFeatureStyleBySldType([{ ...mockSlds[0], sldType: 'UNKNOWN' as SldType }], SldType.POINTS)).toBeUndefined()
    })

    it('should return undefined when feature style is undefined', () => {
      const sldsWithoutStyle = [{ ...mockSlds[0], featureStyle: undefined }]
      expect(getFeatureStyleBySldType(sldsWithoutStyle, SldType.POINTS)).toBeUndefined()
    })
  })

  describe('getSldByType', () => {
    it('should return undefined when slds is undefined or empty', () => {
      expect(getSldByType(undefined)).toBeUndefined()
      expect(getSldByType([])).toBeUndefined()
    })

    it('should return sld for matching sld type', () => {
      expect(getSldByType(mockSlds, SldType.POINTS)).toEqual(mockSlds[0])
      expect(getSldByType(mockSlds, SldType.HEATMAP)).toEqual(mockSlds[1])
    })

    it('should return undefined when sld type not found', () => {
      expect(getSldByType([{ ...mockSlds[0], sldType: 'UNKNOWN' as SldType }], SldType.POINTS)).toBeUndefined()
    })
  })
})