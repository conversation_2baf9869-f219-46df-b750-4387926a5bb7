import { describe, it, expect } from 'vitest'
import { getQueryByWorkspaceType } from '../layerQueries'
import { GET_LAYERS, GET_DESIGN_LAYERS } from '@/shared/graphQl/queries/layers'
import { WorkspaceMethods } from '@/shared/utils/routes'

describe('layerQueries', () => {
  describe('getQueryByWorkspaceType', () => {
    it('should return GET_DESIGN_LAYERS for DESIGN_LAYER type', () => {
      const result = getQueryByWorkspaceType(WorkspaceMethods.DESIGN_LAYER)
      expect(result).toBe(GET_DESIGN_LAYERS)
    })

    it('should return GET_LAYERS for UPLOAD_FILE type', () => {
      const result = getQueryByWorkspaceType(WorkspaceMethods.UPLOAD_FILE)
      expect(result).toBe(GET_LAYERS)
    })

    it('should return GET_LAYERS for unknown type', () => {
      const result = getQueryByWorkspaceType('UNKNOWN' as WorkspaceMethods)
      expect(result).toBe(GET_LAYERS)
    })

    it('should return GET_LAYERS as default', () => {
      const result = getQueryByWorkspaceType(undefined as unknown as WorkspaceMethods)
      expect(result).toBe(GET_LAYERS)
    })
  })
})