import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { handleUnauthorizedError } from '../authHelpers'

describe('authHelpers', () => {
  describe('handleUnauthorizedError', () => {
    const originalLocation = window.location

    beforeEach(() => {
      // Mock window.location
      const location = {
        ...originalLocation,
        replace: vi.fn(),
      }
      Object.defineProperty(window, 'location', {
        value: location,
        writable: true,
      })
      vi.clearAllMocks()
    })

    afterEach(() => {
      // Restore window.location
      Object.defineProperty(window, 'location', {
        value: originalLocation,
        writable: true,
      })
    })

    it('should handle GraphQL errors array with 401 status', () => {
      const error = {
        errors: [
          {
            extensions: {
              http: {
                status: 401,
              },
            },
          },
        ],
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle GraphQL errors array with Unauthorized message', () => {
      const error = {
        errors: [
          {
            message: 'Unauthorized',
          },
        ],
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle error message containing 401', () => {
      const error = {
        message: 'Request failed with status code 401',
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle error message containing Unauthorized', () => {
      const error = {
        message: 'Unauthorized access',
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle single GraphQL error with 401 status', () => {
      const error = {
        graphQLErrors: [
          {
            extensions: {
              http: {
                status: 401,
              },
            },
          },
        ],
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should return false for non-401 errors', () => {
      const errors = [
        { errors: [{ extensions: { http: { status: 404 } } }] },
        { errors: [{ message: 'Not Found' }] },
        { message: 'Request failed with status code 404' },
        { graphQLErrors: [{ extensions: { http: { status: 500 } } }] },
        { message: 'Internal Server Error' },
        {},
        null,
        undefined,
      ]

      errors.forEach((error) => {
        const result = handleUnauthorizedError(error)
        expect(result).toBe(false)
        expect(window.location.replace).not.toHaveBeenCalled()
      })
    })

    it('should handle deeply nested error objects', () => {
      const error = {
        errors: [
          {
            extensions: {
              http: {
                status: 401,
                headers: {
                  'www-authenticate': 'Bearer',
                },
                body: {
                  message: 'Token expired',
                },
              },
            },
          },
        ],
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle multiple errors with one being 401', () => {
      const error = {
        errors: [
          { extensions: { http: { status: 404 } } },
          { extensions: { http: { status: 401 } } },
          { extensions: { http: { status: 500 } } },
        ],
      }

      const result = handleUnauthorizedError(error)
      expect(result).toBe(true)
      expect(window.location.replace).toHaveBeenCalledWith('/login')
    })

    it('should handle error objects with missing properties', () => {
      const errors = [
        { errors: [{}] },
        { errors: [{ extensions: {} }] },
        { errors: [{ extensions: { http: {} } }] },
        { graphQLErrors: [] },
        { graphQLErrors: [{}] },
        { graphQLErrors: [{ extensions: {} }] },
      ]

      errors.forEach((error) => {
        const result = handleUnauthorizedError(error)
        expect(result).toBe(false)
        expect(window.location.replace).not.toHaveBeenCalled()
      })
    })
  })
})