import { describe, it, expect, vi } from 'vitest'
import { validateForm } from '../designlayerValidation'
import { FormField } from '@/components/MultiStepForm/steps/designLayerSteps/LayerFormStep/types'
import i18next from 'i18next'

// Mock i18next
vi.mock('i18next', () => ({
  default: {
    t: vi.fn((key: string) => key),
  },
}))

describe('designlayerValidation', () => {
  describe('validateForm', () => {
    it('should return no errors for valid form data', () => {
      const formData: FormField = {
        name: 'validName123',
        title: 'Valid Title',
        type: 'text',
      }

      const errors = validateForm(formData)
      expect(errors).toEqual({})
    })

    it('should validate required name field', () => {
      const formData: FormField = {
        name: '',
        title: 'Valid Title',
        type: 'text',
      }

      const errors = validateForm(formData)
      expect(errors.name).toBe('workspace.layerForm.validation.identifier.required')
      expect(i18next.t).toHaveBeenCalledWith('workspace.layerForm.validation.identifier.required')
    })

    it('should validate name field format', () => {
      const formData: FormField = {
        name: 'invalid name!@#',
        title: 'Valid Title',
        type: 'text',
      }

      const errors = validateForm(formData)
      expect(errors.name).toBe('workspace.layerForm.validation.identifier.format')
      expect(i18next.t).toHaveBeenCalledWith('workspace.layerForm.validation.identifier.format')
    })

    it('should validate required title field', () => {
      const formData: FormField = {
        name: 'validName123',
        title: '',
        type: 'text',
      }

      const errors = validateForm(formData)
      expect(errors.title).toBe('workspace.layerForm.validation.fieldTitle.required')
      expect(i18next.t).toHaveBeenCalledWith('workspace.layerForm.validation.fieldTitle.required')
    })

    it('should validate required type field', () => {
      const formData: FormField = {
        name: 'validName123',
        title: 'Valid Title',
        type: '',
      }

      const errors = validateForm(formData)
      expect(errors.type).toBe('workspace.layerForm.validation.fieldType.required')
      expect(i18next.t).toHaveBeenCalledWith('workspace.layerForm.validation.fieldType.required')
    })

    it('should validate multiple fields with errors', () => {
      const formData: FormField = {
        name: '',
        title: '',
        type: '',
      }

      const errors = validateForm(formData)
      expect(errors).toEqual({
        name: 'workspace.layerForm.validation.identifier.required',
        title: 'workspace.layerForm.validation.fieldTitle.required',
        type: 'workspace.layerForm.validation.fieldType.required',
      })
    })

    it('should validate name field with special characters', () => {
      const invalidNames = [
        'name with spaces',
        'name-with-hyphens',
        'name_with_underscores',
        'name@with@symbols',
        'name.with.dots',
      ]

      invalidNames.forEach((name) => {
        const formData: FormField = {
          name,
          title: 'Valid Title',
          type: 'text',
        }

        const errors = validateForm(formData)
        expect(errors.name).toBe('workspace.layerForm.validation.identifier.format')
      })
    })

    it('should accept valid name formats', () => {
      const validNames = [
        'validName',
        'ValidName',
        'valid123',
        '123valid',
        'VALIDNAME',
        '123456',
      ]

      validNames.forEach((name) => {
        const formData: FormField = {
          name,
          title: 'Valid Title',
          type: 'text',
        }

        const errors = validateForm(formData)
        expect(errors.name).toBeUndefined()
      })
    })
  })
})