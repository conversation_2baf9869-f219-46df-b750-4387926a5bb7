import { describe, it, expect, vi } from 'vitest'
import { updateLayersVisibility } from '../layerVisibilityUtils'
import { store } from '@/shared/store'
import { updateLayersVisibility as updateLayersVisibilityAction } from '@/shared/store/slices/mapSlice'

// Mock store and action
vi.mock('@/shared/store', () => ({
  store: {
    dispatch: vi.fn(),
  },
}))

vi.mock('@/shared/store/slices/mapSlice', () => ({
  updateLayersVisibility: vi.fn(),
}))

describe('layerVisibilityUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('updateLayersVisibility', () => {
    it('should call mapManagerAction and dispatch update action', () => {
      const mapManagerAction = vi.fn()
      const targetLayerKey = 'layer1'

      updateLayersVisibility(mapManagerAction, targetLayerKey)

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledWith(
        updateLayersVisibilityAction({ targetLayerKey, showAll: undefined })
      )
    })

    it('should handle showAll parameter', () => {
      const mapManagerAction = vi.fn()
      const targetLayerKey = 'layer1'
      const showAll = true

      updateLayersVisibility(mapManagerAction, targetLayerKey, showAll)

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledWith(
        updateLayersVisibilityAction({ targetLayerKey, showAll })
      )
    })

    it('should call onComplete callback if provided', () => {
      const mapManagerAction = vi.fn()
      const onComplete = vi.fn()

      updateLayersVisibility(mapManagerAction, undefined, undefined, onComplete)

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledTimes(1)
      expect(onComplete).toHaveBeenCalledTimes(1)
    })

    it('should not call onComplete if not provided', () => {
      const mapManagerAction = vi.fn()
      const onComplete = vi.fn()

      updateLayersVisibility(mapManagerAction)

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledTimes(1)
      expect(onComplete).not.toHaveBeenCalled()
    })

    it('should handle undefined targetLayerKey', () => {
      const mapManagerAction = vi.fn()

      updateLayersVisibility(mapManagerAction)

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledTimes(1)
      expect(store.dispatch).toHaveBeenCalledWith(
        updateLayersVisibilityAction({ targetLayerKey: undefined, showAll: undefined })
      )
    })

    it('should maintain correct call order', () => {
      const mapManagerAction = vi.fn()
      const onComplete = vi.fn()
      const callOrder: string[] = []

      mapManagerAction.mockImplementation(() => callOrder.push('mapManagerAction'))
      vi.mocked(store.dispatch).mockImplementation(() => {
        callOrder.push('dispatch')
        return undefined as any
      })
      onComplete.mockImplementation(() => callOrder.push('onComplete'))

      updateLayersVisibility(mapManagerAction, undefined, undefined, onComplete)

      expect(callOrder).toEqual(['mapManagerAction', 'dispatch', 'onComplete'])
    })

    it('should handle errors in mapManagerAction', () => {
      const mapManagerAction = vi.fn(() => {
        throw new Error('Test error')
      })
      const onComplete = vi.fn()

      expect(() => {
        updateLayersVisibility(mapManagerAction, undefined, undefined, onComplete)
      }).toThrow('Test error')

      expect(mapManagerAction).toHaveBeenCalledTimes(1)
      expect(store.dispatch).not.toHaveBeenCalled()
      expect(onComplete).not.toHaveBeenCalled()
    })
  })
})