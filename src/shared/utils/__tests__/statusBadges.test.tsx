import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { renderStatusBadge, mapActiveStatusToEnum } from '../statusBadges'
import { UserStatus, getUserStatusLabel } from '@/shared/types/userStatus'

// Mock userStatus
vi.mock('@/shared/types/userStatus', () => ({
  UserStatus: {
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
    WAITING: 'WAITING',
    DELETED: 'DELETED',
  },
  getUserStatusLabel: vi.fn((status: string) => status.toLowerCase()),
}))

describe('statusBadges', () => {
  const mockT = vi.fn((key: string) => key)

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('renderStatusBadge', () => {
    it('should render active status badge with correct styles', () => {
      const { container } = render(renderStatusBadge(UserStatus.ACTIVE, mockT))
      const badge = container.firstChild as HTMLElement

      expect(badge).toHaveClass('text-green-600', 'bg-green-100')
      expect(badge.textContent).toBe('active')
      expect(getUserStatusLabel).toHaveBeenCalledWith(UserStatus.ACTIVE, mockT)
    })

    it('should render inactive status badge with correct styles', () => {
      const { container } = render(renderStatusBadge(UserStatus.INACTIVE, mockT))
      const badge = container.firstChild as HTMLElement

      expect(badge).toHaveClass('text-red-600', 'bg-red-100')
      expect(badge.textContent).toBe('inactive')
      expect(getUserStatusLabel).toHaveBeenCalledWith(UserStatus.INACTIVE, mockT)
    })

    it('should render waiting status badge with correct styles', () => {
      const { container } = render(renderStatusBadge(UserStatus.WAITING, mockT))
      const badge = container.firstChild as HTMLElement

      expect(badge).toHaveClass('text-gray-600', 'bg-gray-100')
      expect(badge.textContent).toBe('waiting')
      expect(getUserStatusLabel).toHaveBeenCalledWith(UserStatus.WAITING, mockT)
    })

    it('should render deleted status badge with correct styles', () => {
      const { container } = render(renderStatusBadge(UserStatus.DELETED, mockT))
      const badge = container.firstChild as HTMLElement

      expect(badge).toHaveClass('text-orange-600', 'bg-orange-100')
      expect(badge.textContent).toBe('deleted')
      expect(getUserStatusLabel).toHaveBeenCalledWith(UserStatus.DELETED, mockT)
    })

    it('should render default status badge with correct styles for unknown status', () => {
      const { container } = render(renderStatusBadge('UNKNOWN' as UserStatus, mockT))
      const badge = container.firstChild as HTMLElement

      expect(badge).toHaveClass('text-[#6366F1]', 'bg-[#6366F1]/10')
      expect(badge.textContent).toBe('unknown')
      expect(getUserStatusLabel).toHaveBeenCalledWith('UNKNOWN', mockT)
    })

    it('should render badge with common styles for all statuses', () => {
      const statuses = [
        UserStatus.ACTIVE,
        UserStatus.INACTIVE,
        UserStatus.WAITING,
        UserStatus.DELETED,
      ]

      statuses.forEach((status) => {
        const { container } = render(renderStatusBadge(status, mockT))
        const badge = container.firstChild as HTMLElement

        expect(badge).toHaveClass('px-3', 'py-1', 'rounded', 'text-sm')
      })
    })
  })

  describe('mapActiveStatusToEnum', () => {
    it('should map ACTIVE status correctly', () => {
      expect(mapActiveStatusToEnum('ACTIVE')).toBe(UserStatus.ACTIVE)
    })

    it('should map WAITING status correctly', () => {
      expect(mapActiveStatusToEnum('WAITING')).toBe(UserStatus.WAITING)
    })

    it('should map INACTIVE status correctly', () => {
      expect(mapActiveStatusToEnum('INACTIVE')).toBe(UserStatus.INACTIVE)
    })

    it('should map unknown status to DELETED', () => {
      expect(mapActiveStatusToEnum('UNKNOWN')).toBe(UserStatus.DELETED)
    })

    it('should map empty string to DELETED', () => {
      expect(mapActiveStatusToEnum('')).toBe(UserStatus.DELETED)
    })
  })
})