import { describe, it, expect } from 'vitest'
import { checkForExistingWorkspaceId } from '../workspaceUtils'

describe('workspaceUtils', () => {
  describe('checkForExistingWorkspaceId', () => {
    it('should return workspace ID when found in data', () => {
      const workspaceExistenceData = {
        workspaceRequests: {
          data: [
            {
              dataset: {
                workspace: {
                  id: 'workspace-123',
                },
              },
            },
          ],
        },
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBe('workspace-123')
    })

    it('should return null when no workspace ID is found', () => {
      const workspaceExistenceData = {
        workspaceRequests: {
          data: [
            {
              dataset: {
                workspace: {
                  id: null,
                },
              },
            },
          ],
        },
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBeNull()
    })

    it('should return null when data is empty', () => {
      const workspaceExistenceData = {
        workspaceRequests: {
          data: [],
        },
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBeNull()
    })

    it('should return null when data structure is invalid', () => {
      const workspaceExistenceData = {
        workspaceRequests: null,
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBeNull()
    })

    it('should return null when input is null', () => {
      const result = checkForExistingWorkspaceId(null)
      expect(result).toBeNull()
    })

    it('should return null when input is undefined', () => {
      const result = checkForExistingWorkspaceId(undefined)
      expect(result).toBeNull()
    })

    it('should return first workspace ID when multiple are found', () => {
      const workspaceExistenceData = {
        workspaceRequests: {
          data: [
            {
              dataset: {
                workspace: {
                  id: 'workspace-123',
                },
              },
            },
            {
              dataset: {
                workspace: {
                  id: 'workspace-456',
                },
              },
            },
          ],
        },
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBe('workspace-123')
    })

    it('should skip entries without workspace ID and return first valid one', () => {
      const workspaceExistenceData = {
        workspaceRequests: {
          data: [
            {
              dataset: {
                workspace: null,
              },
            },
            {
              dataset: {
                workspace: {
                  id: 'workspace-456',
                },
              },
            },
          ],
        },
      }

      const result = checkForExistingWorkspaceId(workspaceExistenceData)
      expect(result).toBe('workspace-456')
    })
  })
})