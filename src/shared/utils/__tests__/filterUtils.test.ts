import { describe, it, expect, vi } from 'vitest'
import {
  getReadableFilterText,
  formatFilterValue,
  isFilterComplete,
  formatSelectValue,
  extractSelectValues,
  type AvailableField,
} from '../filterUtils'
import { FilterCondition } from '@/shared/store/slices/mapSlice'

// Mock i18next (used by getOperationsForFieldType)
vi.mock('i18next', () => ({
  default: {
    t: (key: string) => key,
  },
}))

describe('filterUtils', () => {
  describe('getReadableFilterText', () => {
    const availableFields: AvailableField[] = [
      { name: 'name', label: 'Name', type: 'string' },
      { name: 'age', label: 'Age', type: 'number' },
      { name: 'isActive', label: 'Active', type: 'boolean' },
    ]

    it('should return empty string for incomplete filter', () => {
      const filter: FilterCondition = {
        id: '1',
        field: '',
        operator: '',
        value: null,
        dataType: 'string',
        isVisible: true,
        isNot: false,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe('')
    })

    it('should format string filter correctly', () => {
      const filter: FilterCondition = {
        id: '1',
        field: 'name',
        operator: 'icontains',
        value: 'John',
        dataType: 'string',
        isVisible: true,
        isNot: false,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe(
        'Name layerSettings.filters.filterOperators.contains John'
      )
    })

    it('should format number filter correctly', () => {
      const filter: FilterCondition = {
        id: '1',
        field: 'age',
        operator: 'gt',
        value: 18,
        dataType: 'number',
        isVisible: true,
        isNot: false,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe(
        'Age layerSettings.filters.filterOperators.greaterThan 18'
      )
    })

    it('should format array values correctly', () => {
      const filter: FilterCondition = {
        id: '1',
        field: 'name',
        operator: 'range',
        value: ['A', 'Z'],
        dataType: 'string',
        isVisible: true,
        isNot: false,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe(
        'Name range A، Z'
      )
    })

    it('should handle unknown field names', () => {
      const filter: FilterCondition = {
        id: '1',
        field: 'unknown',
        operator: 'iexact',
        value: 'test',
        dataType: 'string',
        isVisible: true,
        isNot: false,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe(
        'unknown layerSettings.filters.filterOperators.equals test'
      )
    })

    it('should handle negated operators', () => {
      const filter: FilterCondition = {
        id: '1',
        field: 'name',
        operator: 'iexact',
        value: 'John',
        dataType: 'string',
        isNot: true,
        isVisible: true,
      }
      expect(getReadableFilterText(filter, availableFields)).toBe(
        'Name layerSettings.filters.filterOperators.notEquals John'
      )
    })
  })

  describe('formatFilterValue', () => {
    it('should return null for empty values', () => {
      expect(formatFilterValue(null)).toBeNull()
      expect(formatFilterValue(undefined)).toBeNull()
      expect(formatFilterValue('')).toBeNull()
    })

    it('should return the value for non-empty values', () => {
      expect(formatFilterValue('test')).toBe('test')
      expect(formatFilterValue(123)).toBe(123)
      expect(formatFilterValue(true)).toBe(true)
      expect(formatFilterValue([1, 2, 3])).toEqual([1, 2, 3])
    })
  })

  describe('isFilterComplete', () => {
    it('should return false for incomplete filters', () => {
      expect(isFilterComplete({ id: '1', field: '', operator: '', value: null, isVisible: true, isNot: false, dataType: 'string' })).toBe(false)
      expect(isFilterComplete({ id: '1', field: 'name', operator: '', value: null, isVisible: true, isNot: false, dataType: 'string' })).toBe(false)
      expect(isFilterComplete({ id: '1', field: 'name', operator: 'iexact', value: null, isVisible: true, isNot: false, dataType: 'string' })).toBe(false)
      expect(isFilterComplete({ id: '1', field: 'name', operator: 'iexact', value: undefined, isVisible: true, isNot: false, dataType: 'string' })).toBe(false)
    })

    it('should return true for complete filters', () => {
      expect(isFilterComplete({ id: '1', field: 'name', operator: 'iexact', value: 'test', isVisible: true, isNot: false, dataType: 'string' })).toBe(true)
      expect(isFilterComplete({ id: '1', field: 'age', operator: 'gt', value: 0, isVisible: true, isNot: false, dataType: 'number' })).toBe(true)
      expect(isFilterComplete({ id: '1', field: 'tags', operator: 'range', value: ['A', 'Z'], isVisible: true, isNot: false, dataType: 'string' })).toBe(true)
    })
  })

  describe('formatSelectValue', () => {
    it('should return empty array for empty values', () => {
      expect(formatSelectValue(null)).toEqual([])
      expect(formatSelectValue(undefined)).toEqual([])
      expect(formatSelectValue('')).toEqual([])
    })

    it('should format single value correctly', () => {
      expect(formatSelectValue('test')).toEqual([{ value: 'test', label: 'test' }])
    })

    it('should format array values correctly', () => {
      expect(formatSelectValue(['A', 'B'])).toEqual([
        { value: 'A', label: 'A' },
        { value: 'B', label: 'B' },
      ])
    })
  })

  describe('extractSelectValues', () => {
    it('should return null for empty arrays', () => {
      expect(extractSelectValues([])).toBeNull()
      expect(extractSelectValues([] as any[])).toBeNull()
      expect(extractSelectValues([] as any[])).toBeNull()
    })

    it('should extract values from select options', () => {
      const options = [
        { value: 'A', label: 'Option A' },
        { value: 'B', label: 'Option B' },
      ]
      expect(extractSelectValues(options)).toEqual(['A', 'B'])
    })
  })
})