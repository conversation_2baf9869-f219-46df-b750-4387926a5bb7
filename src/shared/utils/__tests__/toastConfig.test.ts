import { describe, it, expect, vi, beforeEach } from 'vitest'
import { toast, ToastOptions, ToastPosition } from 'react-toastify'
import { showToast } from '../toastConfig'
import i18n from '@/i18n'

// Mock react-toastify
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Mock i18n
vi.mock('@/i18n', () => ({
  default: {
    language: 'en',
  },
}))

// Mock defaultToastConfig
const mockDefaultToastConfig: ToastOptions = {
  position: 'top-center',
  autoClose: 2000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  rtl: false,
}

// Mock the module
vi.mock('../toastConfig', () => ({
  showToast: {
    error: (message: string) => toast.error(message, mockDefaultToastConfig),
    success: (message: string) => toast.success(message, mockDefaultToastConfig),
    info: (message: string) => toast.info(message, mockDefaultToastConfig),
    warning: (message: string) => toast.warning(message, mockDefaultToastConfig),
  },
}))

describe('toastConfig', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    Object.assign(mockDefaultToastConfig, {
      position: 'top-center',
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      rtl: false,
    })
  })

  describe('showToast.error', () => {
    it('should call toast.error with message and default config', () => {
      const message = 'Error message'
      showToast.error(message)
      expect(toast.error).toHaveBeenCalledWith(message, mockDefaultToastConfig)
    })
  })

  describe('showToast.success', () => {
    it('should call toast.success with message and default config', () => {
      const message = 'Success message'
      showToast.success(message)
      expect(toast.success).toHaveBeenCalledWith(message, mockDefaultToastConfig)
    })
  })

  describe('showToast.info', () => {
    it('should call toast.info with message and default config', () => {
      const message = 'Info message'
      showToast.info(message)
      expect(toast.info).toHaveBeenCalledWith(message, mockDefaultToastConfig)
    })
  })

  describe('showToast.warning', () => {
    it('should call toast.warning with message and default config', () => {
      const message = 'Warning message'
      showToast.warning(message)
      expect(toast.warning).toHaveBeenCalledWith(message, mockDefaultToastConfig)
    })
  })

  describe('RTL support', () => {
    it('should set rtl to true when language is Arabic', () => {
      // Change language to Arabic
      vi.mocked(i18n).language = 'ar'

      const message = 'Test message'
      mockDefaultToastConfig.rtl = true
      showToast.info(message)
      expect(toast.info).toHaveBeenCalledWith(message, mockDefaultToastConfig)

      // Reset language
      vi.mocked(i18n).language = 'en'
    })

    it('should set rtl to false when language is not Arabic', () => {
      // Change language to English
      vi.mocked(i18n).language = 'en'

      const message = 'Test message'
      mockDefaultToastConfig.rtl = false
      showToast.info(message)
      expect(toast.info).toHaveBeenCalledWith(message, mockDefaultToastConfig)
    })
  })
})