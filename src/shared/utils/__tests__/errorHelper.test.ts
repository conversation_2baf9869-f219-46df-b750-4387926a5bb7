import { describe, it, expect } from 'vitest'
import { extractReason } from '../errorHelper'
import { CustomGraphQLError } from '@/shared/graphQl'

describe('errorHelper', () => {
  describe('extractReason', () => {
    it('should extract reason from error extensions', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {
            reason: {
              field1: 'Error reason 1',
              field2: 'Error reason 2',
            },
          },
        },
      }

      const result = extractReason(error)
      expect(result).toBe('Error reason 1')
    })

    it('should return error message when no reason in extensions', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {},
        },
      }

      const result = extractReason(error)
      expect(result).toBe('GraphQL Error')
    })

    it('should return error message when no http in extensions', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {},
      }

      const result = extractReason(error)
      expect(result).toBe('GraphQL Error')
    })

    it('should return error message when no extensions', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
      }

      const result = extractReason(error)
      expect(result).toBe('GraphQL Error')
    })

    it('should handle empty reason object', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {
            reason: {},
          },
        },
      }

      const result = extractReason(error)
      expect(result).toBeUndefined()
    })

    it('should handle null reason and return error message', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {
            reason: null,
          },
        },
      }

      const result = extractReason(error)
      expect(result).toBe('GraphQL Error')
    })

    it('should handle undefined reason and return error message', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {
            reason: undefined,
          },
        },
      }

      const result = extractReason(error)
      expect(result).toBe('GraphQL Error')
    })

    it('should handle non-object reason and return first character', () => {
      const error: CustomGraphQLError = {
        message: 'GraphQL Error',
        extensions: {
          http: {
            reason: { message: 'Direct error message' },
          },
        },
      }

      const result = extractReason(error)
      expect(result).toBe('Direct error message')
    })
  })
})