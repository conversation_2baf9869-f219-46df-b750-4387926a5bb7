import { FilterCondition } from '@/shared/store/slices/mapSlice'

/**
 * Supported data types for filter fields
 */
export type FilterDataType =
  | 'string'
  | 'number'
  | 'date'
  | 'time'
  | 'boolean'
  | 'integer'

/**
 * Available operators based on field data type
 */
export const FILTER_OPERATORS = {
  string: ['iexact', 'icontains', 'startswith', 'endswith', 'isnull'] as const,
  number: ['iexact', 'gt', 'lt', 'gte', 'lte', 'range', 'isnull'] as const,
  integer: ['iexact', 'gt', 'lt', 'gte', 'lte', 'range', 'isnull'] as const,
  date: [
    'exact',
    'date__gt',
    'date__lt',
    'date__gte',
    'date__lte',
    'range',
    'isnull',
  ] as const,
  time: ['exact', 'gt', 'lt', 'gte', 'lte', 'range', 'isnull'] as const,
  boolean: [
    'iexact', // 'isnull'
  ] as const,
} as const

/**
 * Type for filter operators based on data type
 */
export type FilterOperator<T extends FilterDataType> =
  (typeof FILTER_OPERATORS)[T][number]

/**
 * Logical operators for combining filters
 */
export type LogicalOperator = 'AND' | 'OR'

/**
 * Interface for a filter group that can contain multiple conditions or groups
 */
export interface FilterGroup {
  operator: LogicalOperator
  conditions?: FilterCondition[]
  groups?: FilterGroup[]
}
