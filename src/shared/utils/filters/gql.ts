import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { FilterGroup } from './types'

/**
 * Interface for GraphQL filter input - includes all FilterCondition fields
 */
export interface DjangoFilterInput {
  field: string
  value: any
  clause: string
  id: string
  operator: string
  dataType: 'string' | 'number' | 'date' | 'time' | 'boolean' | 'integer'
  applied?: boolean
  isVisible: boolean
  isNot?: boolean
}

/**
 * Mapping of operators to Django-compatible clauses
 */
const OPERATOR_TO_CLAUSE_MAP: Record<string, string> = {
  // String operators
  exact: 'exact',
  iexact: 'iexact',
  contains: 'contains',
  icontains: 'icontains',
  startswith: 'startswith',
  endswith: 'endswith',
  isnull: 'isnull',
  isempty: 'isempty',

  // Number operators
  gt: 'gt',
  lt: 'lt',
  gte: 'gte',
  lte: 'lte',
  range: 'range',

  // Date operators
  date__gt: 'date__gt',
  date__lt: 'date__lt',
  date__gte: 'date__gte',
  date__lte: 'date__lte',
} as const

/**
 * Creates a filter input for Django GraphQL with all FilterCondition fields
 */
const createFilterInput = (condition: FilterCondition): DjangoFilterInput => {
  const clause = OPERATOR_TO_CLAUSE_MAP[condition.operator]

  // Handle special cases
  if (condition.operator === 'isnull') {
    return {
      field: condition.field,
      clause: 'isnull',
      value: true,
      id: condition.id,
      operator: condition.operator,
      dataType: condition.dataType,
      applied: condition.applied,
      isVisible: condition.isVisible,
      isNot: condition.isNot,
    }
  }

  if (condition.operator === 'isempty') {
    return {
      field: condition.field,
      clause: 'isempty',
      value: "''",
      id: condition.id,
      operator: condition.operator,
      dataType: condition.dataType,
      applied: condition.applied,
      isVisible: condition.isVisible,
      isNot: condition.isNot,
    }
  }

  if (condition.operator === 'range') {
    return {
      field: condition.field,
      clause: 'range',
      value: condition.value,
      id: condition.id,
      operator: condition.operator,
      dataType: condition.dataType,
      applied: condition.applied,
      isVisible: condition.isVisible,
      isNot: condition.isNot,
    }
  }

  return {
    field: condition.field,
    clause,
    value: condition.value,
    id: condition.id,
    operator: condition.operator,
    dataType: condition.dataType,
    applied: condition.applied,
    isVisible: condition.isVisible,
    isNot: condition.isNot,
  }
}

/**
 * Converts a filter condition to Django-compatible GraphQL filter input
 */
export const toGraphQLFilter = (
  condition: FilterCondition
): DjangoFilterInput => {
  return createFilterInput(condition)
}

/**
 * Converts a filter group to an array of GraphQL filter inputs
 * Note: OR conditions will be handled separately by the backend
 */
export const filterGroupToGraphQL = (
  group: FilterGroup
): DjangoFilterInput[] => {
  const filters: DjangoFilterInput[] = []

  if (group.conditions?.length) {
    filters.push(
      ...group.conditions.map((condition) => toGraphQLFilter(condition))
    )
  }

  if (group.groups?.length) {
    group.groups.forEach((subGroup) => {
      filters.push(...filterGroupToGraphQL(subGroup))
    })
  }

  return filters
}

// Transform old filter format to new FilterGroupInput format
export const transformFiltersToFilterGroups = (
  filters: any[] | null,
  attribute: string = 'data__'
) => {
  if (!filters || !Array.isArray(filters)) return null

  const appliedFilters = filters.filter(
    (filter) => filter.applied && filter.isVisible
  )
  if (appliedFilters.length === 0) return null

  return appliedFilters.map((filter) => {
    const { field, value, clause, operator, isNot } = filter
    const op = operator || clause
    const transformedField = `${attribute}${field.replace(/\./g, '__')}`

    // If value is an array with multiple items, create OR group
    if (Array.isArray(value) && value.length > 1) {
      return {
        groupType: 'OR',
        isNot: false,
        filters: value.map((val) => ({
          op,
          field: transformedField,
          value: val.toString(),
          isNot: isNot,
        })),
      }
    }

    // Handle isnull and isempty operations - create both filters
    if (op === 'isnull' || op === 'isempty') {
      return {
        groupType: 'OR',
        isNot: isNot,
        filters: [
          {
            op: 'isempty',
            field: transformedField,
            value: "''",
            isNot: false,
          },
          {
            op: 'isnull',
            field: transformedField,
            value: 'true',
            isNot: false,
          },
        ],
      }
    }

    // Single value or array with one item, create AND group
    const singleValue = Array.isArray(value) ? value[0] : value
    return {
      groupType: 'AND',
      isNot: false,
      filters: [
        {
          op,
          field: transformedField,
          value: singleValue.toString(),
          isNot: isNot,
        },
      ],
    }
  })
}
