import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { FilterGroup } from './types'

/**
 * Possible CQL clauses matching your structure
 */
export const possibleClauses = {
  exact: 'exact',
  iexact: 'iexact',
  contains: 'contains',
  icontains: 'icontains',
  gt: 'gt',
  lt: 'lt',
  gte: 'gte',
  lte: 'lte',
  startswith: 'startswith',
  endswith: 'endswith',
  isnull: 'isnull',
  isempty: 'isempty',
  range: 'range',
  // Date operators
  date__gt: 'date__gt',
  date__lt: 'date__lt',
  date__gte: 'date__gte',
  date__lte: 'date__lte',
} as const

export type clauseType = keyof typeof possibleClauses

/**
 * CQL Object interface matching your structure
 */
export interface cqlObject {
  attribute: string
  jsonPointer: string
  clause: clauseType
  value: string | number | boolean
}

/**
 * Mapping of operators to CQL clauses
 */
const OPERATOR_TO_CLAUSE_MAP: Record<string, clauseType> = {
  // String operators
  exact: 'exact',
  iexact: 'iexact',
  contains: 'contains',
  icontains: 'icontains',
  startswith: 'startswith',
  endswith: 'endswith',
  isnull: 'isnull',
  isempty: 'isempty',

  // Number operators
  gt: 'gt',
  lt: 'lt',
  gte: 'gte',
  lte: 'lte',
  range: 'range',

  // Date operators
  date__gt: 'date__gt',
  date__lt: 'date__lt',
  date__gte: 'date__gte',
  date__lte: 'date__lte',
} as const

/**
 * Convert object path to CQL JSON pointer
 * Handles nested values like: data.rased.land_realestate_facts.floor_facts
 * Converts to: /data/rased/land_realestate_facts/floor_facts
 *
 * Examples:
 * - "land_use" -> "/land_use"
 * - "data.rased.land_realestate_facts.floor_facts" -> "/data/rased/land_realestate_facts/floor_facts"
 * - "user.profile.settings.theme" -> "/user/profile/settings/theme"
 */
export function objectPathToCqlJsonPointer(objectPath: string): string {
  // Handle empty or invalid paths
  if (!objectPath || typeof objectPath !== 'string') {
    return ''
  }

  // Split by dot notation and create JSON pointer path
  return (
    '/' +
    objectPath
      .split('.')
      .filter((part) => part.length > 0) // Remove empty parts
      .join('/')
  )
}

/**
 * Build single CQL expression using jsonPointer
 */
export function build_cql_single_expression({
  attribute,
  jsonPointer,
  clause,
  value,
}: cqlObject): string {
  // Convert dot notation to JSON pointer if needed
  let jsonPointerPath = jsonPointer
  if (!jsonPointer.startsWith('/')) {
    jsonPointerPath = objectPathToCqlJsonPointer(jsonPointer)
  }

  switch (clause) {
    case 'exact':
      // exact string or numbers
      if (typeof value === 'string') {
        return `jsonPointer(${attribute}, '${jsonPointerPath}') = '${value.replace(/'/g, "''")}'`
      }
      return `jsonPointer(${attribute}, '${jsonPointerPath}') = ${value}`

    case 'iexact':
      // insensitive exact strings
      return `jsonPointer(${attribute}, '${jsonPointerPath}') ilike '${value.toString().replace(/'/g, "''")}'`

    case 'contains':
      // string contains
      return `jsonPointer(${attribute}, '${jsonPointerPath}') like '%${value.toString().replace(/'/g, "''")}%'`

    case 'icontains':
      // insensitive string contains
      return `jsonPointer(${attribute}, '${jsonPointerPath}') ilike '%${value.toString().replace(/'/g, "''")}%'`

    case 'startswith':
      // string starts with
      return `jsonPointer(${attribute}, '${jsonPointerPath}') like '${value.toString().replace(/'/g, "''")}%'`

    case 'endswith':
      // string ends with
      return `jsonPointer(${attribute}, '${jsonPointerPath}') like '%${value.toString().replace(/'/g, "''")}'`

    case 'gt':
      // number greater than
      return `jsonPointer(${attribute}, '${jsonPointerPath}') > ${value}`

    case 'gte':
      // number greater than or equal
      return `jsonPointer(${attribute}, '${jsonPointerPath}') >= ${value}`

    case 'lt':
      // number less than
      return `jsonPointer(${attribute}, '${jsonPointerPath}') < ${value}`

    case 'lte':
      // number less than or equal
      return `jsonPointer(${attribute}, '${jsonPointerPath}') <= ${value}`

    case 'isnull':
    case 'isempty':
      // is null or empty check
      return `(jsonPointer(${attribute}, '${jsonPointerPath}') IS NULL OR jsonPointer(${attribute}, '${jsonPointerPath}') = '')`

    case 'range': {
      // range check (handles both array and stringified array "[min, max]")
      let rangeValues: number[] = []

      if (Array.isArray(value) && value.length === 2) {
        rangeValues = value
      } else if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value)
          if (Array.isArray(parsed) && parsed.length === 2) {
            rangeValues = parsed
          }
        } catch {
          // Invalid JSON string, fallback to exact match
          return `jsonPointer(${attribute}, '${jsonPointerPath}') = '${value.replace(/'/g, "''")}'`
        }
      }

      if (rangeValues.length === 2) {
        return `jsonPointer(${attribute}, '${jsonPointerPath}') >= ${rangeValues[0]} AND jsonPointer(${attribute}, '${jsonPointerPath}') <= ${rangeValues[1]}`
      }
      return `jsonPointer(${attribute}, '${jsonPointerPath}') = ${value}`
    }

    case 'date__gt':
      return `jsonPointer(${attribute}, '${jsonPointerPath}') > '${value}'`

    case 'date__lt':
      return `jsonPointer(${attribute}, '${jsonPointerPath}') < '${value}'`

    case 'date__gte':
      return `jsonPointer(${attribute}, '${jsonPointerPath}') >= '${value}'`

    case 'date__lte':
      return `jsonPointer(${attribute}, '${jsonPointerPath}') <= '${value}'`

    default:
      // Fallback to exact match
      return `jsonPointer(${attribute}, '${jsonPointerPath}') = '${value.toString().replace(/'/g, "''")}'`
  }
}

/**
 * Build full CQL expression from multiple cqlObjects
 */
export function build_cql_full_expression(expressions: cqlObject[]): string {
  return expressions
    .map((expn) => build_cql_single_expression(expn))
    .join(' AND ')
}

/**
 * Convert FilterCondition to cqlObject
 * You'll need to provide attribute and jsonPointer mapping based on your data structure
 */
const convertFilterConditionToCqlObject = (
  condition: FilterCondition,
  attribute: string = 'data' // Default attribute, you can customize this
): cqlObject => {
  const clause = OPERATOR_TO_CLAUSE_MAP[condition.operator] || 'exact'

  return {
    attribute,
    jsonPointer: condition.field, // Assuming field contains the JSON path
    clause,
    value: condition.value,
  }
}

/**
 * Process a single filter condition to CQL using jsonPointer format
 */
const processCQLCondition = (
  condition: FilterCondition,
  attribute: string = 'data'
): string => {
  const cqlObj = convertFilterConditionToCqlObject(condition, attribute)
  const expression = build_cql_single_expression(cqlObj)

  // Add NOT prefix if the condition should be negated
  if (condition.isNot) {
    return `NOT (${expression})`
  }

  return expression
}

/**
 * Converts a filter condition to CQL syntax using jsonPointer
 */
export const toCQL = (
  condition: FilterCondition,
  attribute: string = 'data'
): string => {
  return processCQLCondition(condition, attribute)
}

/**
 * Converts a filter group to CQL syntax using jsonPointer format
 */
export const filterGroupToCQL = (
  group: FilterGroup,
  attribute: string = 'data'
): string => {
  const parts: string[] = []

  if (group.conditions?.length) {
    parts.push(
      group.conditions
        .map((condition) => processCQLCondition(condition, attribute))
        .join(` ${group.operator} `)
    )
  }

  if (group.groups?.length) {
    parts.push(
      group.groups
        .map((g) => `(${filterGroupToCQL(g, attribute)})`)
        .join(` ${group.operator} `)
    )
  }

  return parts.join(` ${group.operator} `)
}
