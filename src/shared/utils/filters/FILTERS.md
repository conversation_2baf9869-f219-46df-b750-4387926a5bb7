# Filter Utilities

Simple examples for using filter utilities with GraphQL and CQL.

## Basic Usage

```typescript
import { FilterGroup, filterGroupToGraphQL, filterGroupToCQL } from '@shared/utils/filters';

// Create a filter
const filter: FilterGroup = {
  operator: 'AND',
  conditions: [
    {
      field: 'name',
      operator: 'contains',
      value: 'test',
      dataType: 'string'
    }
  ]
};

// For GraphQL queries
const gqlFilters = filterGroupToGraphQL(filter);
useQuery(GET_DATASETS, {
  variables: { filters: gqlFilters }
});

// For GeoServer/CQL
const cqlFilter = filterGroupToCQL(filter);
const url = `${GEOSERVER_URL}?CQL_FILTER=${encodeURIComponent(cqlFilter)}`;
```

## Supported Operators

### String Fields
```typescript
// Exact match
{ field: 'name', operator: 'exact', value: 'test', dataType: 'string' }

// Case-insensitive exact match
{ field: 'name', operator: 'iexact', value: 'test', dataType: 'string' }

// Contains
{ field: 'name', operator: 'contains', value: 'test', dataType: 'string' }

// Case-insensitive contains
{ field: 'name', operator: 'icontains', value: 'test', dataType: 'string' }

// Starts with
{ field: 'name', operator: 'startswith', value: 'test', dataType: 'string' }

// Ends with
{ field: 'name', operator: 'endswith', value: 'test', dataType: 'string' }

// Null check
{ field: 'description', operator: 'isnull', value: true, dataType: 'string' }
```

### Number Fields
```typescript
// Exact match
{ field: 'age', operator: 'exact', value: 18, dataType: 'number' }

// Comparison
{ field: 'age', operator: 'gt', value: 18, dataType: 'number' }
{ field: 'price', operator: 'lte', value: 100, dataType: 'number' }

// Range
{ field: 'amount', operator: 'range', value: [100, 500], dataType: 'number' }

// Null check
{ field: 'amount', operator: 'isnull', value: true, dataType: 'number' }
```

### Date Fields
```typescript
// Exact match
{ field: 'created_at', operator: 'exact', value: '2024-01-01', dataType: 'date' }

// Date comparison
{ field: 'created_at', operator: 'date__gt', value: '2024-01-01', dataType: 'date' }
{ field: 'created_at', operator: 'date__lt', value: '2024-12-31', dataType: 'date' }

// Date range
{ field: 'event_date', operator: 'range', value: ['2024-01-01', '2024-12-31'], dataType: 'date' }

// Null check
{ field: 'created_at', operator: 'isnull', value: true, dataType: 'date' }
```

### Time Fields
```typescript
// Exact match
{ field: 'updated_at', operator: 'exact', value: '14:00:00', dataType: 'time' }

// Time comparison
{ field: 'updated_at', operator: 'gt', value: '14:00:00', dataType: 'time' }
{ field: 'updated_at', operator: 'lt', value: '18:00:00', dataType: 'time' }

// Time range
{ field: 'work_hours', operator: 'range', value: ['09:00:00', '17:00:00'], dataType: 'time' }

// Null check
{ field: 'updated_at', operator: 'isnull', value: true, dataType: 'time' }
```

### Boolean Fields
```typescript
// Exact match
{ field: 'is_active', operator: 'exact', value: true, dataType: 'boolean' }

// Null check
{ field: 'is_active', operator: 'isnull', value: true, dataType: 'boolean' }
```

## Available Operators by Data Type

### String
- `exact` - Exact match
- `iexact` - Case-insensitive exact match
- `contains` - Contains substring
- `icontains` - Case-insensitive contains
- `startswith` - Starts with prefix
- `endswith` - Ends with suffix
- `isnull` - Is null check

### Number
- `exact` - Exact match
- `gt` - Greater than
- `lt` - Less than
- `gte` - Greater than or equal
- `lte` - Less than or equal
- `range` - Between two values
- `isnull` - Is null check

### Date
- `exact` - Exact date match
- `date__gt` - After date
- `date__lt` - Before date
- `date__gte` - On or after date
- `date__lte` - On or before date
- `range` - Between two dates
- `isnull` - Is null check

### Time
- `exact` - Exact time match
- `gt` - After time
- `lt` - Before time
- `gte` - At or after time
- `lte` - At or before time
- `range` - Between two times
- `isnull` - Is null check

### Boolean
- `exact` - Exact match
- `isnull` - Is null check 