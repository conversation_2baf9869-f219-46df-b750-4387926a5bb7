import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { filterGroupToCQL } from './cql'
import { filterGroupToGraphQL, DjangoFilterInput } from './gql'
import { FilterGroup } from './types'

/**
 * Interface for the complete filter structure expected by the mutation
 */
export interface LayerFiltersInput {
  cql_filter: string
  graphql_filters: DjangoFilterInput[]
}

/**
 * Converts a single FilterCondition to multiple conditions if value is an array
 * This handles OR logic for array values - ONLY used for CQL filters
 */
const expandFilterConditionForCQL = (
  filter: FilterCondition
): FilterCondition[] => {
  if (Array.isArray(filter.value)) {
    return filter.value.map((val, index) => ({
      ...filter,
      id: `${filter.id}_${index}`, // Ensure unique IDs for expanded conditions
      value: val,
    }))
  }
  return [filter]
}

/**
 * Builds CQL and GraphQL filters from FilterCondition array
 * - For CQL: Array values in a single filter are treated as OR conditions
 * - For GraphQL: Array values are kept as single filters (not expanded)
 * - Multiple filters are treated as AND conditions
 */
export const buildLayerFilters = (
  filters: FilterCondition[],
  attribute: string = 'data'
): LayerFiltersInput => {
  if (!filters || filters.length === 0) {
    return {
      cql_filter: '',
      graphql_filters: [],
    }
  }

  // Filter out incomplete filters (not applied or missing required fields)
  const appliedFilters = filters.filter(
    (f) =>
      f.applied &&
      f.field &&
      f.operator &&
      f.value !== null &&
      f.value !== undefined
  )

  if (appliedFilters.length === 0) {
    return {
      cql_filter: '',
      graphql_filters: [],
    }
  }

  // === Build CQL Filters (with array expansion) ===
  const cqlFilterGroups: any[] = appliedFilters.map((filter) => {
    const expandedConditions = expandFilterConditionForCQL(filter)

    if (expandedConditions.length === 1) {
      // Single condition
      return {
        operator: 'AND',
        conditions: expandedConditions.map((cond) => ({
          field: cond.field,
          operator: cond.operator,
          value: cond.value,
          dataType: cond.dataType,
          id: cond.id,
          applied: cond.applied,
          isVisible: cond.isVisible,
          isNot: cond.isNot,
        })),
      }
    } else {
      // Multiple conditions from array value - OR them together
      return {
        operator: 'OR',
        conditions: expandedConditions.map((cond) => ({
          field: cond.field,
          operator: cond.operator,
          value: cond.value,
          dataType: cond.dataType,
          id: cond.id,
          applied: cond.applied,
          isVisible: cond.isVisible,
          isNot: cond.isNot,
        })),
      }
    }
  })

  // Combine all CQL filter groups with AND
  const cqlRootFilterGroup: FilterGroup = {
    operator: 'AND',
    groups: cqlFilterGroups,
  }

  // === Build GraphQL Filters (without array expansion) ===
  const graphqlFilterGroups: any[] = appliedFilters.map((filter) => {
    // Keep the original filter without expanding arrays
    return {
      operator: 'AND',
      conditions: [
        {
          field: filter.field,
          operator: filter.operator,
          value: filter.value, // Keep array values intact
          dataType: filter.dataType,
          id: filter.id,
          applied: filter.applied,
          isVisible: filter.isVisible,
          isNot: filter.isNot,
        },
      ],
    }
  })

  // Combine all GraphQL filter groups with AND
  const graphqlRootFilterGroup: FilterGroup = {
    operator: 'AND',
    groups: graphqlFilterGroups,
  }

  // Build CQL filter (with expanded arrays)
  const cqlFilter = filterGroupToCQL(cqlRootFilterGroup, attribute)

  // Build GraphQL filters (with intact arrays)
  const graphqlFilters = filterGroupToGraphQL(graphqlRootFilterGroup)

  return {
    cql_filter: cqlFilter,
    graphql_filters: graphqlFilters,
  }
}

/**
 * Converts LayerFiltersInput to JSON string for mutation
 */
export const serializeLayerFilters = (filters: LayerFiltersInput): string => {
  return JSON.stringify(filters)
}

/**
 * Builds filters specifically for WMS/GeoServer requests
 */
export const buildWMSFilters = (filters: FilterCondition[]): string => {
  const layerFilters = buildLayerFilters(filters)
  return layerFilters.cql_filter
}

/**
 * Builds filters specifically for GraphQL mutations with full FilterCondition data
 */
export const buildGraphQLFilters = (
  filters: FilterCondition[]
): DjangoFilterInput[] => {
  const layerFilters = buildLayerFilters(filters)
  return layerFilters.graphql_filters
}
