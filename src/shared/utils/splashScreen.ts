export const showSplashScreen = (): HTMLIFrameElement => {
  // Check if splash screen already exists
  const existing = document.getElementById('splash-screen')
  if (existing) return existing as HTMLIFrameElement

  const iframe = document.createElement('iframe')
  iframe.id = 'splash-screen'
  iframe.src = '/splash-screen.html'
  iframe.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    z-index: 9999;
  `
  document.body.appendChild(iframe)
  return iframe
}

export const hideSplashScreen = (iframe: HTMLIFrameElement | null): void => {
  if (!iframe) return

  iframe.style.opacity = '0'
  setTimeout(() => {
    if (iframe.parentNode) {
      iframe.parentNode.removeChild(iframe)
    }
  }, 500)
}
