import i18next from 'i18next'

export interface FilterOperation {
  value: string
  label: string
  isNot?: boolean
}

export const getOperationsForFieldType = (
  fieldType: string
): FilterOperation[] => {
  const operations = {
    string: [
      {
        value: 'iexact',
        label: i18next.t('layerSettings.filters.filterOperators.equals'),
      },
      {
        value: 'iexact',
        label: i18next.t('layerSettings.filters.filterOperators.notEquals'),
        isNot: true,
      },
      {
        value: 'icontains',
        label: i18next.t('layerSettings.filters.filterOperators.contains'),
      },
      {
        value: 'icontains',
        label: i18next.t('layerSettings.filters.filterOperators.notContains'),
        isNot: true,
      },
      {
        value: 'isempty',
        label: i18next.t('layerSettings.filters.filterOperators.isNull'),
      },
      {
        value: 'isempty',
        label: i18next.t('layerSettings.filters.filterOperators.notEmpty'),
        isNot: true,
      },
    ],
    number: [
      {
        value: 'iexact',
        label: i18next.t('layerSettings.filters.filterOperators.equals'),
      },
      {
        value: 'gt',
        label: i18next.t('layerSettings.filters.filterOperators.greaterThan'),
      },
      {
        value: 'lt',
        label: i18next.t('layerSettings.filters.filterOperators.lessThan'),
      },
      {
        value: 'gte',
        label: i18next.t(
          'layerSettings.filters.filterOperators.greaterThanOrEqual'
        ),
      },
      {
        value: 'lte',
        label: i18next.t(
          'layerSettings.filters.filterOperators.lessThanOrEqual'
        ),
      },
      {
        value: 'range',
        label: i18next.t('layerSettings.filters.filterOperators.range'),
      },
      {
        value: 'range',
        label: i18next.t('layerSettings.filters.filterOperators.notBetween'),
        isNot: true,
      },
    ],
    integer: [
      {
        value: 'iexact',
        label: i18next.t('layerSettings.filters.filterOperators.equals'),
      },
      {
        value: 'gt',
        label: i18next.t('layerSettings.filters.filterOperators.greaterThan'),
      },
      {
        value: 'lt',
        label: i18next.t('layerSettings.filters.filterOperators.lessThan'),
      },
      {
        value: 'gte',
        label: i18next.t(
          'layerSettings.filters.filterOperators.greaterThanOrEqual'
        ),
      },
      {
        value: 'lte',
        label: i18next.t(
          'layerSettings.filters.filterOperators.lessThanOrEqual'
        ),
      },
      {
        value: 'range',
        label: i18next.t('layerSettings.filters.filterOperators.range'),
      },
      {
        value: 'range',
        label: i18next.t('layerSettings.filters.filterOperators.notBetween'),
        isNot: true,
      },
    ],
    date: [
      {
        value: 'exact',
        label: i18next.t('layerSettings.filters.filterOperators.equals'),
      },
      {
        value: 'date__gt',
        label: i18next.t('layerSettings.filters.filterOperators.after'),
      },
      {
        value: 'date__lt',
        label: i18next.t('layerSettings.filters.filterOperators.before'),
      },
      {
        value: 'date__gte',
        label: i18next.t('layerSettings.filters.filterOperators.afterOrOn'),
      },
      {
        value: 'date__lte',
        label: i18next.t('layerSettings.filters.filterOperators.beforeOrOn'),
      },
      {
        value: 'range',
        label: i18next.t('layerSettings.filters.filterOperators.range'),
      },
    ],
    boolean: [
      {
        value: 'iexact',
        label: i18next.t('layerSettings.filters.filterOperators.equals'),
      },
      // {
      //   value: 'isnull',
      //   label: i18next.t('layerSettings.filters.filterOperators.isNull'),
      // },
    ],
  }

  return operations[fieldType as keyof typeof operations] || operations.string
}

export const OPERATOR_MAP: Record<string, string> = {
  exact: '=',
  iexact: 'ILIKE',
  contains: 'LIKE',
  icontains: 'ILIKE',
  startswith: 'LIKE',
  endswith: 'LIKE',
  gt: '>',
  lt: '<',
  gte: '>=',
  lte: '<=',
  range: 'BETWEEN',
  date__gt: '>',
  date__lt: '<',
  date__gte: '>=',
  date__lte: '<=',
  isnull: 'IS NULL',
  isempty: 'IS EMPTY',
}
