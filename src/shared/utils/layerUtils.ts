import { Sld, SldType } from '@/shared/store/slices/mapSlice'

/**
 * Gets the color from an array of SLDs by type
 * @param slds The array of SLDs
 * @param sldType The type of SLD to get the color from
 * @param defaultColor Fallback color if no matching SLD is found
 * @returns The color string
 */
export const getColorBySldType = (
  slds: Sld[] | undefined,
  sldType: SldType = SldType.POINTS,
  defaultColor: string = '#000000'
): string => {
  if (!slds || slds.length === 0) {
    return defaultColor
  }

  const targetSld = slds.find((sld) => sld.sldType === sldType)
  return targetSld?.featureStyle?.color || defaultColor
}

/**
 * Gets the opacity from an array of SLDs by type
 * @param slds The array of SLDs
 * @param sldType The type of SLD to get the opacity from
 * @param defaultOpacity Fallback opacity if no matching SLD is found
 * @returns The opacity value
 */
export const getOpacityBySldType = (
  slds: Sld[] | undefined,
  sldType: SldType = SldType.POINTS,
  defaultOpacity: number = 1
): number => {
  if (!slds || slds.length === 0) {
    return defaultOpacity
  }

  const targetSld = slds.find((sld) => sld.sldType === sldType)
  return targetSld?.featureStyle?.opacity ?? defaultOpacity
}

/**
 * Gets the entire feature style from an array of SLDs by type
 * @param slds The array of SLDs
 * @param sldType The type of SLD to get the feature style from
 * @returns The feature style object or undefined if not found
 */
export const getFeatureStyleBySldType = (
  slds: Sld[] | undefined,
  sldType: SldType = SldType.POINTS
) => {
  if (!slds || slds.length === 0) {
    return undefined
  }

  const targetSld = slds.find((sld) => sld.sldType === sldType)
  return targetSld?.featureStyle
}

/**
 * Gets an SLD by type from an array of SLDs
 * @param slds The array of SLDs
 * @param sldType The type of SLD to get
 * @returns The SLD object or undefined if not found
 */
export const getSldByType = (
  slds: Sld[] | undefined,
  sldType: SldType = SldType.POINTS
): Sld | undefined => {
  if (!slds || slds.length === 0) {
    return undefined
  }

  return slds.find((sld) => sld.sldType === sldType)
}
