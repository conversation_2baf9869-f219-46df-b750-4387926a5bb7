import { store } from '@/shared/store'
import { updateLayersVisibility as updateLayersVisibilityAction } from '@/shared/store/slices/mapSlice'

type MapManagerAction = () => void

export const updateLayersVisibility = (
  mapManagerAction: MapManagerAction,
  targetLayerKey?: string,
  showAll?: boolean,
  onComplete?: () => void
) => {
  mapManagerAction()

  store.dispatch(updateLayersVisibilityAction({ targetLayerKey, showAll }))

  if (onComplete) {
    onComplete()
  }
}
