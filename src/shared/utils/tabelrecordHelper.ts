import { cleanStringData } from './recordHelpers'

export const isImageUrl = (value: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  try {
    const url = new URL(value)
    return imageExtensions.some((ext) =>
      url.pathname.toLowerCase().endsWith(ext)
    )
  } catch {
    return false
  }
}

export const isImageField = (
  fieldKey: string,
  keywords: string[] = ['image', 'img']
): boolean => {
  return keywords.some((keyword) =>
    fieldKey.toLowerCase().includes(keyword.toLowerCase())
  )
}

export const cleanAndParseJson = (value: string): object | null => {
  try {
    const cleanedData = cleanStringData(value)
    return JSON.parse(cleanedData)
  } catch {
    return null
  }
}

// Helper function to check if a string is likely an image URL
export const isImageUri = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false

  // Check for common image extensions
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i

  // Check for data URLs
  const isDataUrl = url.startsWith('data:image/')

  // Check for URLs with image extensions or data URLs
  return imageExtensions.test(url) || isDataUrl || url.includes('image')
}
