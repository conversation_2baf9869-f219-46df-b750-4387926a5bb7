/**
 * Formats a number to a specified number of decimal places
 * to avoid floating point precision issues
 *
 * @param value The number to format
 * @param decimals The number of decimal places (default: 0)
 * @returns Formatted number as a string
 */
export const formatNumber = (value: number, decimals: number = 0): string => {
  return value.toFixed(decimals)
}

/**
 * Rounds a number to a specified number of decimal places
 * to avoid floating point precision issues
 *
 * @param value The number to round
 * @param decimals The number of decimal places (default: 0)
 * @returns Rounded number
 */
export const roundNumber = (value: number, decimals: number = 0): number => {
  const multiplier = Math.pow(10, decimals)
  return Math.round(value * multiplier) / multiplier
}
