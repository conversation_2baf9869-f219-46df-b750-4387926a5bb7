import i18n from '@/i18n'
import { toast, ToastOptions } from 'react-toastify'

const defaultToastConfig: ToastOptions = {
  position: 'top-center',
  autoClose: 2000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  rtl: ['ar'].includes(i18n.language),
}

export const showToast = {
  error: (message: string) => toast.error(message, defaultToastConfig),
  success: (message: string) => toast.success(message, defaultToastConfig),
  info: (message: string) => toast.info(message, defaultToastConfig),
  warning: (message: string) => toast.warning(message, defaultToastConfig),
}
