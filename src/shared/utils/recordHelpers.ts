export type FieldType =
  | 'text'
  | 'integer'
  | 'float'
  | 'boolean'
  | 'date'
  | 'enum'

export interface JsonSchemaProperty {
  type: string | string[]
  title?: string
  format?: string
  enum?: string[]
  items?: {
    type: string
  }
  $ref?: string
}

export interface JsonSchema {
  properties: Record<string, JsonSchemaProperty>
  required?: string[]
  definitions?: Record<string, { enum?: string[] }>
}

interface FormattedField {
  key: string
  label: string
  type: FieldType
  required: boolean
  enumValues: string[]
  isArray: boolean
}

interface GraphQLError {
  message?: string
  extensions?: {
    http?: {
      reason?: {
        formData?: string[]
        layerId?: string
        jsonSchema?: string[]
        message?: string
      } | string
    }
  }
}

interface GraphQLErrorResponse {
  graphQLErrors?: GraphQLError[]
}

interface RecordData {
  records?: {
    data?: Array<string | { data?: string | Record<string, unknown> } | Record<string, unknown>>
  }
}

// Helper function to clean and parse string data
export const cleanStringData = (data: string): string => {
  return data
    .replace(/None/g, 'null')
    .replace(/True/g, 'true')
    .replace(/False/g, 'false')
    .replace(/NaN/g, 'null')
    .replace(/'/g, '"')
    .replace(/"undefined"/g, 'null')
    .replace(/"(\d+(\.\d+)?)"/g, '$1')
    .replace(/"null"/g, 'null')
    .trim()
}

// Format fields for modal
export const formatFieldsForModal = (schemaResponse: JsonSchema | null): FormattedField[] => {
  const jsonSchema = schemaResponse
  if (!jsonSchema?.properties) return []

  return Object.entries(jsonSchema.properties)
    .filter(([, value]) => {
      const types = Array.isArray(value.type) ? value.type : [value.type]
      return !(types.length === 1 && types[0] === 'null')
    })
    .map(([key, value]) => {
      let type: FieldType = 'text'
      let enumValues: string[] = []
      let isArray = false

      const types = Array.isArray(value.type) ? value.type : [value.type]
      // Get first non-null type
      const primaryType = types.find(t => t !== 'null') || 'text'

      if (value.type === 'array') {
        isArray = true
        if (value.items?.type === 'string') {
          type = 'text'
        } else if (value.items?.type === 'integer') {
          type = 'integer'
        } else if (value.items?.type === 'number') {
          type = 'float'
        }
      } else if (value.$ref) {
        const definitionKey = value.$ref.split('/').pop() || ''
        const definition = jsonSchema.definitions?.[definitionKey]
        if (definition?.enum) {
          type = 'enum'
          enumValues = definition.enum
        }
      } else {
        switch (primaryType) {
          case 'integer':
            type = 'integer'
            break
          case 'number':
            type = 'float'
            break
          case 'boolean':
            type = 'boolean'
            break
          case 'string':
            if (value.format === 'date') {
              type = 'date'
            } else if (value.enum) {
              type = 'enum'
              enumValues = value.enum
            } else {
              type = 'text'
            }
            break
        }
      }

      return {
        key,
        label: value.title || key,
        type,
        required: jsonSchema.required?.includes(key) || false,
        enumValues,
        isArray,
      }
    })
}

// Format initial data
export const formatInitialData = (recordDetails: RecordData | null): unknown => {
  if (!recordDetails?.records?.data?.length) return {}

  const initialData = recordDetails.records.data[0]

  // Parse string data if it's a string
  if (typeof initialData === 'string') {
    try {
      const cleanedData = cleanStringData(initialData)
      return JSON.parse(cleanedData)
    } catch {
      return initialData
    }
  }

  // Handle object with data property
  const dataObj = initialData as { data?: string | Record<string, unknown> }
  if (dataObj.data && typeof dataObj.data === 'string') {
    try {
      const cleanedData = cleanStringData(dataObj.data)
      return JSON.parse(cleanedData)
    } catch {
      return dataObj.data
    }
  }

  // Return the data property if available
  if (dataObj.data) {
    return dataObj.data
  }

  // Return original data
  return initialData
}

// Handle GraphQL error
export const handleGraphQLError = (error: GraphQLErrorResponse): string => {
  if (!error.graphQLErrors?.length) return 'حدث خطأ أثناء تحديث السجل'

  const firstError = error.graphQLErrors[0]
  const reason = firstError.extensions?.http?.reason

  // Handle form data errors
  if (typeof reason === 'object' && reason.formData && Array.isArray(reason.formData)) {
    return reason.formData.join('\n')
  }

  // Handle layerId or direct reason message
  if (typeof reason === 'object' && reason.layerId) {
    return reason.layerId
  }
  if (typeof reason === 'string') {
    return reason
  }

  // Handle validation errors
  if (typeof reason === 'object' && reason.jsonSchema && Array.isArray(reason.jsonSchema)) {
    return reason.jsonSchema[0]
  }

  // Fallback to generic error message or default
  return firstError.message || 'حدث خطأ أثناء تحديث السجل'
}

// Process field value based on type
export const processFieldValue = (
  value: string,
  fieldType: FieldType
): string | number | boolean | null => {
  if (value === '') return null

  switch (fieldType) {
    case 'integer':
      return value ? parseInt(value, 10) : null
    case 'float':
      return value ? parseFloat(value) : null
    case 'boolean':
      if (typeof value === 'boolean') return value
      return value === 'true' || value === '1' || value === 'yes'
    case 'date':
      return value || null
    case 'text':
      return value
    default:
      return convertValueToString(value)
  }
}

interface MapDataEntry {
  key: string
  value: string
}

const processNestedData = (data: Record<string, unknown>): MapDataEntry[] => {
  return Object.entries(data)
    .filter(([, value]) => value !== null)
    .slice(0, 4)
    .map(([key, value]) => ({
      key,
      value: convertValueToString(value),
    }))
}

const convertValueToString = (value: unknown): string => {
  if (value === null || value === undefined) {
    return ''
  }
  if (value instanceof Date) {
    return value.toISOString()
  }
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value) || ''
    } catch {
      return ''
    }
  }
  if (typeof value === 'string') {
    return value
  }
  if (typeof value === 'number' || typeof value === 'boolean') {
    return value.toString()
  }
  return ''
}

interface ParsedData {
  parsedData?: {
    data?: Record<string, unknown>
  }
  data?: string | Record<string, unknown>
}

// Format map data from string or object
export const formatMapData = (mapData: string): MapDataEntry[] => {
  try {
    const parsedData = JSON.parse(mapData) as ParsedData

    // Handle different possible data formats (object, nested object, stringified JSON)
    if (parsedData.parsedData?.data && typeof parsedData.parsedData.data === 'object') {
      return processNestedData(parsedData.parsedData.data)
    }

    if (parsedData.data) {
      if (typeof parsedData.data === 'string') {
        const cleanedData = cleanStringData(parsedData.data)
        const nestedData = JSON.parse(cleanedData) as Record<string, unknown>
        return processNestedData(nestedData)
      }
      if (typeof parsedData.data === 'object') {
        return processNestedData(parsedData.data)
      }
    }

    return Object.entries(parsedData as Record<string, unknown>)
      .filter(([, value]) => value !== null)
      .slice(0, 4)
      .map(([key, value]) => ({
        key,
        value: convertValueToString(value),
      }))
  } catch (error) {
    console.warn('Using fallback parsing for map data', error)
    try {
      const cleanedData = cleanStringData(mapData)
      const fallbackData = JSON.parse(cleanedData) as Record<string, unknown>
      return processNestedData(fallbackData)
    } catch {
      return []
    }
  }
}

interface Position {
  left: string
  top: string
}

export const getPosition = (x: number, y: number): Position => {
  const rightSpace = window.innerWidth - x
  const bottomSpace = window.innerHeight - y

  return {
    left: rightSpace < 320 ? `${String(x - 320)}px` : `${String(x + 2)}px`,
    top: bottomSpace < 400 ? `${String(y - 320)}px` : `${String(y + 65)}px`,
  }
}
