export type FieldType =
  | 'text'
  | 'integer'
  | 'float'
  | 'boolean'
  | 'date'
  | 'enum'

// Helper function to clean and parse string data
export const cleanStringData = (data: string): string => {
  return data
    .replace(/None/g, 'null')
    .replace(/'/g, '"')
    .replace(/True/g, 'true')
    .replace(/False/g, 'false')
    .replace(/"null"/g, 'null')
    .replace(/"undefined"/g, 'null')
    .replace(/NaN/g, 'null')
    .replace(/"(\d+(\.\d+)?)"/g, '$1')
    .trim()
}

// Format fields for modal
export const formatFieldsForModal = (schemaResponse: any) => {
  const jsonSchema = schemaResponse
  if (!jsonSchema?.properties) return []

  return Object.entries(jsonSchema.properties)
    .filter(([, value]: [string, any]) => {
      const types = Array.isArray(value.type) ? value.type : [value.type]
      return !(types.length === 1 && types[0] === 'null')
    })
    .map(([key, value]: [string, any]) => {
      let type: FieldType = 'text'
      let enumValues: string[] = []
      let isArray = false

      const types = Array.isArray(value.type) ? value.type : [value.type]
      // Get first non-null type
      const primaryType = types.find((t: any) => t !== 'null') || 'text'

      if (value.type === 'array') {
        isArray = true
        if (value.items?.type === 'string') {
          type = 'text'
        } else if (value.items?.type === 'integer') {
          type = 'integer'
        } else if (value.items?.type === 'number') {
          type = 'float'
        }
      } else if (value.$ref) {
        const definitionKey = value.$ref.split('/').pop()
        const definition = jsonSchema.definitions?.[definitionKey]
        if (definition?.enum) {
          type = 'enum'
          enumValues = definition.enum
        }
      } else {
        switch (primaryType) {
          case 'integer':
            type = 'integer'
            break
          case 'number':
            type = 'float'
            break
          case 'boolean':
            type = 'boolean'
            break
          case 'string':
            if (value.format === 'date') {
              type = 'date'
            } else if (value.enum) {
              type = 'enum'
              enumValues = value.enum
            } else {
              type = 'text'
            }
            break
        }
      }

      return {
        key,
        label: value.title || key,
        type,
        required: jsonSchema.required?.includes(key) || false,
        enumValues,
        isArray,
      }
    })
}

// Format initial data
export const formatInitialData = (recordDetails: any) => {
  const initialData = recordDetails?.records?.data?.[0]

  if (!initialData) return {}

  // Parse string data if it's a string
  if (typeof initialData === 'string') {
    try {
      const cleanedData = cleanStringData(initialData)
      return JSON.parse(cleanedData)
    } catch {
      return initialData
    }
  }

  // Handle object or nested data property
  if (initialData?.data && typeof initialData.data === 'string') {
    try {
      const cleanedData = cleanStringData(initialData.data)
      return JSON.parse(cleanedData)
    } catch {
      return initialData.data
    }
  }

  // Return the data property if available
  if (initialData?.data) {
    return initialData.data
  }

  // Return original data
  return initialData
}

// Handle GraphQL error
export const handleGraphQLError = (error: any) => {
  const formDataErrors =
    error.graphQLErrors?.[0]?.extensions?.http?.reason?.formData
  if (formDataErrors && Array.isArray(formDataErrors)) {
    return formDataErrors.join('\n')
  }
  const reasonMessage =
    error.graphQLErrors?.[0]?.extensions?.http?.reason?.layerId ||
    error.graphQLErrors?.[0]?.extensions?.http?.reason
  const validationError =
    error.graphQLErrors?.[0]?.extensions?.http?.reason?.jsonSchema?.[0]
  const errorMessage =
    reasonMessage ||
    validationError ||
    error.graphQLErrors?.[0]?.message ||
    'حدث خطأ أثناء تحديث السجل'

  console.error('Error updating record:', error)
  return errorMessage
}

// Process field value based on type
export const processFieldValue = (
  value: string,
  fieldType: FieldType
): string | number | boolean | null => {
  if (value === '') return null

  switch (fieldType) {
    case 'integer':
      return value ? parseInt(value, 10) : null
    case 'float':
      return value ? parseFloat(value) : null
    case 'boolean':
      if (typeof value === 'boolean') return value
      return value === 'true' || value === '1' || value === 'yes'
    case 'date':
      return value || null
    case 'text':
      return String(value)
    default:
      return value
  }
}

const processNestedData = (data: any) => {
  return Object.entries(data)
    .filter(([, value]) => value !== null)
    .slice(0, 4)
    .map(([key, value]) => ({
      key,
      value: String(value),
    }))
}

const convertValueToString = (value: any): string => {
  if (value === null || value === undefined) {
    return ''
  }
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

// Format map data from string or object
export const formatMapData = (mapData: string) => {
  try {
    const parsedData = JSON.parse(mapData)

    // Handle different possible data formats (object, nested object, stringified JSON)
    if (
      parsedData?.parsedData?.data &&
      typeof parsedData.parsedData.data === 'object'
    ) {
      return processNestedData(parsedData.parsedData.data)
    }

    if (parsedData?.data && typeof parsedData.data === 'string') {
      const cleanedData = cleanStringData(parsedData.data)
      const nestedData = JSON.parse(cleanedData)
      return processNestedData(nestedData)
    }

    if (parsedData?.data && typeof parsedData.data === 'object') {
      return processNestedData(parsedData.data)
    }

    return Object.entries(parsedData)
      .filter(([, value]) => value !== null)
      .map(([key, value]) => {
        return {
          key: key,
          value: convertValueToString(value),
        }
      })
  } catch (error) {
    console.warn('Using fallback parsing for map data', error)
    try {
      const cleanedData = cleanStringData(mapData)
      const fallbackData = JSON.parse(cleanedData)
      return processNestedData(fallbackData)
    } catch {
      return []
    }
  }
}

export const getPosition = (x: number, y: number) => {
  const rightSpace = window.innerWidth - x
  const bottomSpace = window.innerHeight - y

  return {
    left: rightSpace < 320 ? `${x - 320}px` : `${x + 2}px`,
    top: bottomSpace < 400 ? `${y - 320}px` : `${y + 65}px`,
  }
}
