import { WorkspaceMethods } from './routes'

interface StepProgress {
  status: 'incomplete' | 'complete'
  formData: any | null
}

interface StepsProgress {
  [key: string]: StepProgress
}

export const StepProgressManager = {
  saveProgress: (
    step: number,
    formData: any,
    method: string = WorkspaceMethods.UPLOAD_FILE
  ) => {
    const progress: StepProgress = {
      status: 'complete',
      formData,
    }

    const currentProgress = localStorage.getItem(`stepsProgress_${method}`)
    const allProgress: StepsProgress = currentProgress
      ? JSON.parse(currentProgress)
      : {}

    allProgress[`step${step}`] = progress
    localStorage.setItem(`stepsProgress_${method}`, JSON.stringify(allProgress))
  },

  getProgress: (
    step: number,
    method: string = WorkspaceMethods.UPLOAD_FILE
  ): StepProgress => {
    const progress = localStorage.getItem(`stepsProgress_${method}`)
    if (!progress) return { status: 'incomplete', formData: null }

    try {
      const allProgress: StepsProgress = JSON.parse(progress)
      return (
        allProgress[`step${step}`] || { status: 'incomplete', formData: null }
      )
    } catch (error) {
      console.error('Error parsing progress:', error)
      return { status: 'incomplete', formData: null }
    }
  },

  clearProgress: (method: string = WorkspaceMethods.UPLOAD_FILE) => {
    localStorage.removeItem(`stepsProgress_${method}`)
  },
}
