import i18next from 'i18next'

export const formatNumber = (num: number) => {
  const locale = i18next.language === 'ar' ? 'ar-EG' : i18next.language
  const formatted = num.toLocaleString(locale)
  return formatted
}

export const formatDate = (date: string) => {
  const locale = i18next.language === 'ar' ? 'ar-EG' : 'en-US'
  return new Date(date).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

export const getRelativeTimeString = (date: string) => {
  const now = new Date()
  const visitDate = new Date(date)
  const diffInMinutes = Math.floor(
    (now.getTime() - visitDate.getTime()) / (1000 * 60)
  )

  if (diffInMinutes < 60) {
    return {
      count: diffInMinutes,
      unit: i18next.t('workspace.card.minutesAgo'),
    }
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return {
      count: diffInHours,
      unit: i18next.t('workspace.card.hoursAgo'),
    }
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return {
      count: diffInDays,
      unit: i18next.t('workspace.card.daysAgo'),
    }
  }

  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return {
      count: diffInMonths,
      unit: i18next.t('workspace.card.monthsAgo'),
    }
  }

  const diffInYears = Math.floor(diffInMonths / 12)
  return {
    count: diffInYears,
    unit: i18next.t('workspace.card.yearsAgo'),
  }
}

// Format item.since by extracting number and using formatNumber
export const getFormattedSince = (date: string) => {
  if (date) {
    // Extract number from text like "4 أيام"
    const numberMatch = date.match(/\d+/)
    if (numberMatch) {
      const number = parseInt(numberMatch[0])
      const formattedNumber = formatNumber(number)
      // Replace the original number with formatted number
      return date.replace(/\d+/, formattedNumber)
    }
  }
  return date || ''
}
