import { FilterCondition } from '@/shared/store/slices/mapSlice'
import { getOperationsForFieldType } from './filterOperations'

export interface AvailableField {
  name: string
  label: string
  type: string
}

export const getReadableFilterText = (
  filter: FilterCondition,
  availableFields: AvailableField[]
): string => {
  if (
    !filter.field ||
    !filter.operator ||
    filter.value === null ||
    filter.value === undefined
  ) {
    return ''
  }

  const field = availableFields.find((f) => f.name === filter.field)
  const fieldLabel = field?.label || filter.field

  const operations = getOperationsForFieldType(filter.dataType)
  const operation = operations.find((op) => 
    op.value === filter.operator && (op.isNot || false) === (filter.isNot || false)
  )
  const operationText = operation?.label || filter.operator

  let valueText = ''
  if (Array.isArray(filter.value)) {
    valueText = filter.value.join('، ')
  } else {
    valueText = String(filter.value)
  }

  return `${fieldLabel} ${operationText} ${valueText}`
}

export const formatFilterValue = (value: any): any => {
  if (value === null || value === undefined || value === '') {
    return null
  }
  return value
}

export const isFilterComplete = (filter: FilterCondition): boolean => {
  return !!(
    filter.field &&
    filter.operator &&
    filter.value !== null &&
    filter.value !== undefined
  )
}

export const formatSelectValue = (value: any) => {
  if (!value) return []

  if (Array.isArray(value)) {
    return value.map((v) => ({ value: v, label: v }))
  }

  return [{ value, label: value }]
}

export const extractSelectValues = (selected: any[]) => {
  if (!selected || selected.length === 0) return null
  return selected.map((item) => item.value)
}
