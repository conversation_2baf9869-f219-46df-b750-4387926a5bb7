// Interface for column data structure
interface ColumnData {
  column: string
  title?: string
  data: string[]
}

// Interface for column mapping
interface ColumnMapping {
  column: string
  title?: string
  displayName?: string
}

export const createColumnSamples = (samplesData: any) => {
  const samples: Record<string, string[]> = {}
  samplesData.datasetSampleData.data.forEach(
    (item: { column: string; data: string[] }) => {
      samples[item.column] = item.data
    }
  )
  return samples
}

// Create column mappings with display names (title if available, otherwise column)
export const createColumnMappings = (samplesData: any): ColumnMapping[] => {
  return samplesData.datasetSampleData.data.map((item: ColumnData) => ({
    column: item.column,
    title: item.title || item.column,
    displayName: item.title || item.column,
  }))
}

// Create enum options for form fields (showing titles but keeping column values)
export const createColumnEnumOptions = (columnMappings: ColumnMapping[]) => {
  return columnMappings.map((mapping) => ({
    value: mapping.column, // Keep column as value for backend
    label: mapping.displayName, // Show title/displayName to user
  }))
}

export const updateSchemaWithAvailableColumns = (
  baseSchema: any,
  currentFormData: any,
  columnMappings: ColumnMapping[],
  columnSamples: Record<string, string[]>,
  type: 'columns' | 'advanced'
) => {
  // Clear tracking of selected values when switching layouts
  const selectedValues =
    type === 'columns'
      ? (currentFormData.columnLayout === 'عمود'
          ? [currentFormData.latLngColumn]
          : [currentFormData.latColumn, currentFormData.lngColumn]
        ).filter(Boolean)
      : Object.values(currentFormData).filter(Boolean)

  const getAvailableColumnsForField = (fieldName: string) => {
    const currentValue = currentFormData[fieldName]
    // Only filter against values relevant to current layout
    const availableColumns = columnMappings.filter(
      (mapping) =>
        !selectedValues.includes(mapping.column) ||
        mapping.column === currentValue
    )
    return availableColumns.length === 0 ? columnMappings : availableColumns
  }

  if (type === 'columns') {
    return {
      ...baseSchema,
      dependencies: {
        ...baseSchema.dependencies,
        columnLayout: {
          oneOf: [
            {
              properties: {
                columnLayout: { const: 'عمود' },
                latLngColumn: {
                  type: 'string',
                  title: 'schema.stepThree.columns.latLng',
                  enum: getAvailableColumnsForField('latLngColumn'),
                  samples: columnSamples,
                },
              },
              required: ['latLngColumn'],
            },
            {
              properties: {
                columnLayout: { const: 'عمودين' },
                latColumn: {
                  type: 'string',
                  title: 'schema.stepThree.columns.lat',
                  enum: getAvailableColumnsForField('latColumn'),
                  samples: columnSamples,
                },
                lngColumn: {
                  type: 'string',
                  title: 'schema.stepThree.columns.lng',
                  enum: getAvailableColumnsForField('lngColumn'),
                  samples: columnSamples,
                },
              },
              required: ['latColumn', 'lngColumn'],
            },
          ],
        },
      },
    }
  }

  const properties = baseSchema.properties
  const updatedProperties = {} as any

  // Dynamically create properties based on baseSchema
  Object.keys(properties).forEach((key) => {
    const availableColumns = getAvailableColumnsForField(key)
    updatedProperties[key] = {
      ...properties[key],
      enum: availableColumns.map((m) => m.column),
      enumNames: availableColumns.map((m) => m.displayName),
      samples: columnSamples,
    }
  })
  return {
    ...baseSchema,
    properties: updatedProperties,
  }
}

export const labelToKeyMap = {
  'خط الطول': 'latitudeColumn',
  'خط العرض': 'longitudeColumn',
  'اختر خط الطول والعرض': 'latLngColumn',
}

export function transformData(data: any) {
  return Object.keys(data).map((key) => ({
    label: key,
    value: key,
  }))
}

// Get selected column names from form data
export const getSelectedColumnNames = (formData: any): string[] => {
  const selectedColumns = Object.values(formData).filter(Boolean) as string[]
  return selectedColumns // These are already column names, not display names
}
