import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useTranslation } from "react-i18next"

interface SearchInputProps {
    value: string
    onChange: (value: string) => void
    placeholder?: string
    className?: string
}

export const SearchInput = ({
    value,
    onChange,
    placeholder,
    className = "",
}: SearchInputProps) => {
    const { t } = useTranslation()

    return (
        <div className={`relative ${className}`}>
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
                className="pr-10 no-border-focus"
                placeholder={placeholder || t("search")}
                value={value}
                onChange={(e) => onChange(e.target.value)}
            />
        </div>
    )
}