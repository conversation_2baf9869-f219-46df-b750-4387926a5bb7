import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ChevronDown, ChevronUp, Loader2 } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"

interface Permission {
    id: string
    title?: string
    translated?: string
}

interface PermissionGroup {
    id: string
    title: string
    permissions: Permission[]
}

interface PermissionsSectionProps {
    groups: PermissionGroup[]
    selectedPermissions: Record<string, boolean>
    onPermissionChange: (permissions: Record<string, boolean>) => void
    loading?: boolean
}

export const PermissionsSection = ({
    groups,
    selectedPermissions,
    onPermissionChange,
    loading = false,
}: PermissionsSectionProps) => {
    const { t, i18n } = useTranslation()
    const direction = i18n.dir()
    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>(
        groups.reduce((acc, group) => ({ ...acc, [group.id]: false }), {})
    )

    const toggleSection = (sectionId: string) => {
        setExpandedSections((prev) => ({
            ...prev,
            [sectionId]: !prev[sectionId],
        }))
    }

    const togglePermission = (permissionId: string) => {
        const newPermissions = {
            ...selectedPermissions,
            [permissionId]: !selectedPermissions[permissionId],
        }
        onPermissionChange(newPermissions)
    }

    const toggleGroupPermissions = (permissions: Permission[]) => {
        const allSelected = areAllGroupPermissionsSelected(permissions)
        const newPermissions = { ...selectedPermissions }

        permissions.forEach((permission) => {
            newPermissions[permission.id] = !allSelected
        })

        onPermissionChange(newPermissions)
    }

    const areAllGroupPermissionsSelected = (permissions: Permission[]) => {
        return permissions.every((p) => selectedPermissions[p.id])
    }

    const areSomeGroupPermissionsSelected = (permissions: Permission[]) => {
        return (
            permissions.some((p) => selectedPermissions[p.id]) &&
            !permissions.every((p) => selectedPermissions[p.id])
        )
    }

    if (loading) {
        return (
            <div className="text-center py-4 flex items-center justify-center">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span>{t("loadingPermissions")}</span>
            </div>
        )
    }

    return (
        <div className="space-y-3">
            <Label>{t("systemPermissions")}</Label>
            {groups.map((group) => (
                <div key={group.id} className="border rounded-md overflow-hidden">
                    <div
                        className={`flex items-center justify-between p-3 bg-gray-50 cursor-pointer ${direction}`}
                        onClick={() => toggleSection(group.id)}
                    >
                        <div className="flex items-center gap-3">
                            <Checkbox
                                id={`group-${group.id}`}
                                checked={areAllGroupPermissionsSelected(group.permissions)}
                                onCheckedChange={() => toggleGroupPermissions(group.permissions)}
                                className={areSomeGroupPermissionsSelected(group.permissions) ? "bg-indigo-200" : ""}
                                onClick={(e) => e.stopPropagation()}
                            />
                            <Label
                                htmlFor={`group-${group.id}`}
                                className="text-sm font-medium cursor-pointer"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {group.title}
                            </Label>
                        </div>
                        {expandedSections[group.id] ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                    </div>

                    {expandedSections[group.id] && (
                        <div className="p-3 space-y-2 border-t">
                            {group.permissions.map((permission) => (
                                <div key={permission.id} className="flex items-center justify-between pr-6">
                                    <Label htmlFor={`permission-${permission.id}`} className="text-sm font-normal">
                                        {permission.translated || permission.title}
                                    </Label>
                                    <Checkbox
                                        id={`permission-${permission.id}`}
                                        checked={selectedPermissions[permission.id] || false}
                                        onCheckedChange={() => togglePermission(permission.id)}
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            ))}
        </div>
    )
}