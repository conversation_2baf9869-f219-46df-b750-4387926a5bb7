import { UserStatus } from './userStatus'

export interface User {
  id: string | number
  firstName: string
  lastName: string
  email: string
  phone: string
  role: Role
  activeStatus: string
}

export interface Role {
  id: string | number
  title: string
  permissions: string[]
  users?: number
  description?: string
  codename?: string
  usersCount?: number
}

export interface RoleModalProps {
  isOpen: boolean
  onClose: (shouldRefetch?: boolean) => void
  mode: 'create' | 'edit'
  role: Role | null
}

export interface RoleCardProps {
  role: Role
  onEdit: () => void
  onDelete: () => void
}

export interface Permission {
  id: string
  name: string
  codename: string
  translated?: string
}

export interface PermissionGroup {
  id: string
  model: string
  title: string
  permissions: Permission[]
}

export interface DeleteAlertProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  confirmButtonText?: string
  cancelButtonText?: string
  isLoading?: boolean
}

export interface MemberCardProps {
  userId: string
  name: string
  email: string
  phone: string
  roleId: string
  roleTitle?: string
  status: UserStatus
  roles: Role[]
}

export interface AddUserFormProps {
  orgId: number | string | null
  roles: Role[]
  onClose?: () => void
}

export interface Workspace {
  id: string
  name: string
  description?: string
  workspaceType?: string
}

interface WorkspacePermission {
  id: string
  title: string
}

export interface AddWorkspaceUserDialogProps {
  isOpen: boolean
  onAddUsers: (data: { userIds: string[]; permissionIds: string[] }) => void
  workspace?: Workspace | null
}

export interface WorkspaceMember extends User {
  workspacePermissions: WorkspacePermission[]
}

export interface WorkspaceItemProps {
  workspace: Workspace
  onClick: () => void
  isActive?: boolean
}

export interface OrganizationSettingsState {
  // Common
  activeTab: string
  orgId: string | null | number

  // Roles
  roles: Role[]
  currentRole: Role | null
  isRoleModalOpen: boolean
  roleModalMode: 'create' | 'edit'

  // Team Members
  users: User[]
  totalUsers: number
  userSearchTerm: string
  userCurrentPage: number
  usersPerPage: number
  isAddUserDialogOpen: boolean

  // Workspace Permissions
  selectedWorkspace: Workspace | null
  workspaceMembers: User[]
  workspacePermissions: Permission[]
  isAddWorkspaceUserDialogOpen: boolean
  selectedPermissions: Record<string, string[]>
  pendingPermissionChanges: Record<string, string[]>

  // Loading states
  loadingRoles: boolean
  loadingUsers: boolean
  loadingPermissions: boolean
  loadingWorkspaceUsers: boolean
  submitting: boolean

  // Error states
  error: string | null
}
