export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  WAITING = 'WAITING',
  DELETED = 'DELETED',
}

export const getUserStatusLabel = (
  status: UserStatus,
  t: (key: string) => string
) => {
  switch (status) {
    case UserStatus.ACTIVE:
      return t('active')
    case UserStatus.INACTIVE:
      return t('inactive')
    case UserStatus.WAITING:
      return t('pending')
    case UserStatus.DELETED:
      return t('deleted')
    default:
      return t('unknown')
  }
}
