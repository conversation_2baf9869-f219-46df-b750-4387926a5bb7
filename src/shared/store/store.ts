import { combineReducers, configureStore } from '@reduxjs/toolkit'
import { enableMapSet } from 'immer'
import authSlice from './slices/authSlice'
import mapSlice from './slices/mapSlice'
import stepsSlice from './slices/stepSlice'
import workspaceSlice from './slices/workspaceSlice'
import datasetSlice from './slices/datasetSlice'
import organizationSettingsSlice from './slices/organizationSettingsSlice'
import layerSettingSlice from './slices/layerSettingsSlice'

enableMapSet()

const rootReducer = combineReducers({
  auth: authSlice,
  map: mapSlice,
  steps: stepsSlice,
  workspace: workspaceSlice,
  dataset: datasetSlice,
  orgSettings: organizationSettingsSlice,
  layerSettings: layerSettingSlice,
})

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
