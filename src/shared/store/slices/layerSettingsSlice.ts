import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { FeatureStyle, Sld, SldType } from './mapSlice'
import { getGradientFromColorRange } from '@/components/Map/utils/layerSettingUtils'

export interface BorderSettings {
  color: string
  opacity: number
  thickness: number
}

export interface DensityRange {
  type: 'automatic' | 'manual'
  min: number
  max: number
}

export interface LayerSettingsState {
  selectedBackgroundType: 'points' | 'heatMap'
  selectedProperty: string
  selectedGradient: string
  heatmapRadius: number
  heatmapIntensity: number
  densityRange: DensityRange
  isLayerBoundariesOpen: boolean
  applyingBorderSettings: boolean
  creatingHeatmap: boolean
  featureStyle: FeatureStyle
}

const initialState: LayerSettingsState = {
  selectedBackgroundType: 'points',
  selectedProperty: '',
  selectedGradient: 'yellow-pink',
  heatmapRadius: 5,
  heatmapIntensity: 50,
  densityRange: {
    type: 'automatic',
    min: 0.1,
    max: 0.9,
  },
  isLayerBoundariesOpen: false,
  applyingBorderSettings: false,
  creatingHeatmap: false,
  featureStyle: {
    stroke_color: '#000000',
    stroke_width: 1,
    stroke_opacity: 1,
  },
}

export const layerSettingsSlice = createSlice({
  name: 'layerSettings',
  initialState,
  reducers: {
    setBackgroundType: (state, action: PayloadAction<'points' | 'heatMap'>) => {
      state.selectedBackgroundType = action.payload
    },
    setSelectedProperty: (state, action: PayloadAction<string>) => {
      state.selectedProperty = action.payload
    },
    setSelectedGradient: (state, action: PayloadAction<string>) => {
      state.selectedGradient = action.payload
    },
    setHeatmapRadius: (state, action: PayloadAction<number>) => {
      state.heatmapRadius = action.payload
    },
    setHeatmapIntensity: (state, action: PayloadAction<number>) => {
      state.heatmapIntensity = action.payload
    },
    setDensityRangeType: (
      state,
      action: PayloadAction<'automatic' | 'manual'>
    ) => {
      state.densityRange.type = action.payload
    },
    setDensityRangeValues: (
      state,
      action: PayloadAction<{ min: number; max: number }>
    ) => {
      state.densityRange.min = action.payload.min
      state.densityRange.max = action.payload.max
    },
    toggleLayerBoundaries: (state) => {
      state.isLayerBoundariesOpen = !state.isLayerBoundariesOpen
    },
    setApplyingBorderSettings: (state, action: PayloadAction<boolean>) => {
      state.applyingBorderSettings = action.payload
    },
    setCreatingHeatmap: (state, action: PayloadAction<boolean>) => {
      state.creatingHeatmap = action.payload
    },
    setFeatureStyle: (state, action: PayloadAction<Partial<FeatureStyle>>) => {
      state.featureStyle = { ...state.featureStyle, ...action.payload }
    },
    setStrokeColor: (state, action: PayloadAction<string>) => {
      state.featureStyle.stroke_color = action.payload
    },
    setStrokeWidth: (state, action: PayloadAction<number>) => {
      state.featureStyle.stroke_width = action.payload
    },
    setStrokeOpacity: (state, action: PayloadAction<number>) => {
      state.featureStyle.stroke_opacity = action.payload
    },
    initializeFromLayer: (state, action: PayloadAction<any>) => {
      const layer = action.payload
      const heatmapSld = layer.slds.find(
        (sld: Sld) => sld.sldType === SldType.HEATMAP
      )
      // Initialize heatmap settings if they exist, but don't change the selectedBackgroundType
      if (heatmapSld?.title && heatmapSld?.featureStyle) {
        // Only set properties if they exist in the layer data
        if (heatmapSld?.featureStyle?.data_weight_field) {
          state.selectedProperty = heatmapSld?.featureStyle?.data_weight_field
        }

        if (heatmapSld?.featureStyle?.radius) {
          state.heatmapRadius = heatmapSld?.featureStyle.radius
        }

        if (heatmapSld?.featureStyle.opacity !== undefined) {
          state.heatmapIntensity = heatmapSld?.featureStyle.opacity * 100
        }

        if (heatmapSld?.featureStyle.density_range) {
          if (
            Array.isArray(heatmapSld?.featureStyle.density_range) &&
            heatmapSld?.featureStyle.density_range.length >= 2
          ) {
            state.densityRange.type = 'manual'
            state.densityRange.min = heatmapSld?.featureStyle.density_range[0]
            state.densityRange.max = heatmapSld?.featureStyle.density_range[1]
          } else if (heatmapSld?.featureStyle.density_range.type === 'manual') {
            state.densityRange.type = 'manual'
            state.densityRange.min = heatmapSld?.featureStyle?.density_range.min
            state.densityRange.max = heatmapSld?.featureStyle?.density_range.max
          }
        }

        if (heatmapSld?.featureStyle?.color_range) {
          state.selectedGradient = getGradientFromColorRange(
            heatmapSld?.featureStyle?.color_range
          )
        }
      }
    },
    resetLayerSettings: () => {
      return initialState
    },
  },
})

export const {
  setBackgroundType,
  setSelectedProperty,
  setSelectedGradient,
  setHeatmapRadius,
  setHeatmapIntensity,
  setDensityRangeType,
  setDensityRangeValues,
  toggleLayerBoundaries,
  setApplyingBorderSettings,
  setCreatingHeatmap,
  setFeatureStyle,
  setStrokeColor,
  setStrokeWidth,
  setStrokeOpacity,
  initializeFromLayer,
  resetLayerSettings,
} = layerSettingsSlice.actions

export default layerSettingsSlice.reducer
