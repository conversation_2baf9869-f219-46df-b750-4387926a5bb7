import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export type DrawType =
  | 'Polygon'
  | 'Point'
  | 'LineString'
  | 'Circle'
  | 'Rectangle'
  | 'none'
  | null

export enum SldType {
  POINTS = 'POINTS',
  HEATMAP = 'HEATMAP',
}

export interface FeatureStyle {
  color?: string
  opacity?: number
  stroke_color?: string
  stroke_width?: number
  stroke_opacity?: number
}

export interface Sld {
  id: string
  sldType: SldType
  title: string
  featureStyle: FeatureStyle
}

export interface FilterCondition {
  id: string
  field: string
  operator: string
  value: any
  dataType: 'string' | 'number' | 'date' | 'time' | 'boolean' | 'integer'
  applied?: boolean
  isVisible: boolean
  isNot: boolean
}

export interface LayerFilters {
  cql_filter: string
  graphql_filters: FilterCondition[]
}
export interface LayerMetadata {
  id: number
  title: string
  description: string
  boundaries: {
    type: string
    coordinates: any
  }
  color: string
  jsonSchema: any
  webUiJsonSchema: any
  locationFieldMapping: Record<string, string> | any
  readOnly: boolean
  className?: string
  isVisible?: boolean
  layerKey?: string
  dataset: { id: number; workspace: { workspaceType: any } }
  selectedBackgroundType?: 'points' | 'heatMap'
  summaryFields: string[]
  slds: Sld[]
  filters?: LayerFilters
  recordsCount?: number
}

export type MapMode = 'details' | 'exploratory'
interface MapState {
  activeDrawType: DrawType
  isBaseLayerVisible: boolean
  isLegendVisible: boolean
  backendLayers: LayerMetadata[]
  selectedWmsFeature: any | null
  selectedMainLayer: LayerMetadata | any
  chatActiveLayers: LayerMetadata[]
  selectedTableLayer: LayerMetadata | null
  sidebarView: 'layers' | 'settings'
  isWmsLoading: boolean
  isMapCollapsed: boolean
  loadingLayers: Record<string, boolean>
  mapMode: MapMode
  workspaceRefreshTrigger: number
}

const initialState: MapState = {
  activeDrawType: null,
  isBaseLayerVisible: false,
  isLegendVisible: false,
  backendLayers: [],
  selectedWmsFeature: null,
  selectedMainLayer: null,
  chatActiveLayers: [],
  selectedTableLayer: null,
  sidebarView: 'layers',
  isWmsLoading: false,
  isMapCollapsed: false,
  loadingLayers: {},
  mapMode: 'details',
  workspaceRefreshTrigger: 0,
}

export const mapSlice = createSlice({
  name: 'map',
  initialState,
  reducers: {
    addBackendLayer: (state, action: PayloadAction<LayerMetadata>) => {
      state.backendLayers.push(action.payload)
    },
    setLayerLoading: (
      state,
      action: PayloadAction<{ layerName: string; isLoading: boolean }>
    ) => {
      const { layerName, isLoading } = action.payload
      state.loadingLayers[layerName] = isLoading

      const hasLoadingLayers = Object.values(state.loadingLayers).some(
        (loading) => loading
      )
      state.isWmsLoading = hasLoadingLayers
    },
    resetBackendLayers(state) {
      state.backendLayers = []
      state.loadingLayers = {}
      state.isWmsLoading = false
      state.workspaceRefreshTrigger += 1
    },
    removeBackendLayer: (state, action: PayloadAction<number>) => {
      const layerToRemove = state.backendLayers.find(
        (layer) => layer.id === action.payload
      )
      if (layerToRemove?.layerKey) {
        delete state.loadingLayers[layerToRemove.layerKey]
      }
      state.backendLayers = state.backendLayers.filter(
        (layer: any) => layer.id !== action.payload
      )
      state.isWmsLoading = Object.values(state.loadingLayers).some(
        (loading) => loading
      )
    },

    setActiveDrawType: (state, action: PayloadAction<DrawType>) => {
      state.activeDrawType = action.payload
    },
    toggleBaseLayer: (state) => {
      state.isBaseLayerVisible = !state.isBaseLayerVisible
    },
    toggleLegend: (state) => {
      state.isLegendVisible = !state.isLegendVisible
    },
    setSelectedWmsFeature: (state, action: PayloadAction<any>) => {
      state.selectedWmsFeature = action.payload
    },
    setSelectedMainLayer: (state, action: PayloadAction<any>) => {
      state.selectedMainLayer = action.payload
    },
    setLayerOrder: (state, action: PayloadAction<LayerMetadata[]>) => {
      state.backendLayers = action.payload
    },
    updateLayer: (
      state,
      action: PayloadAction<{ id: number; updates: Partial<LayerMetadata> }>
    ) => {
      const { id, updates } = action.payload
      state.backendLayers = state.backendLayers.map((layer) =>
        layer.id === id ? { ...layer, ...updates } : layer
      )
      if (state.selectedMainLayer?.id === id) {
        state.selectedMainLayer = { ...state.selectedMainLayer, ...updates }
      }
      if (state.selectedTableLayer?.id === id) {
        state.selectedTableLayer = { ...state.selectedTableLayer, ...updates }
      }
    },
    setSidebarView: (state, action: PayloadAction<'layers' | 'settings'>) => {
      state.sidebarView = action.payload
    },
    setSelectedTableLayer: (
      state,
      action: PayloadAction<LayerMetadata | null>
    ) => {
      state.selectedTableLayer = action.payload
    },
    setIsMapCollapsed: (state, action: PayloadAction<boolean>) => {
      state.isMapCollapsed = action.payload
    },
    updateLayersVisibility: (
      state,
      action: PayloadAction<{ targetLayerKey?: string; showAll?: boolean }>
    ) => {
      const { targetLayerKey, showAll } = action.payload

      state.backendLayers = state.backendLayers.map((layer) => ({
        ...layer,
        isVisible: showAll ? true : layer.layerKey === targetLayerKey,
      }))
    },
    setMapMode: (state, action: PayloadAction<MapMode>) => {
      state.mapMode = action.payload
    },
    setChatActiveLayers: (state, action: PayloadAction<LayerMetadata[]>) => {
      state.chatActiveLayers = action.payload
      if (action.payload.length > 0) {
        state.selectedMainLayer = action.payload[0]
      } else {
        state.selectedMainLayer = null
      }
    },
    clearChatActiveLayers: (state) => {
      state.chatActiveLayers = []
      state.selectedMainLayer = null
    },
    updateLayerBackgroundType: (
      state,
      action: PayloadAction<{
        layerId: number
        backgroundType: 'points' | 'heatMap'
      }>
    ) => {
      const { layerId, backgroundType } = action.payload
      state.backendLayers = state.backendLayers.map((layer) =>
        layer.id === layerId
          ? { ...layer, selectedBackgroundType: backgroundType }
          : layer
      )
      if (state.selectedMainLayer?.id === layerId) {
        state.selectedMainLayer = {
          ...state.selectedMainLayer,
          selectedBackgroundType: backgroundType,
        }
      }
    },
  },
})
export const {
  addBackendLayer,
  resetBackendLayers,
  removeBackendLayer,
  setActiveDrawType,
  toggleBaseLayer,
  toggleLegend,
  setSelectedWmsFeature,
  setSelectedMainLayer,
  setChatActiveLayers,
  clearChatActiveLayers,
  setLayerOrder,
  updateLayer,
  setSidebarView,
  setSelectedTableLayer,
  setIsMapCollapsed,
  setLayerLoading,
  updateLayersVisibility,
  setMapMode,
  updateLayerBackgroundType,
} = mapSlice.actions

export default mapSlice.reducer
