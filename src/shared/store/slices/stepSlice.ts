import { WorkspaceMethods } from '@/shared/utils/routes'
import { StepProgressManager } from '@/shared/utils/stepProgress'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

type StepData = {
  [key: number]: {
    status: 'incomplete' | 'complete'
    formData: any
    requestId?: string
  }
}

interface StepState {
  currentStep: number
  stepData: StepData
}

const initialState: StepState = {
  currentStep: 0,
  stepData: {},
}
const stepSlice = createSlice({
  name: 'steps',
  initialState,
  reducers: {
    goToStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload
    },
    updateStepData: (
      state,
      action: PayloadAction<{ step: number; data: any; method: string }>
    ) => {
      state.stepData[action.payload.step] = action.payload.data
      StepProgressManager.saveProgress(
        action.payload.step,
        action.payload.data,
        action.payload.method
      )
    },
    resetSteps: (state, action: PayloadAction<{ method: string }>) => {
      state.currentStep = 0
      state.stepData = {}
      StepProgressManager.clearProgress(action.payload.method)
      // Remove 'requestId' from localStorage
      if (action.payload.method === WorkspaceMethods.UPLOAD_FILE)
        localStorage.removeItem('requestId')
      else if (action.payload.method === WorkspaceMethods.DESIGN_LAYER)
        localStorage.removeItem(`${WorkspaceMethods.DESIGN_LAYER}-requestId`)
    },
  },
})

export const { goToStep, updateStepData, resetSteps } = stepSlice.actions
export default stepSlice.reducer
