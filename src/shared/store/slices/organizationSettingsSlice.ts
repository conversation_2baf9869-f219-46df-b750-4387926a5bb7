import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { OrganizationService } from '@/shared/servise/organization.service'
import { UserStatus } from '@/shared/types/userStatus'
import { Workspace } from '../slices/workspaceSlice'
import {
  Role,
  WorkspaceMember,
  OrganizationSettingsState,
} from '@/shared/types/organization'

const initialState: OrganizationSettingsState = {
  activeTab: 'info',
  orgId: null,

  roles: [],
  currentRole: null,
  isRoleModalOpen: false,
  roleModalMode: 'create',

  users: [],
  totalUsers: 0,
  userSearchTerm: '',
  userCurrentPage: 1,
  usersPerPage: 10,
  isAddUserDialogOpen: false,

  selectedWorkspace: null,
  workspaceMembers: [],
  workspacePermissions: [],
  isAddWorkspaceUserDialogOpen: false,
  selectedPermissions: {},
  pendingPermissionChanges: {},

  loadingRoles: false,
  loadingUsers: false,
  loadingPermissions: false,
  loadingWorkspaceUsers: false,
  submitting: false,

  error: null,
}

// Async thunks
export const fetchRoles = createAsyncThunk(
  'organizationSettings/fetchRoles',
  async (orgId: string | number, { rejectWithValue }) => {
    try {
      return await OrganizationService.fetchRoles(orgId)
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchUsers = createAsyncThunk(
  'organizationSettings/fetchUsers',
  async (
    {
      orgId,
      page,
      limit,
      searchTerm,
    }: {
      orgId: string | number
      page: number
      limit: number
      searchTerm: string
    },
    { rejectWithValue }
  ) => {
    try {
      return await OrganizationService.fetchUsers(
        orgId,
        page,
        limit,
        searchTerm
      )
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchPermissions = createAsyncThunk(
  'organizationSettings/fetchPermissions',
  async (orgId: string | number, { rejectWithValue }) => {
    try {
      return await OrganizationService.fetchWorkspacePermissions(orgId)
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchWorkspaceUsers = createAsyncThunk(
  'organizationSettings/fetchWorkspaceUsers',
  async (
    {
      orgId,
      workspaceId,
    }: {
      orgId: string | number
      workspaceId: string | number
    },
    { rejectWithValue }
  ) => {
    try {
      return await OrganizationService.fetchWorkspaceUsers(orgId, workspaceId)
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const addUser = createAsyncThunk(
  'organizationSettings/addUser',
  async (
    {
      orgId,
      firstName,
      lastName,
      email,
      roleId,
      phone,
    }: {
      orgId: string | number
      firstName: string
      lastName: string
      email: string
      roleId: string
      phone: string
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      await OrganizationService.addUser(
        orgId,
        firstName,
        lastName,
        email,
        roleId,
        phone
      )

      // Refresh users list
      dispatch(fetchUsers({ orgId, page: 1, limit: 10, searchTerm: '' }))
      return true
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const changeUserRole = createAsyncThunk(
  'organizationSettings/changeUserRole',
  async (
    {
      orgId,
      userId,
      roleId,
    }: {
      orgId: string | number
      userId: string
      roleId: string
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      await OrganizationService.changeUserRole(orgId, userId, roleId)

      // Refresh users list
      dispatch(
        fetchUsers({
          orgId,
          page: 1,
          limit: 10,
          searchTerm: '',
        })
      )
      return { userId, roleId }
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const changeUserStatus = createAsyncThunk(
  'organizationSettings/changeUserStatus',
  async (
    {
      orgId,
      userId,
      activeStatus,
    }: {
      orgId: string | number
      userId: string
      activeStatus: UserStatus
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      await OrganizationService.changeUserStatus(orgId, userId, activeStatus)

      // Refresh users list
      dispatch(
        fetchUsers({
          orgId,
          page: 1,
          limit: 10,
          searchTerm: '',
        })
      )
      return { userId, activeStatus }
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const createOrUpdateRole = createAsyncThunk(
  'organizationSettings/createOrUpdateRole',
  async (
    {
      orgId,
      roleId,
      title,
      // description,
      permissions,
      isEdit,
    }: {
      orgId: string | number
      roleId?: string
      title: string
      description: string
      permissions: string[]
      isEdit: boolean
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      if (isEdit && roleId) {
        await OrganizationService.updateRole(orgId, roleId, title, permissions)
      } else {
        await OrganizationService.createRole(orgId, title, permissions)
      }

      // Refresh roles list
      dispatch(fetchRoles(orgId))
      return true
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const assignWorkspacePermissions = createAsyncThunk(
  'organizationSettings/assignWorkspacePermissions',
  async (
    {
      orgId,
      workspaceId,
      userIds,
      permissionIds,
    }: {
      orgId: string | number
      workspaceId: string | number
      userIds: string[]
      permissionIds: string[]
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      await OrganizationService.assignWorkspacePermissions(
        orgId,
        workspaceId,
        userIds,
        permissionIds
      )

      // Refresh workspace users
      dispatch(fetchWorkspaceUsers({ orgId, workspaceId }))
      return true
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const organizationSettingsSlice = createSlice({
  name: 'organizationSettings',
  initialState,
  reducers: {
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload
    },
    setOrgId: (state, action: PayloadAction<string>) => {
      state.orgId = action.payload
    },
    setRoleModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isRoleModalOpen = action.payload
    },
    setRoleModalMode: (state, action: PayloadAction<'create' | 'edit'>) => {
      state.roleModalMode = action.payload
    },
    setCurrentRole: (state, action: PayloadAction<Role | null>) => {
      state.currentRole = action.payload
    },
    setUserSearchTerm: (state, action: PayloadAction<string>) => {
      state.userSearchTerm = action.payload
      state.userCurrentPage = 1 // Reset to first page on search
    },
    setUserCurrentPage: (state, action: PayloadAction<number>) => {
      state.userCurrentPage = action.payload
    },
    setAddUserDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isAddUserDialogOpen = action.payload
    },
    setSelectedWorkspace: (state, action: PayloadAction<Workspace | null>) => {
      state.selectedWorkspace = action.payload
    },
    setAddWorkspaceUserDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isAddWorkspaceUserDialogOpen = action.payload
    },
    updateSelectedPermissions: (
      state,
      action: PayloadAction<{ userId: string; permissions: string[] }>
    ) => {
      const { userId, permissions } = action.payload
      state.selectedPermissions[userId] = permissions
    },
    updatePendingPermissionChanges: (
      state,
      action: PayloadAction<{ userId: string; permissions: string[] }>
    ) => {
      const { userId, permissions } = action.payload
      state.pendingPermissionChanges[userId] = permissions
    },
    clearPendingPermissionChanges: (state) => {
      state.pendingPermissionChanges = {}
    },
  },
  extraReducers: (builder) => {
    // Fetch roles
    builder.addCase(fetchRoles.pending, (state) => {
      state.loadingRoles = true
      state.error = null
    })
    builder.addCase(fetchRoles.fulfilled, (state, action) => {
      state.roles = action.payload
      state.loadingRoles = false
    })
    builder.addCase(fetchRoles.rejected, (state, action) => {
      state.loadingRoles = false
      state.error = action.payload as string
    })

    // Fetch users
    builder.addCase(fetchUsers.pending, (state) => {
      state.loadingUsers = true
      state.error = null
    })
    builder.addCase(fetchUsers.fulfilled, (state, action) => {
      state.users = action.payload.users
      state.totalUsers = action.payload.totalCount
      state.loadingUsers = false
    })
    builder.addCase(fetchUsers.rejected, (state, action) => {
      state.loadingUsers = false
      state.error = action.payload as string
    })

    // Fetch permissions
    builder.addCase(fetchPermissions.pending, (state) => {
      state.loadingPermissions = true
      state.error = null
    })
    builder.addCase(fetchPermissions.fulfilled, (state, action) => {
      state.workspacePermissions = action.payload
      state.loadingPermissions = false
    })
    builder.addCase(fetchPermissions.rejected, (state, action) => {
      state.loadingPermissions = false
      state.error = action.payload as string
    })

    // Fetch workspace users
    builder.addCase(fetchWorkspaceUsers.pending, (state) => {
      state.loadingWorkspaceUsers = true
      state.error = null
    })
    builder.addCase(fetchWorkspaceUsers.fulfilled, (state, action) => {
      state.workspaceMembers = action.payload

      // Initialize permissions for each user
      const initialPermissions: Record<string, string[]> = {}
      action.payload.forEach((user: WorkspaceMember) => {
        if (user.workspacePermissions) {
          initialPermissions[user.id] = user.workspacePermissions.map(
            (p) => p.id
          )
        } else {
          initialPermissions[user.id] = []
        }
      })
      state.selectedPermissions = initialPermissions
      state.pendingPermissionChanges = {}
      state.loadingWorkspaceUsers = false
    })
    builder.addCase(fetchWorkspaceUsers.rejected, (state, action) => {
      state.loadingWorkspaceUsers = false
      state.error = action.payload as string
    })

    // Add user
    builder.addCase(addUser.pending, (state) => {
      state.submitting = true
      state.error = null
    })
    builder.addCase(addUser.fulfilled, (state) => {
      state.submitting = false
      state.isAddUserDialogOpen = false
    })
    builder.addCase(addUser.rejected, (state, action) => {
      state.submitting = false
      state.error = action.payload as string
    })

    // Change user role
    builder.addCase(changeUserRole.pending, (state) => {
      state.submitting = true
      state.error = null
    })
    builder.addCase(changeUserRole.fulfilled, (state) => {
      state.submitting = false
    })
    builder.addCase(changeUserRole.rejected, (state, action) => {
      state.submitting = false
      state.error = action.payload as string
    })

    // Change user status
    builder.addCase(changeUserStatus.pending, (state) => {
      state.submitting = true
      state.error = null
    })
    builder.addCase(changeUserStatus.fulfilled, (state) => {
      state.submitting = false
    })
    builder.addCase(changeUserStatus.rejected, (state, action) => {
      state.submitting = false
      state.error = action.payload as string
    })

    // Create or update role
    builder.addCase(createOrUpdateRole.pending, (state) => {
      state.submitting = true
      state.error = null
    })
    builder.addCase(createOrUpdateRole.fulfilled, (state) => {
      state.submitting = false
      state.isRoleModalOpen = false
      state.currentRole = null
    })
    builder.addCase(createOrUpdateRole.rejected, (state, action) => {
      state.submitting = false
      state.error = action.payload as string
    })

    // Assign workspace permissions
    builder.addCase(assignWorkspacePermissions.pending, (state) => {
      state.submitting = true
      state.error = null
    })
    builder.addCase(assignWorkspacePermissions.fulfilled, (state) => {
      state.submitting = false
      state.isAddWorkspaceUserDialogOpen = false
    })
    builder.addCase(assignWorkspacePermissions.rejected, (state, action) => {
      state.submitting = false
      state.error = action.payload as string
    })
  },
})

export const {
  setActiveTab,
  setOrgId,
  setRoleModalOpen,
  setRoleModalMode,
  setCurrentRole,
  setUserSearchTerm,
  setUserCurrentPage,
  setAddUserDialogOpen,
  setSelectedWorkspace,
  setAddWorkspaceUserDialogOpen,
  updateSelectedPermissions,
  updatePendingPermissionChanges,
  clearPendingPermissionChanges,
} = organizationSettingsSlice.actions

export default organizationSettingsSlice.reducer
