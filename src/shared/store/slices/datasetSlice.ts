import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface DatasetState {
  letterMappings: Record<number, string>
}

const initialState: DatasetState = {
  letterMappings: { 0: 'A' },
}

export const datasetSlice = createSlice({
  name: 'dataset',
  initialState,
  reducers: {
    setLetterMappings: (
      state,
      action: PayloadAction<Record<number, string>>
    ) => {
      state.letterMappings = action.payload
    },
  },
})

export const { setLetterMappings } = datasetSlice.actions
export default datasetSlice.reducer
