import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { GET_WORKSPACES } from '@/shared/graphQl/queries/queries'
import { graphQlAppClient } from '@/shared/graphQl'
import { handleUnauthorizedError } from '@/shared/utils/authHelpers'

export const fetchWorkspaces = createAsyncThunk(
  'workspace/fetchWorkspaces',
  async (
    {
      searchTerm,
      offset,
      limit,
      orderBy,
      orgId,
    }: {
      searchTerm: string
      offset: number
      limit: number
      orderBy: string
      orgId: number
    },
    { rejectWithValue }
  ) => {
    if (!orgId) return rejectWithValue('No organization selected')

    try {
      const { data, errors } = await graphQlAppClient.query({
        query: GET_WORKSPACES,
        variables: {
          orgId,
          filters: searchTerm
            ? [{ field: 'name', value: searchTerm, clause: 'icontains' }]
            : [],
          limit,
          offset,
          orderBy,
        },
      })

      if (handleUnauthorizedError({ errors })) {
        return rejectWithValue('Session expired. Please login again.')
      }
      return {
        workspaces: data.workspaces?.data || [],
        totalCount: data.workspaces?.count || 0,
      }
    } catch (error) {
      if (handleUnauthorizedError(error)) {
        return rejectWithValue('Session expired. Please login again.')
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message)
      }
      return rejectWithValue('An unknown error occurred')
    }
  }
)
interface WorkspaceState {
  workspaces: Workspace[]
  totalCount: number
  loading: boolean
  error: string | null
  sortBy: 'created' | 'last_visited'
  selectedWorkspace: null | Workspace
  sortOrder: 'asc' | 'desc'
}

export interface Workspace {
  id: string
  name: string
  description: string
  created: string
  lastVisited: string | null
  since: string
  thumbnail: string
  layersData?: {
    layersCount: number
    recordsCount: number
  }
  workspaceType?: string
}

const initialState: WorkspaceState = {
  workspaces: [],
  totalCount: 0,
  loading: false,
  error: null,
  sortBy: 'created',
  selectedWorkspace: null,
  sortOrder: 'desc',
}

const workspaceSlice = createSlice({
  name: 'workspace',
  initialState,
  reducers: {
    setSortOrder: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.sortOrder = action.payload
    },
    setSortBy: (state, action: PayloadAction<'created' | 'last_visited'>) => {
      state.sortBy = action.payload
      state.workspaces = []
    },
    deleteWorkspace: (state, action) => {
      const id = action.payload
      state.workspaces = state.workspaces.filter(
        (workspace) => workspace.id !== id
      )
      state.totalCount -= 1
    },
    updateWorkspace: (state, action) => {
      const { id, updates } = action.payload
      const index = state.workspaces.findIndex(
        (workspace) => workspace.id === id
      )
      if (index !== -1) {
        state.workspaces[index] = { ...state.workspaces[index], ...updates }
      }
    },
    setSelectedWorkspace: (state, action: PayloadAction<Workspace | null>) => {
      state.selectedWorkspace = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWorkspaces.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(
        fetchWorkspaces.fulfilled,
        (
          state,
          action: PayloadAction<{
            workspaces: Workspace[]
            totalCount: number
          }>
        ) => {
          state.workspaces = action.payload.workspaces
          state.totalCount = action.payload.totalCount
          state.loading = false
        }
      )
      .addCase(fetchWorkspaces.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const {
  setSortOrder,
  setSortBy,
  deleteWorkspace,
  updateWorkspace,
  setSelectedWorkspace,
} = workspaceSlice.actions
export default workspaceSlice.reducer
