import { ReactNode, useEffect } from "react";
import { useAuth } from "react-oidc-context";
import { useLocation } from "react-router-dom";
import jsCookies from "js-cookie";
import { Routes } from "../utils/routes";
import { LoadingOverlay } from "@/components/LoadingOverlay";
import { setUser } from "../store/slices/authSlice";
import { useDispatch } from 'react-redux';
import { showToast } from "../utils/toastConfig";

interface AuthWrapperProps {
  children: ReactNode;
}

export const AuthWrapper = ({ children }: AuthWrapperProps) => {
  const router = useLocation();
  const dispatch = useDispatch();
  const { error, isLoading, user: userData, isAuthenticated } = useAuth();
  // const { data: userDetails, loading: userDetailsLoading } = useQuery(FETCH_USER_DETAILS, {
  //   skip: !isAuthenticated,
  //   variables: { orgId: userData?.profile?.org_id ?? 1 },
  // });
  useEffect(() => {
    handleAuthenticationStatus();
  }, [error, isAuthenticated, isLoading, router.pathname, userData]);

  const handleAuthenticationStatus = () => {
    if (error) {
      handleAuthError();
    }

    if (!isLoading && isAuthenticated && userData) {
      handleSuccessfulAuthentication();
    }
  };

  const handleAuthError = () => {
    if (error) {
      console.error({ error });
      showToast.error(`Authentication error: ${error.message}`);
    }
  };

  const handleSuccessfulAuthentication = () => {
    jsCookies.set("access_token", userData?.access_token ?? '');
    if (router.pathname?.startsWith("/auth/callback")) {
      location.href = `/${Routes.dashboard}`;
    } else {
      dispatchUserData();
    }
  };

  const dispatchUserData = () => {
    const user = {
      email: userData?.profile?.email as string,
      firstName: userData?.profile?.first_name as string,
      lastName: userData?.profile?.last_name as string,
      phone: userData?.profile?.phone as string,
    };

    dispatch(setUser({ userToken: userData?.access_token ?? '', userInfo: { ...user } }));
  };

  return (isLoading) ? <LoadingOverlay /> : children;
};
