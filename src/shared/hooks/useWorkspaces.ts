/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { fetchWorkspaces } from '@/shared/store/slices/workspaceSlice'
import { RootState } from '@/shared/store/store'
import { useDebounce } from './useDebounce'
import { useOrganizationManager } from './useOrganizationManager'

export const useWorkspaces = (
  searchTerm: string,
  limit: number,
  offset: number = 0
) => {
  const dispatch = useDispatch()
  const { workspaces, loading, totalCount, sortBy, sortOrder, error } =
    useSelector((state: RootState) => state.workspace)
  const { selectedOrg } = useOrganizationManager()

  const debouncedSearch = useDebounce(searchTerm, 500)

  useEffect(() => {
    if (selectedOrg?.id) {
      // Determine the orderBy value based on sortBy and sortOrder
      let orderByValue = sortBy as any

      // If sortOrder is desc, add a minus sign prefix
      if (sortOrder === 'desc') {
        orderByValue = `-${sortBy}`
      }

      dispatch(
        fetchWorkspaces({
          searchTerm: debouncedSearch,
          offset,
          limit,
          orderBy: orderByValue,
          orgId: selectedOrg?.id,
        }) as any
      )
    }
  }, [debouncedSearch, sortBy, sortOrder, selectedOrg?.id, offset, limit])

  return {
    workspaces,
    loading,
    error,
    totalCount,
  }
}
