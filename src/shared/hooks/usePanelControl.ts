import { useRef, useState } from 'react'
import { ImperativePanelHandle } from 'react-resizable-panels'

export const usePanelControl = (defaultSize: number) => {
  const [isCollapsed, setIsCollapsed] = useState(true)
  const panelRef = useRef<ImperativePanelHandle>(null)
  const prevSize = useRef(defaultSize)

  const togglePanel = (onExpandOther?: () => void) => {
    if (!isCollapsed) {
      if (panelRef.current) {
        prevSize.current = Math.max(panelRef.current.getSize(), defaultSize)
      }
      setIsCollapsed(true)
      onExpandOther?.()
    } else {
      const targetSize = Math.max(prevSize.current, defaultSize)
      panelRef.current?.resize(targetSize)
      setIsCollapsed(false)
    }
  }

  return {
    isCollapsed,
    panelRef,
    togglePanel,
  }
}
