import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/shared/store'
import { setLetterMappings } from '../store/slices/datasetSlice'
import { useQuery } from '@apollo/client'
import { GET_DATASETS } from '../graphQl/queries/layers'
import { useParams } from 'react-router-dom'
import { useOrganizationManager } from './useOrganizationManager'

interface Dataset {
  id: number
  file: string
  created: string
  metaData: {
    columns: string[]
    eda_report: any
    json_schema: any
    sample_data: any[]
    summary_fields: string[]
    web_ui_json_schema: any
  }
}

export const useDatasetLetters = () => {
  const dispatch = useDispatch()
  const { workspaceId } = useParams()
  const letterMappings = useSelector(
    (state: RootState) => state.dataset.letterMappings || {}
  )
  const { selectedOrg } = useOrganizationManager()

  const { data: datasetsData } = useQuery(GET_DATASETS, {
    variables: { workspaceId: parseInt(workspaceId!), orgId: selectedOrg?.id },
    skip: !workspaceId || !selectedOrg?.id,
  })

  const datasets = datasetsData?.datasets?.data || []

  useEffect(() => {
    if (!datasets?.length) return
    const sortedDatasets = [...datasets].sort(
      (a: Dataset, b: Dataset) => a.id - b.id
    )
    const mappings = sortedDatasets.reduce(
      (acc, dataset: Dataset) => {
        if (dataset.id) {
          acc[dataset.id] = String.fromCharCode(65 + Object.keys(acc).length)
        }
        return acc
      },
      {} as Record<number, string>
    )

    dispatch(setLetterMappings(mappings))
  }, [datasets, dispatch])

  return { letterMappings, datasets }
}
