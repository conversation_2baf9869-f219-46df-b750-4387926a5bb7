import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

export default function useLocalizeDocumentAttributes() {
  const { i18n } = useTranslation()

  useEffect(() => {
    const savedLanguage = localStorage.getItem('preferredLanguage')
    if (savedLanguage && savedLanguage !== i18n.language) {
      document.body.style.fontFamily =
        savedLanguage === 'ar'
          ? "'IBM Plex Sans Arabic', sans-serif"
          : "'Plus Jakarta Sans', sans-serif"
      i18n.changeLanguage(savedLanguage)
    }
  }, [])

  useEffect(() => {
    if (i18n.resolvedLanguage) {
      document.documentElement.lang = i18n.resolvedLanguage
      document.documentElement.dir = i18n.dir(i18n.resolvedLanguage)
      document.body.style.fontFamily =
        i18n.resolvedLanguage === 'ar'
          ? "'IBM Plex Sans Arabic', sans-serif"
          : "'Plus Jakarta Sans', sans-serif"
    }
  }, [i18n, i18n.resolvedLanguage])
}
