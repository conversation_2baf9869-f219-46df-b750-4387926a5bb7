import { useQuery } from '@apollo/client'
import { useSearchParams, useNavigate, useLocation } from 'react-router-dom'
import { GET_ORGANIZATIONS } from '@/shared/graphQl/queries/queries'
import { handleUnauthorizedError } from '../utils/authHelpers'

export const useOrganizationManager = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const orgIdFromUrl = searchParams.get('orgId')

  const { data, loading, error, refetch } = useQuery(GET_ORGANIZATIONS, {
    fetchPolicy: 'cache-first',
  })

  if (error) {
    handleUnauthorizedError(error)
  }
  const organizations = data?.organizations?.data || []
  const count = data?.organizations?.count
  const selectedOrg =
    organizations.find((org: any) => org.id.toString() === orgIdFromUrl) ||
    organizations[0]

  const selectOrganization = (org: any) => {
    const params = new URLSearchParams(searchParams)
    // Remove any existing orgId params first
    params.delete('orgId')
    params.set('orgId', org.id.toString())
    navigate(`${location.pathname}?${params.toString()}`, { replace: true })
  }

  const navigateWithOrg = (
    path: string,
    options?: {
      newTab?: boolean
      replace?: boolean
      additionalParams?: Record<string, string>
    }
  ) => {
    // Parse the target path's existing query parameters, if any
    const [basePath, existingQuery] = path.split('?')
    const params = new URLSearchParams(existingQuery || '')

    // Remove any existing orgId params
    params.delete('orgId')

    // Set orgId
    params.set('orgId', selectedOrg.id.toString())

    // Add additional params
    if (options?.additionalParams) {
      Object.entries(options.additionalParams).forEach(([key, value]) => {
        if (!params.has(key)) {
          params.set(key, value)
        }
      })
    }

    const url = `${basePath}?${params.toString()}`

    if (options?.newTab) {
      // For new tabs, use the stored title from localStorage
      const savedTitle = localStorage.getItem('currentWorkspaceTitle')
      const newTab = window.open(url, '_blank')
      if (newTab && savedTitle) {
        newTab.onload = () => {
          newTab.document.title = savedTitle
        }
      }
      return
    }

    navigate(url, { replace: options?.replace })
  }

  if (organizations.length > 0 && !orgIdFromUrl) {
    selectOrganization(organizations[0])
  }

  return {
    selectedOrg,
    organizations,
    selectOrganization,
    navigateWithOrg,
    count,
    loading,
    refetch,
  }
}
