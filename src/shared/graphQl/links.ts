import { createUploadLink } from 'apollo-upload-client'
import { GraphQLErrorExtensions, SourceLocation } from 'graphql'
import { ApolloLink, HttpLink } from '@apollo/client'
import { onError } from '@apollo/client/link/error'
import { getFullToken } from '../utils/generals'
import i18next from 'i18next'
import { showToast } from '../utils/toastConfig'
import { extractReason } from '../utils/errorHelper'
import { handleUnauthorizedError } from '../utils/authHelpers'

interface CustomGraphQLErrorExtensions extends GraphQLErrorExtensions {
  http?: {
    status?: number
    status_text?: string
    reason?: { [key: string]: string[] | string } | null
  }
}

export type CustomGraphQLError = {
  message?: string
  locations?: readonly SourceLocation[] | undefined
  path?: readonly (string | number)[] | undefined
  extensions?: CustomGraphQLErrorExtensions
}

export const authLink = new ApolloLink((operation, forward) => {
  const token = getFullToken()
  const currentLanguage = i18next.language || 'ar'
  operation.setContext(({ headers }: { headers: HeadersInit }) => ({
    headers: {
      authorization: token,
      'Accept-Language': currentLanguage,
      ...headers,
    },
  }))

  return forward(operation)
})

export const errorLink = onError(({ graphQLErrors }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach((err: CustomGraphQLError) => {
      console.error(err)
      // Check for unauthorized errors first
      if (handleUnauthorizedError(err)) return
      const msg = extractReason(err) as any
      showToast.error(msg || 'Something went wrong, please try again later')
    })
  }
})

export const httpLink = new HttpLink({
  uri: import.meta.env.VITE_GRAPHQL_URL,
})

export const uploadLink = createUploadLink({
  uri: import.meta.env.VITE_GRAPHQL_URL,
})
