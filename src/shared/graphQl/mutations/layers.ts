import { gql } from '@apollo/client'

// need orgId
export const UPDATE_WORKSPACE = gql`
  mutation UpdateWorkspaceLayersSort($dataInput: UpdateWorkSpaceInputType) {
    updateWorkspace(dataInput: $dataInput) {
      workspace {
        id
        lastVisited
        layersSortedIds
      }
    }
  }
`

export const UPDATE_WORKSPACE_LAST_VISITED = gql`
  mutation UpdateWorkspaceLastVisited(
    $dataInput: UpdateWorkSpaceLastVisitedDateInputType
  ) {
    updateWorkspaceLastVisitedDate(dataInput: $dataInput) {
      workspace {
        id
        lastVisited
        layersSortedIds
      }
    }
  }
`
export const UPDATE_LAYER_SUMMARY_FIELDS = gql`
  mutation UpdateLayerSummaryFields(
    $dataInput: UpdateRecordsSummaryFieldsInputType!
  ) {
    updateRecordsSummaryFields(dataInput: $dataInput) {
      success
    }
  }
`
export const UPDATE_RECORD = gql`
  mutation UpdateRecord($dataInput: UpdateRecordInputType!) {
    updateRecord(dataInput: $dataInput) {
      record {
        layerId
        id
        geometry
        data
      }
    }
  }
`

export const CREATE_RECORD = gql`
  mutation CreateRecord($dataInput: CreateRecordInputType!) {
    createRecord(dataInput: $dataInput) {
      record {
        data
        id
        layerId
        geometry
      }
    }
  }
`
// need orgId
export const SEND_MESSAGE_CHAT_AI = gql`
  mutation SendMessageChatAi($inputForm: NewChatInputType!) {
    sendMessageChatAi(inputForm: $inputForm) {
      dbResult
      response
    }
  }
`
// Reset chat AI conversation
export const RESET_CHAT_AI = gql`
  mutation ResetChatAi($dataInput: ResetNewChatInputType!) {
    resetChatAi(dataInput: $dataInput) {
      success
    }
  }
`
export const UPDATE_LAYER = gql`
  mutation UpdateLayer($dataInput: UpdateLayerInputType!) {
    updateLayer(dataInput: $dataInput) {
      layer {
        layerKey
        id
      }
    }
  }
`
export const DELETE_LAYER = gql`
  mutation DeleteLayer($dataInput: DeleteLayerInputType!) {
    deleteLayer(dataInput: $dataInput) {
      success
    }
  }
`

export const CREATE_LAYER_FROM_DATASET = gql`
  mutation CreateLayerFromDataset(
    $dataInput: CreateLayerFromDatasetInputType!
  ) {
    createLayerFromDataset(dataInput: $dataInput) {
      layer {
        id
        layerKey
        boundaries
        jsonSchema
        readOnly
        locationFieldMapping
        title
        webUiJsonSchema
        description
        status
        dataset {
          id
        }
        slds {
          featureStyle
          id
          sldType
          title
        }
        filters
        sampleData
        dataFields
        data
      }
    }
  }
`
export const CREATE_HEATMAP = gql`
  mutation CreateHeatMap($dataInput: HeatMapInputType!) {
    createHeatMap(dataInput: $dataInput) {
      sld {
        id
        title
      }
    }
  }
`

export const CREATE_LAYER_SLD = gql`
  mutation CreateLayerSld($dataInput: SLDInputType!) {
    createLayerSld(dataInput: $dataInput) {
      sld {
        id
      }
    }
  }
`

export const UPDATE_WORKSPACE_THUMBNAIL = gql`
  mutation UpdateWorkspaceThumbnail($dataInput: UpdateWorkSpaceInputType!) {
    updateWorkspace(dataInput: $dataInput) {
      workspace {
        id
        thumbnail
        workspaceType
      }
    }
  }
`

export const UPDATE_LAYER_FILTERS = gql`
  mutation UpdateLayerFilters($dataInput: UpdateLayerFiltersInputType!) {
    updateLayerFilters(dataInput: $dataInput) {
      layer {
        id
      }
    }
  }
`
