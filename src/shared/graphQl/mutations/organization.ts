import { gql } from '@apollo/client'

export const CREATE_ROLE = gql`
  mutation CreateRole($dataInput: RoleInputType!) {
    createRole(dataInput: $dataInput) {
      role {
        id
        title
        usersCount
        permissions {
          id
          codename
          name
        }
      }
    }
  }
`

export const UPDATE_ROLE = gql`
  mutation UpdateRole($dataInput: UpdateRoleInputType!) {
    updateRole(dataInput: $dataInput) {
      role {
        id
        title
        usersCount
        permissions {
          id
          name
          codename
        }
      }
    }
  }
`

export const DELETE_ROLE = gql`
  mutation DeleteRole($dataInput: DeleteRoleInputType!) {
    deleteRole(dataInput: $dataInput) {
      success
    }
  }
`

export const UPDATE_ORGANIZATION_MUTATION = gql`
  mutation UpdateOrganization($dataInput: OrganizationInputType!) {
    updateOrganization(dataInput: $dataInput) {
      organization {
        usersCount
        settings
      }
    }
  }
`

// Mutation to change a user's role
export const CHANGE_USER_ROLE_MUTATION = gql`
  mutation ChangeUserRole($dataInput: ChangeUserRoleInputType!) {
    changeUserRole(dataInput: $dataInput) {
      user {
        id
        email
        role {
          id
          title
          permissions {
            id
            name
          }
        }
      }
    }
  }
`

// Mutation to add a new user
export const ADD_USER_MUTATION = gql`
  mutation AddUser($dataInput: AddUserInputType!) {
    addUser(dataInput: $dataInput) {
      user {
        id
        phone
        lastName
        activeStatus
      }
    }
  }
`
// Mutation to update a user's active status
export const CHANGE_USER_ACTIVE_STATUS_MUTATION = gql`
  mutation ChangeUserActiveStatus(
    $dataInput: ChangeUserActiveStatusInputType!
  ) {
    changeUserActiveStatus(dataInput: $dataInput) {
      user {
        id
        activeStatus
      }
    }
  }
`

export const ASSIGN_WORKSPACE_USER_PERMISSIONS = gql`
  mutation AssignWorkspaceUserPermissions(
    $dataInput: AssignWorkspaceUserPermissionsInputType!
  ) {
    assignWorkspaceUserPermissions(dataInput: $dataInput) {
      users {
        id
        email
        activeStatus
        workspacePermissions {
          id
          codename
        }
      }
    }
  }
`
