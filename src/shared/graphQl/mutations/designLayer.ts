import { gql } from '@apollo/client'

export const CREATE_DESIGN_LAYER_REQUEST = gql`
  mutation createDesignLayerRequest(
    $dataInput: CreateDesignLayerRequestInputType!
  ) {
    createDesignLayerRequest(dataInput: $dataInput) {
      workspaceRequest {
        currentStep
        requestType
        id
        layerData
        status
      }
    }
  }
`
export const CANCEL_DESIGN_LAYER_REQUEST = gql`
  mutation cancelWorkspaceRequest(
    $closeInput: CancelWorkspaceRequestInputType!
  ) {
    cancelWorkspaceRequest(closeInput: $closeInput) {
      closed
    }
  }
`

export const DESIGN_LAYER_JSON_SCHEMA = gql`
  mutation designLayerJsonSchema($dataInput: DesignLayerJsonSchemaInputType!) {
    designLayerJsonSchema(dataInput: $dataInput) {
      workspace {
        created
        description
        id
        lastVisited
        name
        since
        thumbnail
      }
    }
  }
`

export const UPDATE_DESIGN_LAYER_REQUEST = gql`
  mutation updateDesignLayerRequest(
    $dataInput: UpdateDesignLayerRequestInputType!
  ) {
    updateDesignLayerRequest(dataInput: $dataInput) {
      workspaceRequest {
        id
        currentStep
        layerData
        requestType
        status
      }
    }
  }
`
