import { gql } from '@apollo/client'

export const CREATE_DATASET = gql`
  mutation CreateDataset($dataInput: CreateDatasetInputType!) {
    createDataset(dataInput: $dataInput) {
      datasetRequest {
        id
        layerData
        status
      }
    }
  }
`
// need orgId
export const CANCEL_DATASET_REQUEST = gql`
  mutation cancelWorkspaceRequest(
    $closeInput: CancelWorkspaceRequestInputType!
  ) {
    cancelWorkspaceRequest(closeInput: $closeInput) {
      closed
    }
  }
`
// need orgId
export const CREATE_LOCATION_FIELD_MAPPING = gql`
  mutation CreateLocationFieldMapping(
    $dataInput: LocationFieldMappingInputType!
  ) {
    createLocationFieldMapping(dataInput: $dataInput) {
      datasetRequest {
        id
        layerData
        status
      }
    }
  }
`
// need orgId
export const UPDATE_JSON_SCHEMAS = gql`
  mutation UpdateJsonSchemas($dataInput: UpdateJSONSchemaInputType!) {
    updateJsonSchemas(dataInput: $dataInput) {
      datasetRequest {
        id
      }
    }
  }
`
// need orgId
export const CREATE_WORKSPACE = gql`
  mutation CreateWorkSpaceLayer($dataInput: CreateWorkSpaceInputType!) {
    createWorkspaceLayer(dataInput: $dataInput) {
      workspace {
        created
        description
        id
        lastVisited
        name
        since
        thumbnail
      }
    }
  }
`

export const CREATE_EMPTY_WORKSPACE = gql`
  mutation CreateEmptyWorkspace($dataInput: CreateEmptyWorkspaceInputType!) {
    createEmptyWorkspace(dataInput: $dataInput) {
      workspace {
        id
        name
        created
        description
        lastVisited
        layersSortedIds
        modified
        since
        workspaceType
        thumbnail
        layersData {
          recordsCount
          layersCount
        }
      }
    }
  }
`
