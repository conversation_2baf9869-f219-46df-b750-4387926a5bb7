import { gql } from '@apollo/client'

export const FETCH_USER_DETAILS = gql`
  query FetchUserDetails {
    userDetails {
      email
      firstName
      id
      lastName
      phone
    }
  }
`

export const GET_WORKSPACES = gql`
  query GetWorkspaces(
    $orgId: Int!
    $filters: [DjangoFilterInput]!
    $limit: BoundedInt!
    $offset: BoundedInt!
    $orderBy: String!
  ) {
    workspaces(
      orgId: $orgId
      pageInfo: { limit: $limit, offset: $offset, orderBy: $orderBy }
      filters: $filters
    ) {
      data {
        id
        name
        description
        created
        lastVisited
        thumbnail
        since
        layersData {
          layersCount
          recordsCount
        }
        workspaceType
      }
      count
    }
  }
`

export const GET_ORGANIZATIONS = gql`
  query GetOrganizations {
    organizations {
      count
      data {
        id
        settings
        usersCount
      }
    }
  }
`
