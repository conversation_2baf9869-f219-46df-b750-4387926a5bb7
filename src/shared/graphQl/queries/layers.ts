import { gql } from '@apollo/client'

export const GET_LAYERS = gql`
  query getLayers($workspaceId: Int!, $orgId: Int!) {
    layers(workspaceId: $workspaceId, orgId: $orgId) {
      count
      data {
        filters
        dataFields
        recordsCount
        boundaries
        id
        jsonSchema
        layerKey
        readOnly
        locationFieldMapping
        title
        webUiJsonSchema
        description
        status
        dataset {
          id
          workspace {
            workspaceType
          }
        }
        slds {
          id
          sldType
          title
          featureStyle
        }
        summaryFields
      }
    }
  }
`

export const GET_RECORD_DETAILS = gql`
  query GetRecordDetails($layerId: Int!, $orgId: Int!, $pk: Int!) {
    records(layerId: $layerId, orgId: $orgId, pk: $pk) {
      count
      data {
        id
        data
        mapData
        sourceProperties
      }
    }
  }
`

export const GET_LAYER_RECORDS = gql`
  query GetLayerRecords(
    $layerId: Int!
    $orgId: Int!
    $limit: BoundedInt!
    $offset: BoundedInt!
    $filterGroups: [FilterGroupInput]
  ) {
    records(
      layerId: $layerId
      orgId: $orgId
      pageInfo: { limit: $limit, offset: $offset }
      filterGroups: $filterGroups
    ) {
      data {
        data
        mapData
        geometry
        sourceProperties
      }
      count
    }
  }
`
export const GET_WORKSPACE_SummaryFields = gql`
  query GetWorkspaceSummaryFields($workspaceId: Int!, $orgId: Int!, $pk: Int!) {
    layers(workspaceId: $workspaceId, orgId: $orgId, pk: $pk) {
      count
      data {
        data
        id
        sampleData
        dataset {
          created
          file
          id
          metaData
          title
        }
      }
    }
  }
`

export const GET_LAYER_MESSAGES = gql`
  query GetLayerMessages(
    $workspaceId: Int!
    $layersIds: [Int]
    $orgId: Int!
    $limit: BoundedInt!
    $offset: BoundedInt!
  ) {
    messages(
      workspaceId: $workspaceId
      layersIds: $layersIds
      orgId: $orgId
      pageInfo: { limit: $limit, offset: $offset, orderBy: "-id" }
    ) {
      data {
        id
        message {
          userInput
          finalResult
          sqlResult
        }
      }
      count
    }
  }
`
export const GET_DATASETS = gql`
  query GetDatasets($workspaceId: Int!, $orgId: Int!) {
    datasets(workspaceId: $workspaceId, orgId: $orgId) {
      data {
        id
        file
        created
        title
      }
    }
  }
`
export const GET_WORKSPACE_SORTED_LAYERS = gql`
  query GetWorkspaceSortedLayers($pk: Int!, $orgId: Int!) {
    workspaces(pk: $pk, orgId: $orgId) {
      data {
        layersSortedIds
      }
    }
  }
`

export const GET_DESIGN_LAYERS = gql`
  query getLayers($workspaceId: Int!, $orgId: Int!) {
    layers(workspaceId: $workspaceId, orgId: $orgId) {
      count
      data {
        filters
        dataFields
        recordsCount
        boundaries
        id
        jsonSchema
        layerKey
        readOnly
        locationFieldMapping
        title
        webUiJsonSchema
        description
        status
        dataset {
          id
          workspace {
            workspaceType
          }
        }
        slds {
          id
          sldType
          title
          featureStyle
        }
        summaryFields
      }
    }
  }
`
export const GET_EDA_REPORTS = gql`
  query EDAReports(
    $layerId: Int!
    $orgId: Int!
    $workspaceId: Int!
    $filters: [DjangoFilterInput]
    $pageInfo: PageInfo
  ) {
    edaReports(
      layerId: $layerId
      orgId: $orgId
      workspaceId: $workspaceId
      filters: $filters
      pageInfo: $pageInfo
    ) {
      count
      data {
        id
        file
        source
        created
      }
    }
  }
`

export const GET_FIELD_VALUES = gql`
  query GetFieldValues(
    $layerId: Int!
    $orgId: Int!
    $fieldName: String!
    $searchTerm: String!
    $limit: BoundedInt!
    $offset: BoundedInt!
  ) {
    records(
      layerId: $layerId
      orgId: $orgId
      pageInfo: { limit: $limit, offset: $offset }
      filters: { field: $fieldName, value: $searchTerm, clause: icontains }
    ) {
      data {
        data
      }
      count
    }
  }
`
