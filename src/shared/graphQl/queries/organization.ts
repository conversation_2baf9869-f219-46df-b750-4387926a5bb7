import { gql } from '@apollo/client'

export const GET_PERMISSIONS_BY_ORG_ID = gql`
  query GetPermissionsByOrgId($orgId: Int!) {
    permissions(orgId: $orgId) {
      model
      permissions {
        id
        name
        codename
      }
    }
  }
`
export const GET_WORKSPACE_PERMISSIONS = gql`
  query GetWorkspacePermissions($orgId: Int!) {
    workspacePermissions(orgId: $orgId) {
      id
      title
    }
  }
`

export const GET_ROLES = gql`
  query GetRoles($orgId: Int!) {
    roles(orgId: $orgId) {
      count
      data {
        id
        title
        codename
        usersCount
        permissions {
          id
          codename
          name
        }
      }
    }
  }
`

export const GET_USERS_QUERY = gql`
  query GetUsers(
    $orgId: Int!
    $limit: BoundedInt!
    $offset: BoundedInt!
    $filters: [DjangoFilterInput]!
  ) {
    users(
      orgId: $orgId
      pageInfo: { limit: $limit, offset: $offset }
      filters: $filters
    ) {
      data {
        activeStatus
        email
        firstName
        lastName
        id
        role {
          title
          permissions {
            id
            name
          }
          usersCount
          id
        }
      }
      count
    }
  }
`
export const GET_ALL_USERS = gql`
  query GetUsers($orgId: Int!) {
    users(orgId: $orgId) {
      data {
        activeStatus
        email
        firstName
        lastName
        id
        role {
          title
          permissions {
            id
            name
          }
          usersCount
          id
        }
      }
      count
    }
  }
`
export const WORKSPACE_USERS_QUERY = gql`
  query WorkspaceUsers(
    $orgId: Int!
    $workspaceId: Int!
    $excludeIndividuals: Boolean
  ) {
    users(
      orgId: $orgId
      workspaceId: $workspaceId
      excludeIndividuals: $excludeIndividuals
    ) {
      count
      data {
        id
        activeStatus
        firstName
        lastName
        email
        workspacePermissions {
          id
          title
          codename
        }
        role {
          id
          title
        }
      }
    }
  }
`
