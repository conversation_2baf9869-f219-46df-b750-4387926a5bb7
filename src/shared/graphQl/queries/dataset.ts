import { gql } from '@apollo/client'

export const FETCH_DATASET_REQUESTS = gql`
  query fetchDatasetRequests($orgId: Int!) {
    workspaceRequests(orgId: $orgId) {
      count
      data {
        requestType
        currentStep
        id
        layerData
        status
        dataset {
          created
          file
          id
          metaData
          workspace {
            id
          }
        }
      }
    }
  }
`

export const FETCH_DETAILED_DATASET_REQUEST = gql`
  query fetchDetailsDataset($pk: Int!, $orgId: Int!) {
    workspaceRequests(pk: $pk, orgId: $orgId) {
      count
      data {
        requestType
        id
        status
        layerData
        currentStep
        dataset {
          id
          metaData
          file
        }
      }
    }
  }
`

export const GET_DATASET_COLUMNS = gql`
  query getDatasetColumns($datasetRequestId: Int!, $orgId: Int!) {
    workspaceRequests(pk: $datasetRequestId, orgId: $orgId) {
      data {
        requestType
        dataset {
          metaData
        }
      }
    }
  }
`

export const GET_DATASET_SAMPLES = gql`
  query getDatasetSamples($datasetRequestId: Int!, $orgId: Int!) {
    datasetSampleData(datasetRequestId: $datasetRequestId, orgId: $orgId) {
      data {
        column
        data
        title
      }
    }
  }
`
export const GET_JSON_SCHEMAS = gql`
  query getJsonSchemas($datasetRequestId: Int!, $orgId: Int!) {
    jsonSchemas(datasetRequestId: $datasetRequestId, orgId: $orgId) {
      formData
      jsonSchema
    }
  }
`

export const CHECK_WORKSPACE_ID_EXISTENCE = gql`
  query checkWorkspaceExistence($orgId: Int!) {
    workspaceRequests(
      orgId: $orgId
      filters: { field: "request_type", value: "UPLOAD_FILE", clause: iexact }
    ) {
      data {
        dataset {
          workspace {
            id
          }
        }
      }
    }
  }
`
