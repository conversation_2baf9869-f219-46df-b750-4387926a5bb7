import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import "./i18n";
import { RouterProvider } from "react-router-dom";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "@/shared/store";
import { ApolloProvider } from "@apollo/client";
import { graphQlAppClient } from "./shared/graphQl";
import { router } from "./shared/Routing";
import { initClarity } from '../clarity';

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ApolloProvider client={graphQlAppClient}>
      <ReduxProvider store={store}>
        <RouterProvider router={router} />
      </ReduxProvider>
    </ApolloProvider>
  </React.StrictMode>
);


// TODO: change required dsn and id for sentry 
// initialize sentry
// initSentryForProduction();
// initialize clarity
initClarity();