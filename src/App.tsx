import { useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import useLocalizeDocumentAttributes from "./shared/hooks/useLocalize";
import { ToastContainer } from 'react-toastify';
import { showSplashScreen, hideSplashScreen } from "./shared/utils/splashScreen";
import 'react-toastify/dist/ReactToastify.css';
import 'ol/ol.css';
import "./App.css";
import { AuthProvider } from "./shared/auth";

export const App = () => {
  useLocalizeDocumentAttributes();
  const location = useLocation();

  useEffect(() => {
    if (location.pathname === '/' || location.pathname === '/login') {
      const splash = showSplashScreen();
      setTimeout(() => {
        hideSplashScreen(splash);
      }, 5230);
    }
  }, [location.pathname]);

  return (
    <AuthProvider>
      <ToastContainer />
      <Outlet />
    </AuthProvider>)
};
