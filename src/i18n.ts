import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import translation JSON files
import translationEN from '@/locales/en/translation.json'
import translationAR from '@/locales/ar/translation.json'

const resources = {
  en: {
    translation: translationEN,
  },
  ar: {
    translation: translationAR,
  },
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: 'ar',
    resources,
    fallbackLng: 'ar',
    interpolation: {
      escapeValue: false,
      prefix: '${',
      suffix: '}',
    },
  })

export default i18n
