/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'], // Enables dark mode via class toggle
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      // Font Family
      fontFamily: {
        sans: ['IBM Plex Sans Arabic', 'sans-serif'],
        jakarta: ['Plus Jakarta Sans', 'sans-serif'],
      },

      // Font Sizes and Line Heights for Body Text and Headings
      fontSize: {
        // Body Text
        body: ['16px', { lineHeight: '24px' }],

        // Headings
        h1: ['48px', { lineHeight: '56px' }],
        h2: ['40px', { lineHeight: '46px' }],
        h3: ['32px', { lineHeight: '36px' }],
        h4: ['28px', { lineHeight: '32px' }],
        h5: ['24px', { lineHeight: '28px' }],
        h6: ['20px', { lineHeight: '24px' }],
      },

      // Font Weight
      fontWeight: {
        bold: 700,
      },

      // Border Radius
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },

      // Colors
      colors: {
        // Theme colors based on CSS variables
        'custom-bg': '#2C2C2C',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',

        // Custom Color Palettes
        bluePalette: {
          100: '#EFEFFE', // Very Light Lavender
          200: '#CFCDFA', // Light Lavender
          300: '#AEABF6', // Pale Lavender
          400: '#8682F2', // Lavender
          500: '#5E58EE', // Light Blue
          600: '#4642B2', // Blue
          700: '#2F2C77', // Royal Blue
          800: '#131230', // Dark Blue
        },
        grayPalette: {
          100: '#F0F0F5', // Very Light Gray
          200: '#DCDCE0', // Silver
          300: '#C8C8CC', // Light Silver
          400: '#AAAAAD', // Lighter Gray
          500: '#86858C', // Light Gray
          600: '#6D6D73', // Gray
          700: '#49494D', // Dark Gray
          800: '#29292B', // Charcoal
        },

        // Chart Colors
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
    },
  },

  // Plugins
  plugins: [
    require('tailwindcss-animate'),
    require('tailwindcss-rtl'),
    function ({ addUtilities }) {
      addUtilities({
        '.no-border-focus': {
          'focus:ring': '0',
          'focus:ring-offset': '0',
          'focus-visible:ring': '0',
          'focus-visible:ring-offset': '0',
        },
      })
    },
  ],
}
